import { defineConfig } from 'vite';
import eslint from 'vite-plugin-eslint';

export default defineConfig(() => ({
    plugins: [eslint()],
    publicDir: '',
    build: {
        outDir: 'public',
        copyPublicDir: false,
        emptyOutDir: false,
        rollupOptions: {
            input: 'resources/js/embed-form.ts',
            output: {
                entryFileNames: 'embed-form.js',
            },
        },
    },
}));
