<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.5/phpunit.xsd" bootstrap="vendor/autoload.php" colors="true" cacheDirectory=".phpunit.cache" stopOnFailure="true" stopOnError="true">
	<testsuites>
		<testsuite name="Unit">
			<directory suffix="Test.php">
				./tests/Unit
			</directory>
		</testsuite>
		<testsuite name="Feature">
			<directory suffix="Test.php">
				./tests/Feature
			</directory>
		</testsuite>
		<testsuite name="MultiTenantUnit">
			<directory suffix="Test.php">
				./tests/MultiTenantSyncing/Unit
			</directory>
		</testsuite>
		<testsuite name="MultiTenantFeature">
			<directory suffix="Test.php">
				./tests/MultiTenantSyncing/Feature
			</directory>
		</testsuite>
	</testsuites>
	<coverage />
	<php>
		<env name="APP_ENV" value="testing" />
		<env name="BCRYPT_ROUNDS" value="4" />
		<env name="MAIL_MAILER" value="array" />
		<env name="QUEUE_CONNECTION" value="sync" />
		<env name="SESSION_DRIVER" value="array" />
		<env name="TELESCOPE_ENABLED" value="false" />
	</php>
	<source>
		<include>
			<directory suffix=".php">
				./app
			</directory>
		</include>
	</source>
	<logging>
		<junit outputFile="phpunit-results-junit.xml" />
	</logging>
</phpunit>
