import plugin from 'tailwindcss/plugin';
import tailwindAnimate from 'tailwindcss-animate';

import forms from '@tailwindcss/forms';
import typographyPlugin from '@tailwindcss/typography';

import { getTailwindColors } from './resources/js/config/tailwind-colors';

const radialGradientPlugin = plugin(
    ({ matchUtilities, theme }) => {
        matchUtilities(
            {
                // map to bg-radient-[*]
                'bg-radient': value => ({
                    'background-image': `radial-gradient(${value},var(--tw-gradient-stops))`,
                }),
            },
            { values: theme('radialGradients') }
        );
    },
    {
        theme: {
            radialGradients: presets(),
        },
    }
);

/** @type {import('tailwindcss').Config} */
export default {
    darkMode: ['class'],
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/js/**/*.{js,ts,jsx,tsx}',
    ],

    theme: {
        container: {
            center: 'true',
            padding: '2rem',
            screens: {
                '2xl': '1400px',
            },
        },
        extend: {
            colors: {
                ...getTailwindColors(),
                sidebar: {
                    DEFAULT: 'hsl(var(--sidebar-background))',
                    foreground: 'hsl(var(--sidebar-foreground))',
                    primary: 'hsl(var(--sidebar-primary))',
                    'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
                    accent: 'hsl(var(--sidebar-accent))',
                    'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
                    'active-foreground': 'hsl(var(--sidebar-active-foreground))',
                    border: 'hsl(var(--sidebar-border))',
                    ring: 'hsl(var(--sidebar-ring))',
                },
            },
            fontFamily: {
                sans: ['Inter'],
                body: ['Inter'],
            },
            fontSize: {
                '2xs': '0.75rem',
                xs: '0.8125rem',
                sm15: '0.9375rem',
            },
            transitionProperty: {
                width: 'width',
                height: 'height',
            },
            keyframes: {
                'fade-in': {
                    '0%': {
                        opacity: '0',
                    },
                    '100%': {
                        opacity: '1',
                    },
                },
                'fade-out': {
                    '100%': {
                        opacity: '1',
                    },
                    '0%': {
                        opacity: '0',
                    },
                },
                'slide-down': {
                    '0%': {
                        transform: 'translateY(100%)',
                        opacity: '0.1',
                    },
                    '15%': {
                        transform: 'translateY(0)',
                        opacity: '1',
                    },
                    '30%': {
                        transform: 'translateY(0)',
                        opacity: '1',
                    },
                    '45%': {
                        transform: 'translateY(-100%)',
                        opacity: '1',
                    },
                    '100%': {
                        transform: 'translateY(-100%)',
                        opacity: '0.1',
                    },
                },
                'slide-up': {
                    '100%': {
                        transform: 'translateY(-100%)',
                        opacity: '0.1',
                    },
                    '45%': {
                        transform: 'translateY(-100%)',
                        opacity: '1',
                    },
                    '30%': {
                        transform: 'translateY(0)',
                        opacity: '1',
                    },
                    '15%': {
                        transform: 'translateY(0)',
                        opacity: '1',
                    },
                    '0%': {
                        transform: 'translateY(100%)',
                        opacity: '0.1',
                    },
                },
                'accordion-down': {
                    from: {
                        height: '0',
                    },
                    to: {
                        height: 'var(--radix-accordion-content-height)',
                    },
                },
                'accordion-up': {
                    from: {
                        height: 'var(--radix-accordion-content-height)',
                    },
                    to: {
                        height: '0',
                    },
                },
                'collapsible-down': {
                    from: {
                        height: '0',
                    },
                    to: {
                        height: 'var(--radix-collapsible-content-height)',
                    },
                },
                'collapsible-up': {
                    from: {
                        height: 'var(--radix-collapsible-content-height)',
                    },
                    to: {
                        height: '0',
                    },
                },
            },
            animation: {
                'accordion-down': 'accordion-down 0.2s ease-out',
                'accordion-up': 'accordion-up 0.2s ease-out',
                'collapsible-down': 'collapsible-down var(--animation-duration, 300ms) ease-out',
                'collapsible-up': 'collapsible-up var(--animation-duration, 300ms) ease-out',
                'spin-slow': 'spin 3s linear infinite',
                'fade-in': 'fade-in 0.5s ease-in-out',
                'slide-down': 'slide-down 1s ease-in-out 0.25s 1',
                'slide-up': 'slide-up 1s ease-in-out 0.25s 1',
            },
            height: {
                4.5: '1.125rem',
            },
            width: {
                4.5: '1.125rem',
            },
            zIndex: {
                1: '1',
                2: '2',
                3: '3',
                4: '4',
                5: '5',
                6: '6',
                7: '7',
                8: '8',
                9: '9',
                11: '11',
                12: '12',
                13: '13',
                14: '14',
                15: '15',
                16: '16',
                17: '17',
                18: '18',
                19: '19',
            },
        },
    },

    plugins: [forms({ strategy: 'class' }), tailwindAnimate, typographyPlugin, radialGradientPlugin],
};

/**
 * utility class presets
 */
function presets() {
    const shapes = ['circle', 'ellipse'];
    const pos = {
        c: 'center',
        t: 'top',
        b: 'bottom',
        l: 'left',
        r: 'right',
        tl: 'top left',
        tr: 'top right',
        bl: 'bottom left',
        br: 'bottom right',
    };
    return shapes.reduce((acc, shape) => {
        Object.entries(pos).forEach(([posName, posValue]) => {
            acc[`${shape}-${posName}`] = `${shape} at ${posValue}`;
        });
        return acc;
    }, {});
}
