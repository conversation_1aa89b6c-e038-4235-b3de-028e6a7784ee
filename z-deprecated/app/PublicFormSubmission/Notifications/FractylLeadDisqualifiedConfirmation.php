<?php

namespace App\PublicFormSubmission\Notifications;

use App\Models\CampaignForm;
use App\Notifications\Concerns\PostmarkHeaderTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\HtmlString;
use Symfony\Component\Mime\Email;

class FractylLeadDisqualifiedConfirmation extends Notification implements ShouldQueue
{
    use PostmarkHeaderTrait;
    use Queueable;

    public function __construct(public string $leadName, public CampaignForm $campaignForm)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->from($this->campaignForm->sender_email_address, $this->campaignForm->sender_email_name)
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $this->attachPostmarkHeaders($message, $notifiable);
            })
            ->subject('Thank you for your interest')
            ->greeting(Lang::get('Hello :name,', ['name' => $this->leadName]))
            ->line('Based upon the information you have provided, it appears that you do not currently qualify for the Revitalize 1 Study.')
            ->line('Thank you for your interest in the Revitalize 1 Study.')
            ->line(
                new HtmlString(
                    "You can stay informed of future studies you may qualify for by visiting this <a href='https://www.fractyl.com/contact-form'>website</a>.",
                ),
            )
            ->line('{{{ pm:unsubscribe }}}');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
