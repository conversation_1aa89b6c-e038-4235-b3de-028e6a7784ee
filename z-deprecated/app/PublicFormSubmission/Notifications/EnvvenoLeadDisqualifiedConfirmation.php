<?php

namespace App\PublicFormSubmission\Notifications;

use App\Models\CampaignForm;
use App\Notifications\Concerns\PostmarkHeaderTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\Mime\Email;

class EnvvenoLeadDisqualifiedConfirmation extends Notification implements ShouldQueue
{
    use PostmarkHeaderTrait;
    use Queueable;

    public $leadName = '';

    public CampaignForm $campaignForm;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($leadName, CampaignForm $campaignForm)
    {
        $this->leadName     = $leadName;
        $this->campaignForm = $campaignForm;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toMail($notifiable): MailMessage
    {
        $body = 'Thank you for your interest in the VenoValve clinical trial. It appears from your answers to the pre-screening questions ' .
            'that you are not a candidate for the study at this time. The VenoValve clinical trial has very specific subject criteria for ' .
            'who can and cannot participate in the study, and only limited subject slots are available. Even if you are not a candidate ' .
            'to participate in the VenoValve clinical trial, you may still be a candidate for the VenoValve if the device receives ' .
            'FDA approval. You can elect to receive updates on the VenoValve by visiting https://venovalve.com/ and have your name added ' .
            'to a list of prioritized potential subjects. If the VenoValve receives FDA approval, we will contact you to direct you ' .
            'to a healthcare provider in your area.';

        return (new MailMessage)
            ->from($this->campaignForm->sender_email_address, $this->campaignForm->sender_email_name)
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $this->attachPostmarkHeaders($message, $notifiable);
            })
            ->subject('Thank you for your interest')
            ->greeting(Lang::get('Hello :name,', ['name' => $this->leadName]))
            ->line($body)
            ->line('{{{ pm:unsubscribe }}}');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
