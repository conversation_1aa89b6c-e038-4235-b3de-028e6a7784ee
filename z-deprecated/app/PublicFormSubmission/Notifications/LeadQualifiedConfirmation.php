<?php

namespace App\PublicFormSubmission\Notifications;

use App\Models\CampaignForm;
use App\Notifications\Concerns\PostmarkHeaderTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\Mime\Email;

class LeadQualifiedConfirmation extends Notification implements ShouldQueue
{
    use PostmarkHeaderTrait;
    use Queueable;

    public $leadName = '';

    public CampaignForm $campaignForm;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($leadName, CampaignForm $campaignForm)
    {
        $this->leadName     = $leadName;
        $this->campaignForm = $campaignForm;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->from($this->campaignForm->sender_email_address, $this->campaignForm->sender_email_name)
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $this->attachPostmarkHeaders($message, $notifiable);
            })
            ->subject('Congrats on qualifying to join this study')
            ->greeting(Lang::get('Hello :name,', ['name' => $this->leadName]))
            ->line('Based on your answers it looks like you might be a good fit for the study!')
            ->line('Someone from the scheduling team will be reviewing your response and may be reaching out soon with your next steps.')
            ->line("Please - don't filter any calls in the next few days, we don't want you to lose your spot on this study.")
            ->line('Thanks for your participation')
            ->line('{{{ pm:unsubscribe }}}');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
