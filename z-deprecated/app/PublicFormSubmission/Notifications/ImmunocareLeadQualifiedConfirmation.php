<?php

namespace App\PublicFormSubmission\Notifications;

use App\Models\CampaignForm;
use App\Notifications\Concerns\PostmarkHeaderTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\HtmlString;
use Symfony\Component\Mime\Email;

class ImmunocareLeadQualifiedConfirmation extends Notification implements ShouldQueue
{
    use PostmarkHeaderTrait;
    use Queueable;

    public string $leadName;

    public CampaignForm $campaignForm;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $leadName, CampaignForm $campaignForm)
    {
        $this->leadName     = $leadName;
        $this->campaignForm = $campaignForm;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->from($this->campaignForm->sender_email_address, $this->campaignForm->sender_email_name)
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $this->attachPostmarkHeaders($message, $notifiable);
            })
            ->subject('Congrats! You’re Pre-Qualified for this study!')
            ->greeting(Lang::get('Hello :name,', ['name' => $this->leadName]))
            ->line('Thank you for applying for the ImmunoCARE COVID-19 Study. You have met the basic pre-qualification criteria and may continue on to the next step. If you haven’t already, please follow the link below to do some additional screening questionnaires and create a MyDataHelps account. ')
            ->line(
                new HtmlString("Here is the link: <a href='https://mydatahelps.org/e/HWJCKT/Screener?utm_source=1nH&utm_medium=email'>https://mydatahelps.org/e/HWJCKT/Screener?utm_source=1nH&utm_medium=email</a>"),
            )
            ->line('If you have already completed this next step, feel free to disregard this email.')
            ->line('Have a wonderful day.')
            ->line('{{{ pm:unsubscribe }}}');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
