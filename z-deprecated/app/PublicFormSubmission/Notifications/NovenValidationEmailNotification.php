<?php

namespace App\PublicFormSubmission\Notifications;

use App\Enums\NovenGroupEnum;
use App\Models\CampaignForm;
use App\Notifications\Concerns\PostmarkHeaderTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\HtmlString;
use Symfony\Component\Mime\Email;

class NovenValidationEmailNotification extends Notification implements ShouldQueue
{
    use PostmarkHeaderTrait;
    use Queueable;

    public function __construct(
        public string $referralName,
        public NovenGroupEnum $group,
        public CampaignForm $campaignForm,
    ) {
        $this->onQueue('castor-integration');
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(mixed $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(mixed $notifiable): MailMessage
    {
        $emailBody = Lang::get('noven.qualified-email')[$this->group->value];

        return (new MailMessage)
            ->from($this->campaignForm->sender_email_address, $this->campaignForm->sender_email_name)
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $this->attachPostmarkHeaders($message, $notifiable);
            })
            ->subject("You're Pre-Qualified!")
            ->greeting("Hi {$this->referralName},")
            ->line($emailBody)
            ->salutation(new HtmlString('Warm Regards,<br> The App Study Team'))
            ->line('{{{ pm:unsubscribe }}}');
    }
}
