<?php

namespace App\PublicFormSubmission\Notifications;

use App\Models\CampaignForm;
use App\Notifications\Concerns\PostmarkHeaderTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\Mime\Email;

class LeadSiteNotification extends Notification implements ShouldQueue
{
    use PostmarkHeaderTrait;
    use Queueable;

    public $toName = '';

    public $studyName = '';

    public $qualificationBucket = '';

    public CampaignForm $campaignForm;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($toName, $studyName, $qualificationBucket, CampaignForm $campaignForm)
    {
        $this->toName              = $toName;
        $this->studyName           = $studyName;
        $this->qualificationBucket = $qualificationBucket;
        $this->campaignForm        = $campaignForm;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->from($this->campaignForm->sender_email_address, $this->campaignForm->sender_email_name)
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $this->attachPostmarkHeaders($message, $notifiable);
            })
            ->subject('A new pre-qualified lead has arrived')
            ->greeting(Lang::get('Hello :name,', ['name' => $this->toName]))
            ->line(Lang::get('You have a new qualified lead for :studyName with qualification of :qualificationBucket', [
                'studyName'           => $this->studyName,
                'qualificationBucket' => $this->qualificationBucket,
            ]));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
