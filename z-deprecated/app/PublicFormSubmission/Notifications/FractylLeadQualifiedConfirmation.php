<?php

namespace App\PublicFormSubmission\Notifications;

use App\Models\CampaignForm;
use App\Notifications\Concerns\PostmarkHeaderTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\Mime\Email;

class FractylLeadQualifiedConfirmation extends Notification implements ShouldQueue
{
    use PostmarkHeaderTrait;
    use Queueable;

    public function __construct(public string $leadName, public CampaignForm $campaignForm)
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->from($this->campaignForm->sender_email_address, $this->campaignForm->sender_email_name)
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $this->attachPostmarkHeaders($message, $notifiable);
            })
            ->subject('Congrats on pre-qualifying to join this study')
            ->greeting(Lang::get('Hello :name,', ['name' => $this->leadName]))
            ->line('Excellent new! Based on the answers you provided, you have pre-qualified.')
            ->line(
                'A staff member from the study center will reach out within the next few days to ' .
                'discuss this study further and schedule an appointment, if appropriate. Please rest ' .
                'assured that all contact efforts are handled discreetly and confidentiality is always ' .
                'strictly protected, unless permission is provided or except as required by law.',
            )
            ->line(
                'Submitting this information is not a commitment to participate in this study, but ' .
                'rather simply exploring an opportunity to join the study if qualified. We may also ' .
                'forward the contact information you provided to a different study center in the area ' .
                'if the original study center is unavailable.',
            )
            ->line('The information has been sent to the study team. Please expect a call to the telephone number you entered.')
            ->line('Thank you for your interest in the Revitalize 1 Study.')
            ->line('{{{ pm:unsubscribe }}}');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
