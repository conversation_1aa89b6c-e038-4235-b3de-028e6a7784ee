<?php

namespace App\PublicFormSubmission\Notifications;

use App\Models\CampaignForm;
use App\Notifications\Concerns\PostmarkHeaderTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\Mime\Email;

class MeiraGTxLeadQualifiedConfirmation extends Notification implements ShouldQueue
{
    use PostmarkHeaderTrait;
    use Queueable;

    public $leadName = '';

    public CampaignForm $campaignForm;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($leadName, CampaignForm $campaignForm)
    {
        $this->leadName     = $leadName;
        $this->campaignForm = $campaignForm;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toMail($notifiable): MailMessage
    {
        $body = 'Thanks for taking the time to apply for the Xerostomia (Dry Mouth) Study.' .
            'Based on your answers, it looks like you pre-qualified and may now move on to the next screening steps for the study.' .
            '<br/>Please call Katherine at your earliest convenience at ************ or email <NAME_EMAIL>.' .
            'When you call be sure to give your name and phone number, and to mention that your call is regarding the Dry Mouth Study.' .
            'Let us know if you have any questions.';

        return (new MailMessage)
            ->from($this->campaignForm->sender_email_address, $this->campaignForm->sender_email_name)
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $this->attachPostmarkHeaders($message, $notifiable);
            })
            ->subject('Congrats on qualifying to join this study')
            ->greeting(Lang::get('Hello :name,', ['name' => $this->leadName]))
            ->line($body)
            ->line('{{{ pm:unsubscribe }}}')
            ->salutation('Have a great day');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
