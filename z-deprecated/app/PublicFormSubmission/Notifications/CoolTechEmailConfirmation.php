<?php

namespace App\PublicFormSubmission\Notifications;

use App\Models\CampaignForm;
use App\Notifications\Concerns\PostmarkHeaderTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\HtmlString;
use Symfony\Component\Mime\Email;

class CoolTechEmailConfirmation extends Notification implements ShouldQueue
{
    use PostmarkHeaderTrait;
    use Queueable;

    public function __construct(
        public string $leadName,
        public string $lang,
        public CampaignForm $campaignForm,
    ) {
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(mixed $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(mixed $notifiable): MailMessage
    {
        return (new MailMessage)
            ->from($this->campaignForm->sender_email_address, $this->campaignForm->sender_email_name)
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $this->attachPostmarkHeaders($message, $notifiable);
            })
            ->subject('Congrats on qualifying to join this study')
            ->greeting('Dear participant,')
            ->line(Lang::get('obvio.cooltech.email.greeting', locale: $this->lang))
            ->line(Lang::get('obvio.cooltech.email.first_paragraph', locale: $this->lang))
            ->line(
                new HtmlString(Lang::get('obvio.cooltech.email.second_paragraph', locale: $this->lang) . ' <a href="https://bit.ly/clicknextstreps">https://bit.ly/clicknextstreps</a>'),
            )
            ->line('{{{ pm:unsubscribe }}}');
    }
}
