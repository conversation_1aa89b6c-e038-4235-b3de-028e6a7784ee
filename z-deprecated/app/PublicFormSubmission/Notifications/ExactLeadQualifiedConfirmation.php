<?php

namespace App\PublicFormSubmission\Notifications;

use App\Models\CampaignForm;
use App\Notifications\Concerns\PostmarkHeaderTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Mime\Email;

class ExactLeadQualifiedConfirmation extends Notification implements ShouldQueue
{
    use PostmarkHeaderTrait;
    use Queueable;

    public $leadName = '';

    public $requestData = [];

    public CampaignForm $campaignForm;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($leadName, $requestData, CampaignForm $campaignForm)
    {
        $this->leadName     = $leadName;
        $this->requestData  = $requestData;
        $this->campaignForm = $campaignForm;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toMail($notifiable): MailMessage
    {
        $body = Lang::get('Thank you for your interest in the BLUE-C colonoscopy study! Based on the information you provided,
            you have been determined to be eligible for participation! Please use the following link to access Health Storylines,
            the application you will use to complete your journey through the study milestones. If you have any questions,
            please reach out to our study team at ************** <NAME_EMAIL>.');
        $mobileUrl = 'https://healthstorylines.page.link/BLUEC';
        $webUrl    = 'https://crcscreeningstudy.healthstorylines.com/app/#/register?group=574';

        Log::debug('requestData', ['requestData' => $this->requestData]);

        if (! empty($this->requestData['leadTracking'])) {
            $leadTracking = $this->requestData['leadTracking'];

            if (! empty($leadTracking->utm)) {
                $utms     = [];
                $origUtms = $leadTracking->utm;

                foreach ($origUtms as $key => $utm) {
                    $utms["utm_{$key}"] = $utm;
                }

                $utms = http_build_query($utms);
                Log::debug('utms string', ['utms' => $utms]);

                $apiUrl   = 'https://my.healthstorylines.com/api/companies/123/get_campaign_info?group_id=574';
                $response = Http::get("{$apiUrl}&{$utms}");

                if ($response->successful()) {
                    $mobileUrl = $response->object()->shortened_url;
                } else {
                    $mobileUrl .= "?{$utms}";
                }
            }
        }

        return (new MailMessage)
            ->from($this->campaignForm->sender_email_address, $this->campaignForm->sender_email_name)
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $this->attachPostmarkHeaders($message, $notifiable);
            })
            ->subject('Congrats on qualifying for the colonoscopy study')
            ->greeting(Lang::get('Hello :name,', ['name' => $this->leadName]))
            ->line($body)
            ->line("On Mobile: {$mobileUrl}")
            ->line("On the Web: {$webUrl}")
            ->line('{{{ pm:unsubscribe }}}');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
