<?php

namespace App\PublicFormSubmission\Notifications;

use App\Models\CampaignForm;
use App\Notifications\Concerns\PostmarkHeaderTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\HtmlString;
use Symfony\Component\Mime\Email;

class ClickLeadQualifiedConfirmation extends Notification implements ShouldQueue
{
    use PostmarkHeaderTrait;
    use Queueable;

    public string $leadName;

    public CampaignForm $campaignForm;

    /**
     * @return void
     */
    public function __construct(string $leadName, CampaignForm $campaignForm)
    {
        $this->leadName     = $leadName;
        $this->campaignForm = $campaignForm;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->from($this->campaignForm->sender_email_address, $this->campaignForm->sender_email_name)
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $this->attachPostmarkHeaders($message, $notifiable);
            })
            ->subject('Congrats! You’re Pre-Qualified for this Migraine Study!')
            ->greeting('Form Complete!')
            ->line('Thanks for taking the time to complete the questionnaire. You’ll receive a second email invite from the ObvioGo team ' .
                'in the coming days (don’t forget to check your spam/junk folder). The email invite will have a link to set up your ' .
                'ObvioGo password and download the ObvioGo App where you will complete the rest of the screening process. Have a great day!',
            )
            ->line(
                new HtmlString("Want to learn more about Click Therapeutics and keep up with the latest news? <a href='https://www.clicktherapeutics.com/'>Click here</a>"),
            )
            ->line(
                new HtmlString("To learn more about ObvioHealth and their mission to deliver stronger evidence for better health outcomes. <a href='https://www.obviohealth.com/'>Click here</a>"),
            )
            ->line('{{{ pm:unsubscribe }}}');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toArray($notifiable): array
    {
        return [];
    }
}
