<?php

namespace App\PublicFormSubmission\Notifications;

use App\Models\CampaignForm;
use App\Notifications\Concerns\PostmarkHeaderTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\Mime\Email;

class CoolTech2Email extends Notification implements ShouldQueue
{
    use PostmarkHeaderTrait;
    use Queueable;

    public function __construct(
        public string $leadName,
        public string $lang,
        public CampaignForm $campaignForm,
    ) {
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(mixed $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(mixed $notifiable): MailMessage
    {
        return (new MailMessage)
            ->from($this->campaignForm->sender_email_address, $this->campaignForm->sender_email_name)
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $this->attachPostmarkHeaders($message, $notifiable);
            })
            ->subject('Congrats on qualifying to join this study')
            ->greeting('Dear participant,')
            ->line(Lang::get('obvio.cooltech2.email.greeting', locale: $this->lang))
            ->line(Lang::get('obvio.cooltech2.email.first_paragraph', locale: $this->lang))
            ->line(Lang::get('obvio.cooltech2.email.second_paragraph', locale: $this->lang))
            ->line(Lang::get('obvio.cooltech2.email.third_paragraph', locale: $this->lang))
            ->line('{{{ pm:unsubscribe }}}');
    }
}
