<?php

namespace App\PublicFormSubmission\Notifications;

use App\Models\CampaignForm;
use App\Notifications\Concerns\PostmarkHeaderTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\Mime\Email;

class ColgatePediatricLeadDisqualifiedConfirmation extends Notification implements ShouldQueue
{
    use PostmarkHeaderTrait;
    use Queueable;

    public $leadName = '';

    public $lang;

    public CampaignForm $campaignForm;

    /**
     * @return void
     */
    public function __construct(string $leadName, string $lang, CampaignForm $campaignForm)
    {
        $this->leadName     = $leadName;
        $this->lang         = $lang;
        $this->campaignForm = $campaignForm;
    }

    /**
     * @param mixed $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * @param mixed $notifiable
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->from($this->campaignForm->sender_email_address, $this->campaignForm->sender_email_name)
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $this->attachPostmarkHeaders($message, $notifiable);
            })
            ->subject(Lang::get('colgate.pediatrics_disqualified_email.subject', [], $this->lang))
            ->greeting(Lang::get('colgate.pediatrics_disqualified_email.greeting', ['name' => $this->leadName], $this->lang))
            ->line(Lang::get('colgate.pediatrics_disqualified_email.body', [], $this->lang))
            ->line('{{{ pm:unsubscribe }}}')
            ->salutation(Lang::get('colgate.pediatrics_disqualified_email.salutation', [], $this->lang));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
