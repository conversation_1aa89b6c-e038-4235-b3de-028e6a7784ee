<?php

namespace App\PublicFormSubmission\Notifications;

use App\Models\CampaignForm;
use App\Notifications\Concerns\PostmarkHeaderTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;
use Symfony\Component\Mime\Email;

class ColgatePediatricLeadQualifiedConfirmation extends Notification implements ShouldQueue
{
    use PostmarkHeaderTrait;
    use Queueable;

    /**
     * @var string
     */
    public $leadName = '';

    /**
     * @var string
     */
    public $lang = '';

    public CampaignForm $campaignForm;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($leadName, $lang, CampaignForm $campaignForm)
    {
        $this->leadName     = $leadName;
        $this->lang         = $lang;
        $this->campaignForm = $campaignForm;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->from($this->campaignForm->sender_email_address, $this->campaignForm->sender_email_name)
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $this->attachPostmarkHeaders($message, $notifiable);
            })
            ->subject(Lang::get('colgate.pediatrics_qualified_email.subject', [], $this->lang))
            ->greeting(Lang::get('colgate.pediatrics_qualified_email.greeting', ['name' => $this->leadName], $this->lang))
            ->line(Lang::get('colgate.pediatrics_qualified_email.body', [], $this->lang))
            ->line('{{{ pm:unsubscribe }}}')
            ->salutation(Lang::get('colgate.pediatrics_qualified_email.salutation', [], $this->lang));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toArray($notifiable): array
    {
        return [
            //
        ];
    }
}
