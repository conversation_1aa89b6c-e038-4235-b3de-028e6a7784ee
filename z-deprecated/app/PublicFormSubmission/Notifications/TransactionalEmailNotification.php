<?php

namespace App\PublicFormSubmission\Notifications;

use App\Enums\LanguageEnum;
use App\Enums\MergeTagEnum;
use App\Models\CampaignForm;
use App\Models\CentralUser;
use App\Models\Lead;
use App\Models\TransactionalEmail;
use App\Models\User;
use App\Notifications\Concerns\PostmarkHeaderTrait;
use App\Services\MergeTagService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Symfony\Component\Mime\Email;

/**
 * class TransactionalEmailNotification
 */
class TransactionalEmailNotification extends Notification implements ShouldQueue
{
    use PostmarkHeaderTrait;
    use Queueable;

    public CampaignForm $campaignForm;

    public string $lang;

    public string $leadName;

    /**
     * @return void
     */
    public function __construct(public Lead $lead, public TransactionalEmail $transactionalEmail)
    {
        $this->lang         = $this->lead->lang ?? 'en';
        $this->leadName     = ! empty($this->lead->acc_lead_name) ? $this->lead->acc_lead_name : 'Participant';
        $this->campaignForm = $this->lead->campaignForm;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     */
    public function via($notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     */
    public function toMail($notifiable): MailMessage
    {
        [$htmlContent, $subject] = $this->getContentAndSubject($this->lang);
        $mergeTagValues          = $this->getMergeTagValues($notifiable);
        $htmlContent             = (new MergeTagService())->replaceMergeTags($htmlContent, $mergeTagValues);

        return (new MailMessage)
            ->withSymfonyMessage(function (Email $message) use ($notifiable) {
                $this->attachPostmarkHeaders($message, $notifiable);
            })
            ->view('emails.transactional', ['htmlContent' => $htmlContent])
            ->from($this->campaignForm->sender_email_address, $this->campaignForm->sender_email_name)
            ->subject($subject);
    }

    private function getContentAndSubject(string $languageCode): array
    {
        $en          = LanguageEnum::en()->value;
        $subject     = $this->transactionalEmail->subject[$languageCode] ?? $this->transactionalEmail->subject[$en];
        $htmlContent = $this->transactionalEmail->html_content[$languageCode] ?? $this->transactionalEmail->html_content[$en];

        return [$htmlContent, $subject];
    }

    /**
     * @return string[]
     */
    private function getMergeTagValues(mixed $notifiable): array
    {
        return [
            MergeTagEnum::leadName()->label                => $this->leadName,
            MergeTagEnum::leadQualificationBucket()->label => $this->lead->acc_qualification_bucket,
            MergeTagEnum::studyName()->label               => $this->campaignForm->study->study_name,
            MergeTagEnum::userName()->label                => $notifiable instanceof User || $notifiable instanceof CentralUser ? $notifiable->acc_name : 'User',
        ];
    }
}
