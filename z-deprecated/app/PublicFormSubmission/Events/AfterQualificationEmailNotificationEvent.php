<?php

namespace App\PublicFormSubmission\Events;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use Illuminate\Foundation\Events\Dispatchable;

class AfterQualificationEmailNotificationEvent
{
    use Dispatchable;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(public CampaignFormSubmissionDto $campaignFormSubmissionDto)
    {
    }
}
