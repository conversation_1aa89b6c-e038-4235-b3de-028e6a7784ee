<?php

namespace App\PublicFormSubmission\Events;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\PublicFormSubmission\Contracts\PublicFormSubmissionTransactionalEvent;
use Illuminate\Foundation\Events\Dispatchable;

class DisqualificationEmailNotificationEvent implements PublicFormSubmissionTransactionalEvent
{
    use Dispatchable;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(public CampaignFormSubmissionDto $campaignFormSubmissionDto)
    {
    }
}
