<?php

namespace App\PublicFormSubmission\Actions;

use App\Concerns\FlattenQuestionsAnswers;
use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Models\CampaignForm;
use Closure;

class SetCampaignForm
{
    use FlattenQuestionsAnswers;

    public function __invoke(CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next): CampaignFormSubmissionDto
    {
        $campaignFormSubmissionDto->campaignForm = CampaignForm::query()
            ->where('form_public', $campaignFormSubmissionDto->formId)
            ->with([
                'study',
                'questions' => fn ($query) => $query
                    ->with('questionType')
                    ->orderBy('order_by'),
            ])
            ->first();

        return $next($campaignFormSubmissionDto);
    }
}
