<?php

namespace App\PublicFormSubmission\Actions\SecondaryScreener;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Models\AdditionalCampaignFormLead;
use Closure;

class CreateOrUpdateAdditionalCampaignForm
{
    public function __invoke(CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next): CampaignFormSubmissionDto
    {
        $additionalCampaignForm = AdditionalCampaignFormLead::query()
            ->firstOrCreate([
                'campaign_form_id' => $campaignFormSubmissionDto->campaignForm->id,
                'lead_id'          => $campaignFormSubmissionDto->referral->id,
            ]);

        $additionalCampaignForm->refresh();

        $additionalCampaignForm->version += 1;
        $additionalCampaignForm->is_draft = $campaignFormSubmissionDto->isDraft;
        $additionalCampaignForm->save();

        return $next($campaignFormSubmissionDto);
    }
}
