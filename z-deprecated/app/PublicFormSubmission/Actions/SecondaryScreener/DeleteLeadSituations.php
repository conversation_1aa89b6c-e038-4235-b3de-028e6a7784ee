<?php

namespace App\PublicFormSubmission\Actions\SecondaryScreener;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use Closure;

class DeleteLeadSituations
{
    public function __invoke(CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next): CampaignFormSubmissionDto
    {
        if ($campaignFormSubmissionDto->isDraft) {
            return $next($campaignFormSubmissionDto);
        }

        $referral     = $campaignFormSubmissionDto->referral;
        $campaignForm = $campaignFormSubmissionDto->campaignForm;

        $referral
            ->leadSituations()
            ->where('campaign_form_id', $campaignForm->id)
            ->delete();

        $referral->load('situations');

        $campaignFormSubmissionDto->referral = $referral;

        return $next($campaignFormSubmissionDto);
    }
}
