<?php

namespace App\PublicFormSubmission\Actions\EmbedForm;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use Closure;
use Illuminate\Support\Facades\Event;

class RegisterStaticListeners
{
    public function __invoke(CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next): CampaignFormSubmissionDto
    {
        $campaignForm = $campaignFormSubmissionDto->campaignForm;

        /**
         * We do not want to register the TransactionalNotification listener multiple times
         * to the same event. So filter out the transactional listeners and
         * register them separately.
         */
        $eventListeners = $campaignForm->campaignEventListeners()
            ->isNotTransactional()
            ->with(['campaignEvent', 'campaignListener'])
            ->get();

        foreach ($eventListeners as $eventListener) {
            if (! empty($eventListener->campaignEvent->event) && ! empty($eventListener->campaignListener->listener)) {
                $EventClass  = "App\\PublicFormSubmission\\Events\\{$eventListener->campaignEvent->event}";
                $ListenClass = "App\\PublicFormSubmission\\Listeners\\{$eventListener->campaignListener->listener}";

                if (class_exists($EventClass) && class_exists($ListenClass)) {
                    Event::listen($EventClass, $ListenClass);
                }
            }
        }

        /**
         * Register the TransactionalNotification listeners once for each event
         */
        $eventListeners = $campaignForm->campaignEventListeners()
            ->isTransactional()
            ->with(['campaignEvent', 'campaignListener'])
            ->get();

        $transactionalRegistry = $eventListeners->reduce(function ($registry, $eventListener) {
            $EventClass  = "App\\PublicFormSubmission\\Events\\{$eventListener->campaignEvent->event}";
            $ListenClass = "App\\PublicFormSubmission\\Listeners\\{$eventListener->campaignListener->listener}";

            if (class_exists($EventClass) && class_exists($ListenClass)) {
                $registry[$EventClass] = $ListenClass;
            }

            return $registry;
        }, []);

        foreach ($transactionalRegistry as $EventClass => $ListenClass) {
            Event::listen($EventClass, $ListenClass);
        }

        return $next($campaignFormSubmissionDto);
    }
}
