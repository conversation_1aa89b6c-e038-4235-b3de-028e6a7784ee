<?php

namespace App\PublicFormSubmission\Actions\EmbedForm;

use App\Concerns\FlattenQuestionsAnswers;
use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Models\Site;
use Closure;
use Illuminate\Support\Arr;

class SetSiteId
{
    use FlattenQuestionsAnswers;

    public function __invoke(CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next): CampaignFormSubmissionDto
    {
        $site_id            = null;
        $flattenedAnswers   = $this->flattenAnswersFromArray($campaignFormSubmissionDto->answers, false, true);
        $flattenedQuestions = $this->flattenQuestions($campaignFormSubmissionDto->campaignForm->questions->toArray(), false, true);

        $questions = Arr::where($flattenedQuestions, function ($value) {
            return $value['question_type']['is_allow_sites'];
        });

        if (! empty($questions)) {
            foreach ($questions as $question) {
                $answer = Arr::first($flattenedAnswers, function ($value) use ($question) {
                    return $value['question_uuid'] == $question['uuid'];
                });

                if (! empty($answer['answer'])) {
                    $siteUUID = collect($answer['answer'])->pluck('value')->first();
                    $site_id  = Site::where('uuid', $siteUUID)->value('id');
                }
            }
        }

        $campaignFormSubmissionDto->siteId = $site_id ?: null;

        return $next($campaignFormSubmissionDto);
    }
}
