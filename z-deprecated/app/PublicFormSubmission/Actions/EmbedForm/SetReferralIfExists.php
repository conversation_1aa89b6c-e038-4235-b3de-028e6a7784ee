<?php

namespace App\PublicFormSubmission\Actions\EmbedForm;

use App\Concerns\FlattenQuestionsAnswers;
use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Enums\CampaignFormDupOptionsEnum;
use App\Models\Lead;
use App\Models\Question;
use Closure;
use Illuminate\Support\Arr;

class SetReferralIfExists
{
    use FlattenQuestionsAnswers;

    public function __invoke(CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next): CampaignFormSubmissionDto
    {
        $where   = [];
        $orWhere = [];

        $campaignFormSubmissionDto
            ->campaignForm
            ->questions
            ->filter(function ($question) {
                return in_array($question->questionType->name, ['firstName', 'lastName', 'phone', 'email']);
            })
            ->each(function (Question $question) use ($campaignFormSubmissionDto, &$where, &$orWhere) {
                $questionTypeName = $question->questionType->name;
                $snakeCaseName    = str($questionTypeName)->snake()->value();

                if ($answer = $campaignFormSubmissionDto->referralFields->{$questionTypeName}) {
                    $where[$snakeCaseName] = $answer;

                    if (in_array($questionTypeName, ['phone', 'email'])) {
                        $orWhere[$snakeCaseName] = $answer;
                    }
                }
            });

        if (! empty($where)) {
            $query = Lead::query();

            /**
             * If we have an email and/or phone, we want to make sure the same email or phone
             * doesn't exist for this specific campaign
             */
            if (! empty($orWhere)) {
                $query
                    ->where(function ($q) use ($where, $orWhere) {
                        $q
                            ->where($where)
                            ->orWhere(function ($q) use ($orWhere) {
                                $q->orWhere($orWhere);
                            });
                    });
            }
            /** Otherwise we'll use the first and/or last name */
            else {
                $query->where($where);
            }

            $studyId                   = $campaignFormSubmissionDto->campaignForm->study_id;
            $excludeStudyDuplicates    = $campaignFormSubmissionDto->campaignForm->exclude_study_duplicates;
            $excludedDuplicatesFormIds = $campaignFormSubmissionDto->campaignForm->excluded_duplicates_form_ids;

            /** If duplicates are byStudy */
            if (CampaignFormDupOptionsEnum::byIndex($excludeStudyDuplicates) === CampaignFormDupOptionsEnum::byStudy()) {
                /*
                 * Check if the related campaignForm has exclude_study_duplicates
                 * set to true, if so then we need to check for duplicates across
                 * any campaign form in the study.
                 */
                $query
                    ->where(function ($q) use ($studyId) {
                        $q->whereHas('study', function ($q) use ($studyId) {
                            $q->where('studies.id', $studyId);
                        });
                    });
            }
            /** If duplicates are bySelectedForms */
            elseif ($excludedDuplicatesFormIds) {
                // Get only the 'value' values from the array of json object.
                $excludedDuplicatesFormIds = Arr::pluck($excludedDuplicatesFormIds, 'value');

                /*
                 * If exclude_study_duplicates is set to false, check if the related
                 * campaignForm has excluded_form_duplicates_ids. If so then check against
                 * any campaign form with an ID present in that column for duplicates.
                 */
                $query->where(function ($q) use ($excludedDuplicatesFormIds) {
                    $q
                        ->whereHas('campaignForm', function ($q) use ($excludedDuplicatesFormIds) {
                            $q->wherein('uuid', $excludedDuplicatesFormIds);
                        })
                        ->orWhereHas('additionalCampaignForms', function ($q) use ($excludedDuplicatesFormIds) {
                            $q->wherein('uuid', $excludedDuplicatesFormIds);
                        });
                });
            }
            /** If duplicates are byThisForm */
            else {
                $query
                    ->where(function ($q) use ($campaignFormSubmissionDto) {

                        $q
                            ->where('campaign_form_id', $campaignFormSubmissionDto->campaignForm->id)
                            ->orWhereHas('additionalCampaignForms', function ($q) use ($campaignFormSubmissionDto) {
                                $q->where('campaign_form_id', $campaignFormSubmissionDto->campaignForm->id);
                            });
                    });
            }

            $referral = $query->first();

            $campaignFormSubmissionDto->referral      = $referral;
            $campaignFormSubmissionDto->isNewReferral = empty($referral);
        }

        return $next($campaignFormSubmissionDto);
    }
}
