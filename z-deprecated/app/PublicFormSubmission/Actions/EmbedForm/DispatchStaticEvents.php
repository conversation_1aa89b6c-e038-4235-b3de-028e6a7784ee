<?php

namespace App\PublicFormSubmission\Actions\EmbedForm;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\PublicFormSubmission\Events\AfterQualificationEmailNotificationEvent;
use App\PublicFormSubmission\Events\BeforeQualificationEmailNotificationEvent;
use App\PublicFormSubmission\Events\ChatbotFlowEvent;
use App\PublicFormSubmission\Events\DisqualificationEmailNotificationEvent;
use App\PublicFormSubmission\Events\FacebookConversionEvent;
use App\PublicFormSubmission\Events\QualificationEmailNotificationEvent;
use App\PublicFormSubmission\Events\SiteEmailNotificationEvent;
use Closure;

class DispatchStaticEvents
{
    public function __invoke(CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next): CampaignFormSubmissionDto
    {
        /**
         * Trigger event to capture Facebook Conversion Data
         */
        event(new FacebookConversionEvent($campaignFormSubmissionDto));

        /**
         * Trigger email notification events
         */
        event(new BeforeQualificationEmailNotificationEvent($campaignFormSubmissionDto));
        event(new QualificationEmailNotificationEvent($campaignFormSubmissionDto));
        event(new AfterQualificationEmailNotificationEvent($campaignFormSubmissionDto));

        /**
         * Trigger chatbot flow events
         */
        event(new ChatbotFlowEvent($campaignFormSubmissionDto));

        /**
         * Trigger site email notification flow events
         */
        event(new SiteEmailNotificationEvent($campaignFormSubmissionDto));

        /**
         * Trigger disqualification email notification events
         */
        event(new DisqualificationEmailNotificationEvent($campaignFormSubmissionDto));

        return $next($campaignFormSubmissionDto);
    }
}
