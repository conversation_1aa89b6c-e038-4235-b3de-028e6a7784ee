<?php

namespace App\PublicFormSubmission\Actions\EmbedForm;

use App\Concerns\FlattenQuestionsAnswers;
use App\DataTransferObjects\CampaignFormSubmissionDto;
use Closure;
use Exception;

class ThrowExceptionIfInternalForm
{
    use FlattenQuestionsAnswers;

    public function __invoke(CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next): CampaignFormSubmissionDto
    {
        if ($campaignFormSubmissionDto->campaignForm->show_internal) {
            throw new Exception('This form is not for external use');
        }

        return $next($campaignFormSubmissionDto);
    }
}
