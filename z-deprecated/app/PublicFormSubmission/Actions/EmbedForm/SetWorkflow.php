<?php

namespace App\PublicFormSubmission\Actions\EmbedForm;

use App\Concerns\FlattenQuestionsAnswers;
use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Models\Site;
use Closure;

class SetWorkflow
{
    use FlattenQuestionsAnswers;

    public function __invoke(CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next): CampaignFormSubmissionDto
    {
        $defaultWorkflow = $campaignFormSubmissionDto->campaignForm->study->workflows()->isDefault()->first();
        $site_id         = $campaignFormSubmissionDto->siteId;
        $workflow        = null;

        /** Determine the workflow by the study settings */
        if ($campaignFormSubmissionDto->campaignForm->study->usesCampaignWorkflows()) {
            $workflow = $campaignFormSubmissionDto->campaignForm->workflow;
        } elseif ($campaignFormSubmissionDto->campaignForm->study->usesSiteWorkflows() && ! empty($site_id)) {
            $workflow = Site::find($site_id)?->workflow;
        }

        if (! $workflow) {
            $workflow = $defaultWorkflow;
        }

        $workflow->load('leadStatusWorkflows');

        $campaignFormSubmissionDto->workflow = $workflow;

        return $next($campaignFormSubmissionDto);
    }
}
