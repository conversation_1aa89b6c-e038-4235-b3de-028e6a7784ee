<?php

namespace App\PublicFormSubmission\Actions\EmbedForm;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Enums\LanguageEnum;
use Closure;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class SetRedirectUrls
{
    public function __invoke(CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next): CampaignFormSubmissionDto
    {
        $urlQuery        = [];
        $campaignForm    = $campaignFormSubmissionDto->campaignForm;
        $referral        = $campaignFormSubmissionDto->referral;
        $leadTracking    = $referral->leadTracking;
        $language        = $referral->lang;
        $defaultLanguage = LanguageEnum::en()->value;

        $qualifiedUrl = array_key_exists($language, $campaignForm->thank_you_url_qualified)
            ? $campaignForm->thank_you_url_qualified[$language]
            : $campaignForm->thank_you_url_qualified[$defaultLanguage];

        $qualifiedSituationWithUrl = $referral->situations()
            ->wherePivot('campaign_form_id', $campaignForm->id)
            ->whereNotNull('qualified_url')
            ->orderBy('order_by')
            ->first();

        if ($qualifiedSituationWithUrl) {
            $qualifiedUrl = array_key_exists($language, $qualifiedSituationWithUrl->qualified_url)
                ? $qualifiedSituationWithUrl->qualified_url[$language]
                : $qualifiedSituationWithUrl->qualified_url[$defaultLanguage];
        }

        $disqualifiedUrl = array_key_exists($language, $campaignForm->thank_you_url_disqualified)
            ? $campaignForm->thank_you_url_disqualified[$language]
            : $campaignForm->thank_you_url_disqualified[$defaultLanguage];

        if (! empty($leadTracking->utm)) {
            $utms     = [];
            $origUtms = $leadTracking->utm;

            foreach ($origUtms as $key => $utm) {
                $urlQuery["utm_{$key}"] = $utm;
            }

            /*$utms = http_build_query($utms);
            $qualifiedUrl .= Str::contains($qualifiedUrl, '?') ? "&{$utms}" : "?{$utms}";
            $disqualifiedUrl .= Str::contains($disqualifiedUrl, '?') ? "&{$utms}" : "?{$utms}";*/
        }

        /** Append the referral query parameter if the referral is new. */
        if ($campaignFormSubmissionDto->isNewReferral) {
            $urlQuery['referral'] = $referral->uuid;
            /*$referralQueryParam = http_build_query(['referral' => $referral->uuid]);
            $qualifiedUrl .= Str::contains($qualifiedUrl, '?') ? "&{$referralQueryParam}" : "?{$referralQueryParam}";
            $disqualifiedUrl .= Str::contains($disqualifiedUrl, '?') ? "&{$referralQueryParam}" : "?{$referralQueryParam}";*/
        }

        if (! empty($urlQuery)) {
            $queryString = Arr::query($urlQuery);
            $qualifiedUrl .= Str::contains($qualifiedUrl, '?') ? "&{$queryString}" : "?{$queryString}";
            $disqualifiedUrl .= Str::contains($disqualifiedUrl, '?') ? "&{$queryString}" : "?{$queryString}";
        }

        $campaignFormSubmissionDto->qualifiedUrl    = $qualifiedUrl;
        $campaignFormSubmissionDto->disqualifiedUrl = $disqualifiedUrl;

        return $next($campaignFormSubmissionDto);
    }
}
