<?php

namespace App\PublicFormSubmission\Actions\EmbedForm;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Models\Lead;
use Closure;

class SaveAndSetReferralIfNotExists
{
    public function __invoke(CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next): CampaignFormSubmissionDto
    {
        /** If this isn't a new referral, update version and return early */
        if (! $campaignFormSubmissionDto->isNewReferral) {
            // TODO: When we implement the external secondary screener, this needs to account for the additional campaign form instead
            $campaignFormSubmissionDto->referral->version += 1;
            $campaignFormSubmissionDto->referral->save();

            $campaignFormSubmissionDto->referral->load(['study', 'campaignForm']);

            return $next($campaignFormSubmissionDto);
        }

        /** If this a new referral, let's create and save the lead model*/

        $workflow                         = $campaignFormSubmissionDto->workflow;
        $referralFields                   = $campaignFormSubmissionDto->referralFields;
        $leadStatusWorkflows              = $workflow->leadStatusWorkflows;
        $newLeadStatusWorkflowId          = $leadStatusWorkflows->where('lead_status_id', $workflow->new_lead_status_id)->first()->id;
        $disqualifiedLeadStatusWorkflowId = $leadStatusWorkflows->where('lead_status_id', $workflow->disqual_lead_status_id)->first()->id;
        $leadStatusWorkflowId             = $campaignFormSubmissionDto->qualifiedSituationIds->isNotEmpty() ? $newLeadStatusWorkflowId : $disqualifiedLeadStatusWorkflowId;

        $referral = Lead::create([
            'best_time'               => $referralFields->bestTime,
            'campaign_form_id'        => $campaignFormSubmissionDto->campaignForm->id,
            'email'                   => $referralFields->email,
            'first_name'              => $referralFields->firstName,
            'is_test'                 => $referralFields->isTest,
            'lang'                    => $referralFields->lang,
            'last_name'               => $referralFields->lastName,
            'lead_status_workflow_id' => $leadStatusWorkflowId,
            'origin_ip'               => $campaignFormSubmissionDto->clientIp,
            'phone'                   => $referralFields->phone,
            'site_id'                 => $campaignFormSubmissionDto->siteId,
            'study_id'                => $campaignFormSubmissionDto->campaignForm->study->id,
            'version'                 => 1,
            'workflow_id'             => $workflow->id,
        ]);

        $referral->refresh();
        $referral->load(['study', 'campaignForm']);

        $campaignFormSubmissionDto->referral = $referral;

        return $next($campaignFormSubmissionDto);
    }
}
