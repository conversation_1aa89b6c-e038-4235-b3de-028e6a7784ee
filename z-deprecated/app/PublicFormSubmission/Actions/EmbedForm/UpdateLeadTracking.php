<?php

namespace App\PublicFormSubmission\Actions\EmbedForm;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Models\LeadTracking;
use Closure;

class UpdateLeadTracking
{
    public function __invoke(CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next): CampaignFormSubmissionDto
    {
        /** Either retrieve the tracking record from the referral or trackingId form the request */
        $referral     = $campaignFormSubmissionDto->referral;
        $leadTracking = $referral->leadTrackings()->first() ?? LeadTracking::where('uuid', $campaignFormSubmissionDto->trackingId)->first();

        /** Leads should only have one tracking record. Only attach if tracking record doesn't exist */
        if ($leadTracking) {
            $leadTracking
                ->update([
                    'lead_id' => $campaignFormSubmissionDto->referral->id,
                    'params'  => [
                        'fbcCookie' => $campaignFormSubmissionDto->fbcCookie,
                        'fbpCookie' => $campaignFormSubmissionDto->fbpCookie,
                    ],
                ]);
        }

        $referral->load('leadTracking');

        $campaignFormSubmissionDto->referral = $referral;

        return $next($campaignFormSubmissionDto);
    }
}
