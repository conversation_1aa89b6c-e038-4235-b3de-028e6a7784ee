<?php

namespace App\PublicFormSubmission\Actions;

use App\Concerns\FlattenQuestionsAnswers;
use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\DataTransferObjects\SituationTreeReportCardDto;
use App\Enums\NumberOperatorsEnum;
use App\Models\Question;
use App\Models\Situation;
use App\ValueObjects\DobAnswer;
use Closure;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class SituationTreeWalker
{
    use FlattenQuestionsAnswers;

    private array $situations;

    private Collection $reportCard;

    public function __construct()
    {
        $this->reportCard = collect();
    }

    /**
     * Runs lead answers through all situation/condition logic, returning a comma
     * delimited string of qualification buckets, and a report card containing
     * all the answer/condition results.
     */
    public function __invoke(CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next): CampaignFormSubmissionDto
    {
        if ($campaignFormSubmissionDto->isDraft) {
            return $next($campaignFormSubmissionDto);
        }

        $situations = Situation::where('campaign_form_id', $campaignFormSubmissionDto->campaignForm->id)
            ->with(['situationGroupsForBuilder'])
            ->get()
            ->toArray();

        $answerTree = $this->flattenAnswersFromArray($campaignFormSubmissionDto->answers, false, true);

        $situationResults = collect($situations)
            ->mapWithKeys(function ($situation) use ($answerTree) {
                $situationGroupsResult = ! empty($situation['situation_groups_for_builder'])
                    ? $this->evaluateSituationGroups($situation['situation_groups_for_builder'], $answerTree)
                    : [false];

                return [$situation['id'] => $situationGroupsResult];
            });

        $qualifiedSituationIds = $situationResults
            ->reject(function ($results, $situationId) use ($situations) {
                $results = collect($results);

                /**
                 * Reject any results that contain falsey values or if the
                 * situation doesn't exist in the situations array.
                 */
                return $results->filter()->count() !== $results->count() ||
                    collect($situations)->doesntContain('id', $situationId);
            })
            ->keys();

        $campaignFormSubmissionDto->reportCard            = $this->reportCard;
        $campaignFormSubmissionDto->qualifiedSituationIds = $qualifiedSituationIds;

        return $next($campaignFormSubmissionDto);
    }

    public function evaluateSituationGroups($situationGroups, $answerTree, $situationGroupIsChild = false): array
    {
        $return = [];

        foreach ($situationGroups as $situationGroup) {
            /* If this is a child group, and it doesn't have any children to evaluate,
             * we skip it since the children are evaluated along with its parent
             */
            if ($situationGroupIsChild && empty($situationGroup['children'])) {
                continue;
            }

            $expectedTrueResults = 1;
            $results             = [$this->parseGroup($situationGroup)($answerTree)];
            /** Evaluate the group's conditions will return true/false for the group */

            /* Evaluate the children's conditions and add then to the results array
             * ie. If the group has 3 children, the total boolean results in the results array should be 4
             */
            foreach ($situationGroup['children'] as $child) {
                $results[] = $this->parseGroup($child)($answerTree);

                /* We only increment the $expectedTrueResults when the child group operator is & */
                if ($child['group_operator'] == '&') {
                    $expectedTrueResults += 1;
                }
            }

            /* Count the number of true results and determine if they equal $expectedTrueResults.
             * This is the final evaluation for the entire group including its children
             */
            $return[] = collect($results)->filter()->count() == $expectedTrueResults;
            $return   = array_merge($return, $this->evaluateSituationGroups($situationGroup['children'], $answerTree, true));
        }

        return $return;
    }

    private function parseGroup($situationGroup): Closure
    {
        $conditionOperator = ! empty($situationGroup['operator']) && ! empty($situationGroup['situation_group_conditions']) ? $situationGroup['operator'] : null;
        $logicalFunction   = $this->logicalFuncs($conditionOperator);

        /** Checks if a logical function exists for this situations operator */
        if (empty($conditionOperator) || ! $logicalFunction) {
            return function () {
                return null;
            };
        }

        $conditionClosures = collect($situationGroup['situation_group_conditions'])
            ->map(function ($situationGroupCondition) {
                return function ($answerTree) use ($situationGroupCondition) {
                    return $this->compareAnswers($answerTree, $situationGroupCondition);
                };
            });

        return function ($answerTree) use ($conditionClosures, $logicalFunction) {
            $conditionResults = collect($conditionClosures)
                ->map(function ($func) use ($answerTree) {
                    return $func($answerTree);
                })
                /** Remove null results returned for child questions that never displayed */
                ->reject(fn ($result) => is_null($result));

            return $logicalFunction($conditionResults);
        };
    }

    private function logicalFuncs($key): bool|Closure
    {
        $logicFunctions = [
            '&' => function ($results) {
                $resultCount = collect($results)->filter()->count();

                return $resultCount == count($results);
            },
            '|' => function ($results) {
                $resultCount = collect($results)->filter()->count();

                return $resultCount > 0;
            },
            '^' => function ($results) {
                $resultCount = collect($results)->filter(function ($result) use ($results) {
                    return $results[0] == $result;
                })->count();

                return $resultCount != count($results);
            },
            '!' => function ($results) {
                $resultCount = collect($results)->filter()->count();

                return $resultCount == 0;
            },
        ];

        return ! empty($logicFunctions[$key]) ? $logicFunctions[$key] : false;
    }

    private function compareAnswers($answerTree, $situationGroupCondition)
    {
        $questionUUID = $situationGroupCondition['question']['uuid'] ?? null;
        $questionType = $situationGroupCondition['question']['question_type'] ?? null;

        if ($questionUUID) {
            $answerBranch = $this->findAnswerBranch($questionUUID, $answerTree);
        }

        /**
         * If we didn't find the matching answer for this condition,
         * or it doesn't have an answer populated, return.
         */
        if (empty($answerBranch) || ! isset($answerBranch['answer'])) {
            return;
        }

        [$isChild, $childQuestion] = $this->isChildQuestion($questionUUID);

        if ($isChild) {
            $parentAnswerBranch  = $this->findAnswerBranch($childQuestion->parent->uuid, $answerTree);
            $parentAnswerCorrect = $this->evaluateParentAnswer($situationGroupCondition['question']['conditionSource'], $parentAnswerBranch['answer']);

            /**
             * If the parent answer is incorrect, we don't need to evaluate the child question
             */
            if (! $parentAnswerCorrect) {
                return;
            }
        }

        $operator = $situationGroupCondition['operator'];

        /**
         * Process calculated values
         * Mutate answer and extract any [calculated] key value if the array key of calculated exists
         */
        if (is_array($answerBranch['answer']) && array_key_exists('calculated', $answerBranch['answer'])) {
            $answerBranch['answer'] = $answerBranch['answer']['calculated'];
        }

        if ($questionType && $questionType['name'] == 'dob') {
            $answerBranch['answer'] = (new DobAnswer($answerBranch['answer']))->age();
        }

        if ($compareFunction = $this->compareFuncs($operator)) {
            $answer            = $answerBranch['answer'];
            $compareConditions = $situationGroupCondition['conditions'];

            if ($questionType['is_allow_options'] || $questionType['is_allow_sites']) {
                $compareConditions = collect(array_is_list($situationGroupCondition['conditions']) ? $situationGroupCondition['conditions'] : [$situationGroupCondition['conditions']])->pluck('value')->toArray();
                $answer            = collect($answerBranch['answer'])->pluck('value')->toArray();
            }

            $isCorrect = $compareFunction($compareConditions, $answer);

            /** Used in FormatSaveAnswers when saving condition/answer results */
            $this->reportCard->push(
                SituationTreeReportCardDto::from([
                    'situation_group_condition_id' => $situationGroupCondition['id'],
                    'question_id'                  => $situationGroupCondition['question']['id'],
                    'question_uuid'                => $situationGroupCondition['question']['uuid'],
                    'is_correct'                   => $isCorrect,
                    'operator'                     => $operator,
                    'conditions'                   => $situationGroupCondition['conditions'],
                ]),
            );

            return $isCorrect;
        }
    }

    private function findAnswerBranch(string $questionUUID, $answerTree): ?array
    {
        return Arr::first($answerTree, function ($value) use ($questionUUID) {
            return $value['question_uuid'] == $questionUUID;
        });
    }

    private function isChildQuestion($uuid): array
    {
        $question = Question::where('uuid', $uuid)->first();

        return [$question->parent_id !== null, $question];
    }

    private function evaluateParentAnswer($childConditionSource, int|array $rawParentAnswer): bool
    {
        if (! empty($childConditionSource['operator'])) {
            $compareOperator  = NumberOperatorsEnum::operatorToKey($childConditionSource['operator']);
            $compareFunction  = $this->compareFuncs($compareOperator);
            $comparisonResult = $compareFunction($rawParentAnswer, $childConditionSource);
        } else {
            $rawParentAnswer  = Arr::wrap($rawParentAnswer);
            $rawParentAnswer  = collect($rawParentAnswer)->pluck('value')->toArray();
            $arrayIntersect   = array_intersect($rawParentAnswer, $childConditionSource);
            $comparisonResult = ! empty($rawParentAnswer) && $arrayIntersect === $rawParentAnswer;
        }

        return (bool)$comparisonResult;
    }

    private function compareFuncs($key): bool|Closure
    {
        $compareFunctions = [
            'ge'    => function ($expected, $actual) {
                return $actual >= $expected;
            },
            'gt'    => function ($expected, $actual) {
                return $actual > $expected;
            },
            'le'    => function ($expected, $actual) {
                return $actual <= $expected;
            },
            'lt'    => function ($expected, $actual) {
                return $actual < $expected;
            },
            'eq'    => function ($expected, $actual) {
                $result = $expected === $actual;

                if (is_array($actual) && is_array($expected)) {
                    $result = collect($expected)->diff($actual)->isEmpty();
                } elseif (is_array($expected) && ! is_array($actual)) {
                    $result = in_array($actual, $expected);
                } elseif (! is_array($expected) && is_array($actual)) {
                    $result = in_array($expected, $actual);
                }

                return $result;
            },
            'ne'    => function ($expected, $actual) {
                $result = false;

                if (! is_array($actual) && ! is_array($expected)) {
                    $result = $expected != $actual;
                } elseif (is_array($actual) && is_array($expected)) {
                    $result = collect($actual)->intersect($expected)->isEmpty();
                } elseif (is_array($expected) && ! is_array($actual)) {
                    $result = ! in_array($actual, $expected);
                } elseif (! is_array($expected) && is_array($actual)) {
                    $result = ! in_array($expected, $actual);
                }

                return $result;
            },
            'in'    => function ($expected, $actual) {
                return ! empty($actual) && is_array($actual)
                    ? $expected === $actual || collect($actual)->every(function ($value) use ($expected) {
                        return in_array($value, $expected);
                    })
                    : in_array($actual, $expected);
            },
            'inAny' => function ($expected, $actual) {
                return ! empty($actual) && is_array($actual)
                    ? $expected === $actual || collect($expected)->intersect($actual)->isNotEmpty()
                    : in_array($actual, $expected);
            },
        ];

        return ! empty($compareFunctions[$key]) ? $compareFunctions[$key] : false;
    }
}
