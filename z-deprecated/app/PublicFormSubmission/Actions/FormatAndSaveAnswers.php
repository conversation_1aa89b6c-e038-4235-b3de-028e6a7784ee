<?php

namespace App\PublicFormSubmission\Actions;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Models\Lead;
use App\Models\LeadAnswer;
use App\Models\LeadAnswerResult;
use App\Models\Question;
use App\Models\QuestionType;
use Closure;
use Illuminate\Support\Collection;

class FormatAndSaveAnswers
{
    public ?int $top_parent_id = null;

    public ?int $parent_id = null;

    public ?Collection $questions = null;

    public ?Collection $questionTypes = null;

    public array $questionIds = [];

    public array $answerIds = [];

    private array $questionAnswers = [];

    public int $orderBy = 0;

    public function __invoke(CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next): CampaignFormSubmissionDto
    {
        $campaignForm        = $campaignFormSubmissionDto->campaignForm;
        $referral            = $campaignFormSubmissionDto->referral;
        $answers             = $campaignFormSubmissionDto->answers;
        $questions           = $campaignForm->questions;
        $this->questions     = $questions->keyBy('uuid');
        $this->questionIds   = $questions->pluck('id', 'uuid')->toArray();
        $this->questionTypes = QuestionType::all()->keyBy('uuid');
        $this->answerIds     = LeadAnswer::query()
            ->whereHas('question.campaignForm', function ($query) use ($campaignForm) {
                $query->where('campaign_forms.id', $campaignForm->id);
            })
            ->where('lead_id', $referral->id)
            ->pluck('id')
            ->toArray();

        if (! empty($answers)) {
            foreach ($answers as $answer) {
                $this->saveAnswers($referral, [$answer]);
            }
        }

        if ($campaignFormSubmissionDto->isNewReferral || $campaignFormSubmissionDto->campaignForm->show_internal) {
            $this->saveLeadAnswerResults($campaignFormSubmissionDto->reportCard);
        }

        return $next($campaignFormSubmissionDto);
    }

    public function saveAnswers(Lead $referral, array $answerInfos, bool $isChild = false): void
    {
        if (! $isChild) {
            $this->top_parent_id = null;
            $this->parent_id     = null;
        }

        foreach ($answerInfos as $answerInfo) {
            $answer       = $answerInfo['answer'] ?? null;
            $question     = $this->questions->get($answerInfo['question_uuid']);
            $questionType = $this->questionTypes->get($answerInfo['question_type_uuid']);

            if ($questionType->is_layout) {
                continue;
            }

            if (is_array($answer) && empty($answer)) {
                $answer = null;
            }

            $saveValues = [
                'lead_id'          => $referral->id,
                'top_parent_id'    => $isChild ? $this->top_parent_id : null,
                'parent_id'        => $isChild ? $this->parent_id : null,
                'question_id'      => $question->id,
                'question_type_id' => $questionType->id,
                'question_label'   => $answerInfo['question_label'],
                'answer'           => $answer,
                'options'          => ! empty($answerInfo['options']) ? $answerInfo['options'] : null,
                'is_predefined'    => $question->is_predefined,
                'is_encrypted'     => $question->is_encrypted,
                'is_required'      => $question->is_required,
                'order_by'         => $this->orderBy,
            ];

            $newAnswer = LeadAnswer::where(
                [
                    'question_id' => $question->id,
                    'lead_id'     => $referral->id,
                ],
            )->first();

            if (! empty($newAnswer)) {
                $newAnswer->update($saveValues);
            } else {
                $newAnswer = LeadAnswer::create($saveValues);
            }

            $this->questionAnswers[$answerInfo['question_uuid']] = $newAnswer->id;
            $this->orderBy++;
            $this->parent_id = $newAnswer->id;

            if (! empty($this->answerIds[$newAnswer->id])) {
                unset($this->answerIds[$newAnswer->id]);
            }

            if (! $isChild) {
                $this->top_parent_id = $newAnswer->id;
            }

            if (! empty($answerInfo['children'])) {
                $this->saveAnswers($referral, $answerInfo['children'], true);
            } else {
                $this->parent_id = $this->top_parent_id;
            }
        }
    }

    /**
     * Saves the lead answer results from the SituationTreeWalker
     * with each matching question and situation condition.
     */
    private function saveLeadAnswerResults(Collection $reportCard): void
    {
        foreach ($reportCard as $result) {
            $data = [
                'situation_group_condition_id' => $result->situation_group_condition_id,
                'question_id'                  => $result->question_id,
                'lead_answer_id'               => $this->questionAnswers[$result->question_uuid],
                'operator'                     => $result->operator,
            ];

            LeadAnswerResult::updateOrCreate($data, ['is_correct' => $result->is_correct, 'conditions' => $result->conditions]);
        }
    }
}
