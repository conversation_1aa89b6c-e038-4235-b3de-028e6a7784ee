<?php

namespace App\PublicFormSubmission\Actions;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Models\LeadSituation;
use Closure;

class SaveLeadSituations
{
    public function __invoke(CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next): CampaignFormSubmissionDto
    {
        if ($campaignFormSubmissionDto->isDraft) {
            return $next($campaignFormSubmissionDto);
        }

        $qualifiedSituationIds = $campaignFormSubmissionDto->qualifiedSituationIds;
        $referral              = $campaignFormSubmissionDto->referral;

        /** If we don't have qualificationSituationIds or this isn't a new referral and this isn't an internal form, return early */
        if (
            $qualifiedSituationIds->isEmpty() ||
            (! $campaignFormSubmissionDto->isNewReferral && ! $campaignFormSubmissionDto->campaignForm->show_internal)
        ) {
            return $next($campaignFormSubmissionDto);
        }

        foreach ($qualifiedSituationIds as $situationId) {
            $leadSituation = LeadSituation::withTrashed()
                ->firstOrCreate([
                    'lead_id'          => $campaignFormSubmissionDto->referral->id,
                    'situation_id'     => $situationId,
                    'campaign_form_id' => $campaignFormSubmissionDto->campaignForm->id,
                ]);

            if ($leadSituation->trashed()) {
                $leadSituation->restore();
            }
        }

        $referral->load(['situations']);

        $campaignFormSubmissionDto->referral = $referral;

        return $next($campaignFormSubmissionDto);
    }
}
