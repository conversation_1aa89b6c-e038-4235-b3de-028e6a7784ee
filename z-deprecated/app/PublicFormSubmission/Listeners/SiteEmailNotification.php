<?php

namespace App\PublicFormSubmission\Listeners;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Models\User;
use App\PublicFormSubmission\Events\SiteEmailNotificationEvent;
use App\PublicFormSubmission\Notifications\LeadSiteNotification;
use Illuminate\Contracts\Queue\ShouldQueue;

class SiteEmailNotification implements ShouldQueue
{
    /**
     * Send notifications to sites for qualified leads
     */
    public function handle(SiteEmailNotificationEvent $event): void
    {
        /** @var CampaignFormSubmissionDto $campaignFormSubmissionDto */
        $campaignFormSubmissionDto = $event->campaignFormSubmissionDto;
        $referral                  = $campaignFormSubmissionDto->referral;
        $campaignForm              = $campaignFormSubmissionDto->campaignForm;

        if (! empty($referral->site_id) && $referral->isQualified($referral->campaignForm) && $campaignFormSubmissionDto->isNewReferral) {
            $users = User::whereHas('sites', function ($q) use ($referral) {
                $q->where('site_id', $referral->site_id);
            })->get();

            foreach ($users as $user) {
                $user->notify(
                    new LeadSiteNotification(
                        $user->acc_name,
                        $referral->study->study_name,
                        $referral->acc_qualification_bucket,
                        $campaignForm,
                    ),
                );
            }
        }
    }
}
