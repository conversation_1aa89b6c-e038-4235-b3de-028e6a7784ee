<?php

namespace App\PublicFormSubmission\Listeners;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\PublicFormSubmission\Events\QualificationEmailNotificationEvent;
use App\PublicFormSubmission\Notifications\MeiraGTxLeadQualifiedConfirmation;
use Illuminate\Contracts\Queue\ShouldQueue;

class MeiraGTxQualificationEmailNotification implements ShouldQueue
{
    public function handle(QualificationEmailNotificationEvent $event): void
    {
        /** @var CampaignFormSubmissionDto $campaignFormSubmissionDto */
        $campaignFormSubmissionDto = $event->campaignFormSubmissionDto;
        $referral                  = $campaignFormSubmissionDto->referral;

        if ($referral->isQualified($referral->campaignForm) && ! empty($referral->email) && $campaignFormSubmissionDto->isNewReferral) {
            $referralName = $referral->acc_lead_name;

            $referral->notify(new MeiraGTxLeadQualifiedConfirmation($referralName, $referral->campaignForm));
        }
    }
}
