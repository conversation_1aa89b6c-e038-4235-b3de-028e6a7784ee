<?php

namespace App\PublicFormSubmission\Listeners;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Jobs\SendTransactionalSmsNotification;
use App\Models\CampaignListener;
use App\Models\Lead;
use App\Models\TransactionalEmail;
use App\Models\TransactionalSms;
use App\Models\User;
use App\PublicFormSubmission\Contracts\PublicFormSubmissionTransactionalEvent;
use App\PublicFormSubmission\Events\DisqualificationEmailNotificationEvent;
use App\PublicFormSubmission\Events\QualificationEmailNotificationEvent;
use App\PublicFormSubmission\Events\SiteEmailNotificationEvent;
use App\PublicFormSubmission\Notifications\TransactionalEmailNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;

class TransactionalNotification implements ShouldQueue
{
    public function handle(PublicFormSubmissionTransactionalEvent $event): void
    {
        /** @var CampaignFormSubmissionDto $campaignFormSubmissionDto */
        $campaignFormSubmissionDto = $event->campaignFormSubmissionDto;

        $referral  = $campaignFormSubmissionDto->referral;
        $eventName = getClassShortName($event);

        /**
         * @var Collection<TransactionalEmail>|Collection<TransactionalSms> $transactionables
         */
        $transactionables = CampaignListener::forTransactionalNotifications($eventName, $referral->campaign_form_id)
            ->with('transactionable')
            ->get()
            ->pluck('transactionable');

        foreach ($transactionables as $transactionable) {
            if (! $this->shouldSendTransactionable($event, $campaignFormSubmissionDto, $transactionable)) {
                continue;
            }

            if ($transactionable instanceof TransactionalEmail) {
                $recipients = collect([$referral]);

                if ($event instanceof SiteEmailNotificationEvent) {
                    $recipients = User::whereHas('sites', function ($q) use ($referral) {
                        $q->where('site_id', $referral->site_id);
                    })->get();
                }

                foreach ($recipients as $recipient) {
                    $recipient->notify(new TransactionalEmailNotification($referral, $transactionable));
                }
            }

            if ($transactionable instanceof TransactionalSms) {
                SendTransactionalSmsNotification::dispatch($transactionable, $referral);
            }
        }
    }

    private function shouldSendTransactionable(PublicFormSubmissionTransactionalEvent $event, CampaignFormSubmissionDto $campaignFormSubmissionDto, TransactionalEmail|TransactionalSms $transactionable): bool
    {
        /** @var Lead $referral */
        $referral  = $campaignFormSubmissionDto->referral;
        $eventName = getClassShortName($event);

        $shouldSendTransactionable = match ($eventName) {
            getClassShortName(QualificationEmailNotificationEvent::class)    => function () use ($referral, $campaignFormSubmissionDto, $transactionable) {
                $isNewAndQualified = $referral->isQualified($referral->campaignForm) && $campaignFormSubmissionDto->isNewReferral;

                if ($transactionable instanceof TransactionalEmail) {
                    return $isNewAndQualified && ! empty($referral->email);
                }

                if ($transactionable instanceof TransactionalSms) {
                    return $isNewAndQualified && ! empty($referral->phone);
                }

                return false;
            },
            getClassShortName(DisQualificationEmailNotificationEvent::class) => function () use ($referral, $campaignFormSubmissionDto, $transactionable) {
                $isNewAndDisqualified = $referral->isDisqualified($referral->campaignForm) && $campaignFormSubmissionDto->isNewReferral;

                if ($transactionable instanceof TransactionalEmail) {
                    return $isNewAndDisqualified && ! empty($referral->email);
                }

                if ($transactionable instanceof TransactionalSms) {
                    return $isNewAndDisqualified && ! empty($referral->phone);
                }

                return false;
            },
            getClassShortName(SiteEmailNotificationEvent::class)             => function () use ($referral, $campaignFormSubmissionDto) {
                return $referral->isQualified($referral->campaignForm) && $campaignFormSubmissionDto->isNewReferral && ! empty($referral->site_id);
            },
            default                                                          => fn () => false,
        };

        return $shouldSendTransactionable();
    }
}
