<?php

namespace App\PublicFormSubmission\Listeners;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Jobs\CreateNovenEconsentProfile;
use App\PublicFormSubmission\Events\AfterQualificationEmailNotificationEvent;

class CastorNovenIntegration
{
    public function handle(AfterQualificationEmailNotificationEvent $event): void
    {
        /** @var CampaignFormSubmissionDto $campaignFormSubmissionDto */
        $campaignFormSubmissionDto = $event->campaignFormSubmissionDto;
        $referral                  = $campaignFormSubmissionDto->referral;
        $eConsentIntegration       = $referral->study->getCastorEconsentIntegration();

        if ($eConsentIntegration && $campaignFormSubmissionDto->isNewReferral && $referral->isQualified($referral->campaignForm) && ! empty($referral->email)) {
            CreateNovenEconsentProfile::dispatch($referral);
        }
    }
}
