<?php

namespace App\PublicFormSubmission\Listeners;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\PublicFormSubmission\Events\DisqualificationEmailNotificationEvent;
use App\PublicFormSubmission\Notifications\LeadDisqualifiedConfirmation;

class DisqualificationEmailNotification
{
    public function handle(DisqualificationEmailNotificationEvent $event): void
    {
        /** @var CampaignFormSubmissionDto $campaignFormSubmissionDto */
        $campaignFormSubmissionDto = $event->campaignFormSubmissionDto;
        $referral                  = $campaignFormSubmissionDto->referral;

        if ($referral->isDisqualified($referral->campaignForm) && ! empty($referral->email) && $campaignFormSubmissionDto->isNewReferral) {
            $referralName = $referral->acc_lead_name;

            $referral->notify(new LeadDisqualifiedConfirmation($referralName, $referral->campaignForm));
        }
    }
}
