<?php

namespace App\PublicFormSubmission\Listeners;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Jobs\RegisterObvioParticipant;
use App\Models\IntegrationStudy;
use App\PublicFormSubmission\Events\AfterQualificationEmailNotificationEvent;

class CoolTech2Integration
{
    public function handle(AfterQualificationEmailNotificationEvent $event): void
    {
        /** @var CampaignFormSubmissionDto $campaignFormSubmissionDto */
        $campaignFormSubmissionDto = $event->campaignFormSubmissionDto;
        $referral                  = $campaignFormSubmissionDto->referral;
        $obvioIntegration          = $referral->study->getObvioIntegration();
        $integrationStudy          = IntegrationStudy::find($obvioIntegration?->pivot?->id);

        if ($obvioIntegration && $campaignFormSubmissionDto->isNewReferral && $referral->isQualified($referral->campaignForm) && ! empty($referral->email) && ! empty($integrationStudy?->study_code)) {
            RegisterObvioParticipant::dispatch($referral);
        }
    }
}
