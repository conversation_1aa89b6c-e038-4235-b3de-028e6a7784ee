<?php

namespace App\PublicFormSubmission\Listeners;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\PublicFormSubmission\Events\FacebookConversionEvent;
use App\PublicFormSubmission\Resources\FacebookConversionResource;
use App\Utilities\PhoneUtility;
use FacebookAds\Api;
use FacebookAds\Logger\CurlLogger;
use FacebookAds\Object\ServerSide\ActionSource;
use FacebookAds\Object\ServerSide\Event;
use FacebookAds\Object\ServerSide\EventRequest;
use FacebookAds\Object\ServerSide\UserData;
use Illuminate\Contracts\Queue\ShouldQueue;

class ProcessFacebookConversion implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(FacebookConversionEvent $event): void
    {
        /** @var CampaignFormSubmissionDto $campaignFormSubmissionDto */
        $campaignFormSubmissionDto = $event->campaignFormSubmissionDto;
        $referral                  = $campaignFormSubmissionDto->referral;

        if ($campaignFormSubmissionDto->isNewReferral) {
            $payload     = (new FacebookConversionResource($referral))->resolve();
            $accessToken = config('services.facebook.access_token');
            $pixel_id    = config('services.facebook.pixel_id');
            $api         = Api::init(null, null, $accessToken);

            $api->setLogger(new CurlLogger());

            $user_data = (new UserData())
                ->setEmails([$payload['lead']['email']])
                ->setPhones([PhoneUtility::clean_phone($payload['lead']['phone'])])
                ->setClientIpAddress($payload['lead']['clientIp'])
                ->setClientUserAgent($payload['lead']['clientUserAgent'])
                ->setFbc($payload['lead']['fbcCookie'])
                ->setFbp($payload['lead']['fbpCookie']);
            $event = (new Event())
                ->setEventId($payload['serverEvent']['eventId'])
                ->setEventName('Lead')
                ->setEventTime(time())
                ->setEventSourceUrl($payload['serverEvent']['eventSourceUrl'])
                ->setUserData($user_data)
                ->setOptOut(false)
                ->setActionSource(ActionSource::WEBSITE);
            $request = (new EventRequest($pixel_id))
                ->setEvents([$event]);
            $request->execute();
        }
    }
}
