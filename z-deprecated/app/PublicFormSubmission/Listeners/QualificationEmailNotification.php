<?php

namespace App\PublicFormSubmission\Listeners;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\PublicFormSubmission\Events\QualificationEmailNotificationEvent;
use App\PublicFormSubmission\Notifications\LeadQualifiedConfirmation;

class QualificationEmailNotification
{
    public function handle(QualificationEmailNotificationEvent $event): void
    {
        /** @var CampaignFormSubmissionDto $campaignFormSubmissionDto */
        $campaignFormSubmissionDto = $event->campaignFormSubmissionDto;
        $referral                  = $campaignFormSubmissionDto->referral;

        if ($referral->isQualified($referral->campaignForm) && ! empty($referral->email) && $campaignFormSubmissionDto->isNewReferral) {
            $referralName = $referral->acc_lead_name;
            $referral->notify(new LeadQualifiedConfirmation($referralName, $referral->campaignForm));
        }
    }
}
