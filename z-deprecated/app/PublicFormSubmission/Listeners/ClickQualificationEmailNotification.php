<?php

namespace App\PublicFormSubmission\Listeners;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\PublicFormSubmission\Events\QualificationEmailNotificationEvent;
use App\PublicFormSubmission\Notifications\ClickLeadQualifiedConfirmation;

class ClickQualificationEmailNotification
{
    public function handle(QualificationEmailNotificationEvent $event): void
    {
        /** @var CampaignFormSubmissionDto $campaignFormSubmissionDto */
        $campaignFormSubmissionDto = $event->campaignFormSubmissionDto;
        $referral                  = $campaignFormSubmissionDto->referral;

        if ($referral->isQualified($referral->campaignForm) && ! empty($referral->email) && $campaignFormSubmissionDto->isNewReferral) {
            $referralName = ! empty($referral->acc_lead_name) ? $referral->acc_lead_name : 'Participant';

            $referral->notify(new ClickLeadQualifiedConfirmation($referralName, $referral->campaignForm));
        }
    }
}
