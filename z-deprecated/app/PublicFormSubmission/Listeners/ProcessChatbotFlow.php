<?php

namespace App\PublicFormSubmission\Listeners;

use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\PublicFormSubmission\Events\ChatbotFlowEvent;
use App\Services\ChatbotService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Twilio\Exceptions\TwilioException;

class ProcessChatbotFlow implements ShouldQueue
{
    /**
     * @throws TwilioException
     */
    public function handle(ChatbotFlowEvent $event): void
    {
        /** @var CampaignFormSubmissionDto $campaignFormSubmissionDto */
        $campaignFormSubmissionDto = $event->campaignFormSubmissionDto;
        $referral                  = $campaignFormSubmissionDto->referral;
        $campaignForm              = $campaignFormSubmissionDto->campaignForm;

        if ($referral->isQualified($campaignForm) && ! empty($referral->phone) && $campaignForm->studioFlow && $campaignFormSubmissionDto->isNewReferral) {
            (new ChatbotService())->processChatbotFlow($referral, $campaignForm);
        }
    }
}
