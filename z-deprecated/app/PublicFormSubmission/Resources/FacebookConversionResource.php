<?php

namespace App\PublicFormSubmission\Resources;

use App\Models\Lead;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Lead
 */
class FacebookConversionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'lead'        => [
                'email'           => $this->email ?? null,
                'phone'           => $this->phone ?? null,
                'clientIp'        => $this->leadTracking->origin_ip ?? null,
                'clientUserAgent' => $this->leadTracking->user_agent ?? null,
                'firstName'       => $this->first_name ?? null,
                'lastName'        => $this->last_name ?? null,
                'externalId'      => $this->uuid ?? null,
                'fbcCookie'       => $this->leadTracking->params['fbcCookie'] ?? null,
                'fbpCookie'       => $this->leadTracking->params['fbpCookie'] ?? null,
            ],
            'serverEvent' => [
                'actionSource'   => 'website',
                'eventId'        => generateRandom('lud', 25),
                'eventName'      => 'Lead',
                'eventTime'      => now()->timestamp,
                'eventSourceUrl' => $request->header('referer') ?? 'https://1nhealth.com',
                'optOut'         => false,
            ],
            'customData'  => [],
        ];
    }
}
