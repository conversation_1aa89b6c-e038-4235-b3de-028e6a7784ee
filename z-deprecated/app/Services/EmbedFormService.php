<?php

namespace App\Services;

use App\Models\CampaignForm;
use App\Models\LeadTracking;
use App\Modules\MarketingServices\Models\Ad;
use App\Modules\MarketingServices\Models\AdCampaign;
use App\Modules\MarketingServices\Models\AdGroup;
use App\Modules\MarketingServices\Models\AdPlatform;
use Illuminate\Http\Request;

/**
 * @deprecated
 * TODO: NM - Needs to be deleted after updated Form Builder release
 */
class EmbedFormService
{
    public function getFormInfo(Request $request, CampaignForm $campaignForm, array $utms): LeadTracking
    {
        $ad_platform_id = null;
        $ad_campaign_id = null;
        $ad_group_id    = null;
        $ad_id          = null;

        if (! empty($request->utm_source)) {
            $ad_platform_id = AdPlatform::where('platform', 'like', str_replace('-2', '', $request->utm_source))
                ->orWhere('utm_slug', str_replace('-2', '', $request->utm_source))
                ->value('id');
        }

        if ($ad_platform_id && ! empty($request->utm_campaign)) {
            $ad_campaign_id = AdCampaign::where([
                'ad_platform_id' => $ad_platform_id,
                'study_id'       => $campaignForm->study_id,
            ])
                ->where(function ($query) use ($request) {
                    $query->where('uuid', $request->utm_campaign)
                        ->orWhere('utm_slug', $request->utm_campaign);
                })
                ->value('id');
        }

        if (! empty($request->utm_medium)) {
            $ad_group = AdGroup::where(function ($query) use ($request) {
                $query->where('uuid', $request->utm_medium)
                    ->orWhere('utm_slug', $request->utm_medium);
            })
                ->when($ad_campaign_id, function ($query) use ($ad_campaign_id) {
                    $query->where('ad_campaign_id', $ad_campaign_id);
                })
                ->first();

            $ad_group_id = $ad_group?->id;

            if ($ad_group && ! $ad_campaign_id) {
                $ad_campaign_id = $ad_group->ad_campaign_id;
            }
        }

        if (! empty($request->utm_content)) {
            $ad = Ad::where(function ($query) use ($request) {
                $query->where('uuid', $request->utm_content)
                    ->orWhere('utm_slug', $request->utm_content);
            })
                ->when($ad_group_id, function ($query) use ($ad_group_id) {
                    $query->where('ad_group_id', $ad_group_id);
                })
                ->first();

            $ad_id = $ad?->id;

            if ($ad && ! $ad_group_id) {
                $ad_group_id = $ad->ad_group_id;
            }
        }

        return LeadTracking::create(
            [
                'campaign_form_id' => $campaignForm->id,
                'ad_campaign_id'   => $ad_campaign_id,
                'ad_platform_id'   => $ad_platform_id,
                'ad_group_id'      => $ad_group_id,
                'ad_id'            => $ad_id,
                'origin_ip'        => $request->clientIp,
                'user_agent'       => $request->userAgent(),
                'lang'             => $request->lang ?? 'en',
                'utm'              => collect($request->all())
                    ->filter(fn ($value, $key) => in_array($key, $utms))
                    ->mapWithKeys(fn ($value, $key) => [str_replace('utm_', '', $key) => $value]),
            ],
        )->refresh();
    }
}
