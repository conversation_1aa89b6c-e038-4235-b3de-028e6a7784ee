<?php

namespace App\Mixins;

use App\Models\User;
use <PERSON><PERSON>\Dusk\Browser;
use PHPUnit\Framework\Assert as PHPUnit;

/**
 * @mixin Browser
 */
class BrowserMixin
{
    public function assertTenantAuthUserAs()
    {
        return function (User $tenantUser, ?string $domain = null) {
            $response              = $this->currentAuthAndTenantUser($domain);
            $tenantAuthUser        = $response['tenantAuthUser'] ?? null;
            $requestTenantAuthUser = $response['requestTenantAuthUser'] ?? null;

            PHPUnit::assertNotNull($tenantAuthUser, 'The current tenant user is not set.');
            PHPUnit::assertNotNull($requestTenantAuthUser, 'The current tenant user does not exist on the request object.');

            PHPUnit::assertSame(
                $tenantAuthUser['id'], $tenantUser->id,
                'The currently tenant user does not have the same primary key as the expected user',
            );

            PHPUnit::assertSame(
                $tenantAuthUser['uuid'], $tenantUser->uuid,
                'The currently tenant user does not have the same uuid as the expected user',
            );

            PHPUnit::assertSame(
                $tenantAuthUser['id'], $requestTenantAuthUser['id'],
                'The currently request tenant user does not have the same primary key as the expected user',
            );

            PHPUnit::assertSame(
                $tenantAuthUser['uuid'], $requestTenantAuthUser['uuid'],
                'The currently request tenant user does not have the same uuid as the expected user',
            );

            return $this;
        };
    }

    protected function currentAuthAndTenantUser()
    {
        return function (?string $domain = null) {
            $tenantUrl = $domain ? tenant_route($domain, 'dusk.auth.tenant') : null;
            $response  = $this->visit($tenantUrl ?? route('dusk.auth.user'));

            return json_decode(strip_tags($response->driver->getPageSource()), true);
        };
    }
}
