<?php

namespace App\Http\Requests;

use App\Concerns\ApiResponserTrait;
use App\Enums\LanguageEnum;
use App\Models\CampaignForm;
use App\Rules\AnswerSchema;
use App\Rules\ExistsFor;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class EmbedFormSubmitRequest extends FormRequest
{
    use ApiResponserTrait;

    private CampaignForm $campaignForm;

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $isPublicForm = $this->route()->named('embed-form.store');
        $trackingRule = $isPublicForm ? ['trackingId' => ['required', 'exists:leadTrackings,uuid']] : [];

        return array_merge(
            $trackingRule,
            [
                'answers'    => ['required', new AnswerSchema],
                'bestTime'   => ['nullable', 'string'],
                'clientIp'   => ['nullable', 'string'],
                'email'      => ['sometimes', 'required', 'email'],
                'fbcCookie'  => ['nullable', 'string'],
                'fbpCookie'  => ['nullable', 'string'],
                'formId'     => ['required', new ExistsFor(CampaignForm::query(), 'form_public')],
                'firstName'  => ['sometimes', 'required'],
                'isTest'     => ['boolean'],
                'lastName'   => ['sometimes', 'required'],
                'lang'       => ['required'],
                'phone'      => ['sometimes', 'required'],
                'trackingId' => ['nullable', 'string'],
            ],
        );
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'answers.required'    => 'There seems to be a problem with this submission',
            'first_name.required' => 'The first name field is required.',
            'last_name.required'  => 'The last name field is required.',
            'phone.required'      => 'The phone field is required.',
            'email.required'      => 'The email field is required.',
            'formId.required'     => "We can't find the form",
            'trackingId.required' => "We can't find the tracking id",
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $answers      = collect($this->answers);
        $campaignForm = CampaignForm::where('form_public', $this->formId)
            ->with('questions.questionType')
            ->first();

        $predefinedAnswers = $campaignForm->questions
            ->mapWithKeys(function ($question) use ($answers) {
                if ($question->is_predefined && $answer = $answers->where('question_uuid', $question->uuid)->first()) {
                    return [$question->questionType->name => $answer['answer']];
                }

                return [];
            })
            ->filter()
            ->toArray();

        $this->merge(array_merge([
            'answer_schema' => $campaignForm->answer_schema,
            'isTest'        => $this->is_test,
            'lang'          => $this->lang ?? LanguageEnum::en()->value,
        ], $predefinedAnswers));
    }

    //    protected function isLeadNew($addFields): array
    //    {
    //        $addFields['checkForDuplicates'] = false;
    //
    //        /** If we have first name, last name, email or phone, we need to check for duplicates */
    //        if (! empty($addFields['initLeadData'])) {
    //            $addFields['checkForDuplicates'] = true;
    //        }
    //
    //        if (! empty($addFields['campaign_form'])) {
    //            $addFields['initLeadData']['campaign_form_id'] = $addFields['campaign_form']->id;
    //            $addFields['initLeadData']['study_id']         = $addFields['campaign_form']->study_id;
    //        }
    //
    //        return $addFields;
    //    }

    /*protected function getCampaignForm(): array
    {
        if (empty($this->formId)) {
            return [];
        }

        $campaignForm = CampaignForm::query()
            ->where('form_public', $this->formId)
            ->with([
                'study',
                'questions' => fn ($query) => $query
                    ->with('questionType')
                    ->orderBy('order_by'),
            ])
            ->first();

        $this->campaignForm = $campaignForm;

        return ['campaign_form' => $campaignForm];
    }*/

    //    protected function getPredefinedFields(): array
    //    {
    //        $addFields     = [];
    //        $replaceFields = [
    //            'firstName' => 'first_name',
    //            'lastName'  => 'last_name',
    //            'bestTime'  => 'best_time',
    //        ];
    //        $checkDupsFields = [
    //            'first_name',
    //            'last_name',
    //            'email',
    //            'phone',
    //        ];
    //
    //        foreach ($this->answers as $answer) {
    //            $question = $this->campaignForm->questions->firstWhere('uuid', $answer['question_uuid']);
    //
    //            if ($question->is_predefined && isset($answer['answer'])) {
    //                $questionTypeName      = $question->questionType->name;
    //                $fieldName             = $replaceFields[$questionTypeName] ?? $questionTypeName;
    //                $addFields[$fieldName] = $answer['answer'];
    //
    //                if (in_array($fieldName, $checkDupsFields)) {
    //                    $addFields['initLeadData'][$fieldName] = $answer['answer'];
    //                }
    //
    //                if ($fieldName == 'phone') {
    //                    $addFields['orInitLeadData'][$fieldName] = $answer['answer'];
    //                }
    //
    //                if ($fieldName == 'email') {
    //                    $addFields['orInitLeadData'][$fieldName] = $answer['answer'];
    //                }
    //            }
    //        }
    //
    //        return $addFields;
    //    }

    /**
     * @throws HttpResponseException
     */
    protected function failedValidation(Validator $validator): void
    {
        $response = [
            'message'     => 'Oops, Something Went Wrong',
            'description' => 'There was an issue submitting your request',
            'errors'      => $validator->errors()->getMessages(),
        ];

        $resp = $this->errorResponse($response, 200);

        throw new HttpResponseException($resp);
    }
}
