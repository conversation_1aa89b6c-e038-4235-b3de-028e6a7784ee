<?php

namespace App\Http\Requests;

use App\Concerns\ApiResponserTrait;
use App\Rules\VerifyUniqueQuestionIds;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Arr;

class QuestionBuilderSaveRequest extends FormRequest
{
    use ApiResponserTrait;

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'thank_you_url_qualified'    => ['required'],
            'thank_you_url_disqualified' => ['required'],
            'form_public'                => ['required'],
            'uuid'                       => ['string'],
            'languageCode'               => ['string'],
            'answer_schema'              => ['array'],
            'settings'                   => ['array'],
            'questions'                  => [new VerifyUniqueQuestionIds],
            'calendly_event_type'        => ['array', 'nullable'],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'thank_you_url_qualified.required'    => 'Qualified thank you url is required.',
            'thank_you_url_disqualified.required' => 'Disqualified thank you url is required.',
            'form_public.required'                => 'Public form id is required.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $formPublic = $this->campaignForm->form_public ?? (new \Hidehalo\Nanoid\Client())->generateId($size = 21);

        $thankYouUrlQualified    = $this->thankYouUrlQualified;
        $thankYouUrlDisqualified = $this->thankYouUrlDisqualified;

        $thankYouUrlQualified[$this->languageCode]    = $this->thankYouUrlQualifiedValue;
        $thankYouUrlDisqualified[$this->languageCode] = $this->thankYouUrlDisqualifiedValue;
        $calendlyEventType                            = $this->calendlyEventType ? Arr::except($this->calendlyEventType, ['label', 'value']) : null;

        $this->merge([
            'answer_schema'              => $this->answerSchema ?? null,
            'form_public'                => $formPublic,
            'thank_you_url_qualified'    => $thankYouUrlQualified ?? null,
            'thank_you_url_disqualified' => $thankYouUrlDisqualified ?? null,
            'calendly_event_type'        => $calendlyEventType,
        ]);
    }

    /**
     * @throws HttpResponseException
     */
    protected function failedValidation(Validator $validator): void
    {
        $response = $this->errorResponse([
            'message'     => 'Oops, Something Went Wrong',
            'description' => 'The given data is invalid',
            'errors'      => $validator->errors()->getMessages(),
        ], 200);

        throw new HttpResponseException($response);
    }
}
