<?php

namespace App\Http\Resources;

use App\Concerns\FlattenQuestionsAnswers;
use App\Models\CampaignForm;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin CampaignForm
 */
class EmbedFormResource extends JsonResource
{
    use FlattenQuestionsAnswers;

    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $languages = $this->getQuestionLanguages($this->questionsForBuilder->toArray());

        return [
            'formId'    => $this->form_public,
            'languages' => $languages,
            'questions' => (new QuestionCollection($this->questionsForBuilder))->resolve(),
            'settings'  => $this->settings ?? new \stdClass(),
        ];
    }
}
