<?php

namespace App\Http\Resources;

use App\Concerns\FlattenQuestionsAnswers;
use App\Enums\CalendarEngineProviderEnum;
use App\Enums\LanguageEnum;
use App\Models\CampaignForm;
use App\Models\QuestionType;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin CampaignForm
 */
class QuestionBuilderResource extends JsonResource
{
    use FlattenQuestionsAnswers;

    /** @var Collection<QuestionType> */
    public Collection $questionTypes;

    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        $languages = $this->getQuestionLanguages($this->questionsForBuilder->toArray());
        $english   = LanguageEnum::en();

        $selectedLanguage = [
            'label' => $english->label,
            'value' => $english->value,
        ];

        $calendlyEventType                = $this->calendly_event_type ? (new SelectListForCalendlyEventTypesResource($this->calendly_event_type))->resolve() : null;
        $calendarEngineIntegrationEnabled = $this->study->calendarEngineIntegrationEnabled(
            CalendarEngineProviderEnum::calendly(),
        );

        return [
            'calendarEngineIntegrationEnabled' => $calendarEngineIntegrationEnabled,
            'calendlyEventType'                => $calendlyEventType,
            'campaignName'                     => $this->campaign_name,
            'doNotSubmit'                      => ['selectLanguage' => $selectedLanguage],
            'formPublic'                       => $this->form_public,
            'languageCode'                     => $english->value,
            'languages'                        => $languages,
            'questions'                        => (new QuestionCollection($this->questionsForBuilder))->resolve(),
            'questionTypes'                    => (new QuestionTypeCollection($this->questionTypes))->resolve(),
            'settings'                         => $this->settings ?? new \stdClass(),
            'showInternal'                     => $this->show_internal,
            'sites'                            => $this->acc_sites,
            'studyName'                        => $this->study->study_name,
            'studyUUID'                        => $this->study->uuid,
            'thankYouUrlQualified'             => $this->thank_you_url_qualified,
            'thankYouUrlDisqualified'          => $this->thank_you_url_disqualified,
            'uuid'                             => $this->uuid,
        ];
    }

    /**
     * @param Collection<QuestionType> $questionTypes
     */
    public function setQuestionTypes(Collection $questionTypes): QuestionBuilderResource
    {
        $this->questionTypes = $questionTypes;

        return $this;
    }
}
