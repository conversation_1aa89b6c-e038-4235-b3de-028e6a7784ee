<?php

namespace App\Http\Controllers\Admin;

use App\Concerns\ApiResponserTrait;
use App\Http\Controllers\Controller;
use App\Http\Requests\QuestionBuilderSaveRequest;
use App\Http\Resources\QuestionBuilderResource;
use App\Models\CampaignForm;
use App\Models\QuestionType;
use App\Services\CampaignFormService;
use App\Services\FormatSaveQuestions;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;
use Inertia\Response as InertiaResponse;

class QuestionBuilderController extends Controller
{
    use ApiResponserTrait;

    /**
     * Get initial data for the Question Builder
     *
     * @throws AuthorizationException
     */
    public function edit(CampaignForm $campaignForm): InertiaResponse
    {
        $this->authorize('editQuestionBuilder');

        $campaignForm
            ->load([
                'study'       => function ($q) {
                    $q->withTrashed();
                },
                'study.sites' => function ($q) {
                    $q->withTrashed();
                },
                'questionsForBuilder',
            ]);

        $questionTypes = QuestionType::orderBy('alias')
            ->where('name', '<>', 'divider')
            ->get();

        return Inertia::render('QuestionBuilder/Edit', [
            'entity' => (new QuestionBuilderResource($campaignForm))->setQuestionTypes($questionTypes)->resolve(),
        ]);
    }

    /**
     * @throws AuthorizationException
     */
    public function update(QuestionBuilderSaveRequest $request, CampaignForm $campaignForm): JsonResponse
    {
        $this->authorize('editQuestionBuilder');

        $campaignForm->fill($request->validated())->save();
        (new FormatSaveQuestions($campaignForm->id, $request->questions));

        $campaignForm = CampaignForm::where(['id' => $campaignForm->id])
            ->with([
                'study'       => function ($q) {
                    $q
                        ->withTrashed();
                },
                'study.sites' => function ($q) {
                    $q
                        ->withTrashed();
                },
            ])
            ->first();

        $questionTypes = QuestionType::orderBy('alias')
            ->where('name', '<>', 'divider')
            ->get();

        $response = [
            'message'     => 'Question Builder Saved!',
            'description' => 'The question builder has been saved',
            'entity'      => (new QuestionBuilderResource($campaignForm))->setQuestionTypes($questionTypes)->resolve(),
        ];

        return $this->successResponse($response, 200);
    }

    public function copyToAnotherForm(CampaignForm $sourceCampaignForm, CampaignForm $targetCampaignForm, CampaignFormService $campaignFormService): JsonResponse
    {
        $this->authorize('editQuestionBuilder');

        try {
            $campaignFormService->copyQuestions($sourceCampaignForm, $targetCampaignForm);

            return $this->successResponse([
                'message'     => 'Questions Copied',
                'description' => 'The questions were successfully copied.',
                'uuid'        => $targetCampaignForm->uuid,
            ]);
        } catch (\Exception $e) {
            logger($e);
            report($e);
            app('sentry')->captureException($e);

            return $this->errorResponse([
                'message'     => 'Oops, Something Went Wrong',
                'description' => 'There was a problem duplicating the campaign form. The team has been notified of this issue.',
            ], 200);
        }
    }
}
