<?php

namespace App\Http\Controllers;

use App\Concerns\ApiResponserTrait;
use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Http\Requests\EmbedFormSubmitRequest;
use App\Http\Resources\EmbedFormResource;
use App\Models\CampaignForm;
use App\Modules\Automation\Events\PreScreenerSubmittedEvent;
use App\PublicFormSubmission\Actions\EmbedForm\DispatchStaticEvents;
use App\PublicFormSubmission\Actions\EmbedForm\RegisterStaticListeners;
use App\PublicFormSubmission\Actions\EmbedForm\SaveAndSetReferralIfNotExists;
use App\PublicFormSubmission\Actions\EmbedForm\SetRedirectUrls;
use App\PublicFormSubmission\Actions\EmbedForm\SetReferralIfExists;
use App\PublicFormSubmission\Actions\EmbedForm\SetSiteId;
use App\PublicFormSubmission\Actions\EmbedForm\SetWorkflow;
use App\PublicFormSubmission\Actions\EmbedForm\ThrowExceptionIfInternalForm;
use App\PublicFormSubmission\Actions\EmbedForm\UpdateLeadTracking;
use App\PublicFormSubmission\Actions\FormatAndSaveAnswers;
use App\PublicFormSubmission\Actions\SaveLeadSituations;
use App\PublicFormSubmission\Actions\SetCampaignForm;
use App\PublicFormSubmission\Actions\SituationTreeWalker;
use App\Services\EmbedFormService;
use Barryvdh\Debugbar\Facades\Debugbar;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Pipeline;
use Inertia\Inertia;
use Inertia\Response as InertiaResponse;

class EmbedFormController extends Controller
{
    use ApiResponserTrait;

    private $utms = ['utm_campaign', 'utm_content', 'utm_medium', 'utm_source', 'utm_term'];

    public function index(string $formId, Request $request, EmbedFormService $embedFormService): InertiaResponse
    {
        DebugBar::disable();
        // TODO: Return early when we don't have a formId 4.0

        /** @var CampaignForm $campaignForm */
        $campaignForm = CampaignForm::query()
            ->where('form_public', $request->formId)
            ->with('questionsForBuilder')
            ->first();
        $leadTracking = $embedFormService->getFormInfo($request, $campaignForm, $this->utms);

        return Inertia::render('EmbedForm/DisplayForm', [
            'formId'        => $formId,
            'utms'          => collect($request->all())->filter(fn ($value, $key) => in_array($key, $this->utms)),
            'lang'          => $request->lang,
            'embedFormData' => Inertia::lazy(fn () => (new EmbedFormResource($campaignForm))->resolve() + ['trackingId' => $leadTracking->uuid]),
        ]);
    }

    public function formInfo(Request $request, EmbedFormService $embedFormService): JsonResponse
    {
        if (! $request->formId) {
            return $this->errorResponse([
                'message'     => 'Oops, Something Went Wrong',
                'description' => "Looks like we can't find this form",
            ], 200);
        }

        $campaignForm = CampaignForm::query()
            ->where('form_public', $request->formId)
            ->with('questionsForBuilder')
            ->first();

        if (! $campaignForm) {
            return $this->errorResponse([
                'message'     => 'Oops, Something Went Wrong',
                'description' => "Looks like we can't find this form",
            ], 200);
        }

        $leadTracking = $embedFormService->getFormInfo($request, $campaignForm, $this->utms);

        return $this->successResponse([
            'message'     => 'Campaign Form Initialized!',
            'description' => 'The campaign form has been initialized',
            'embedded'    => array_merge((new EmbedFormResource($campaignForm))->resolve(), [
                'leadTracking' => $leadTracking->uuid,
            ]),
        ]);
    }

    public function store(EmbedFormSubmitRequest $request): JsonResponse
    {
        $campaignFormSubmissionDto = CampaignFormSubmissionDto::from($request->validated());

        /** @var CampaignFormSubmissionDto $campaignFormSubmissionDto */
        $campaignFormSubmissionDto = Pipeline::send($campaignFormSubmissionDto)
            ->through([
                SetCampaignForm::class,
                ThrowExceptionIfInternalForm::class,
                SetSiteId::class,
                SetWorkflow::class,
                SetReferralIfExists::class,
                SituationTreeWalker::class,
                SaveAndSetReferralIfNotExists::class,
                SaveLeadSituations::class,
                UpdateLeadTracking::class,
                FormatAndSaveAnswers::class,
                SetRedirectUrls::class,
                RegisterStaticListeners::class,
                DispatchStaticEvents::class,
                function (CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next) {
                    $campaignForm = $campaignFormSubmissionDto->campaignForm;
                    $referral     = $campaignFormSubmissionDto->referral;

                    PreScreenerSubmittedEvent::dispatch(
                        $referral->id,
                        $campaignForm->id,
                        $campaignFormSubmissionDto->isNewReferral,
                        $referral->campaign_form_id === $campaignForm->id,
                    );

                    return $next($campaignFormSubmissionDto);
                },
            ])
            ->thenReturn();

        return $this->successResponse([
            'message'     => 'Referral Added Successfully',
            'description' => 'The referral was added',
            'redirect'    => $campaignFormSubmissionDto->referral->situations->isNotEmpty()
                ? $campaignFormSubmissionDto->qualifiedUrl
                : $campaignFormSubmissionDto->disqualifiedUrl,
        ]);
    }
}
