<?php

namespace App\Http\Controllers;

use App\Concerns\ApiResponserTrait;
use App\Concerns\FlattenQuestionsAnswers;
use App\DataTransferObjects\CampaignFormSubmissionDto;
use App\Http\Requests\LeadAnswerSubmitRequest;
use App\Http\Resources\EmbedFormResource;
use App\Http\Resources\LeadAnswerCollection;
use App\Http\Resources\LeadResource;
use App\Models\CampaignForm;
use App\Models\Lead;
use App\Models\LeadAnswer;
use App\Models\Situation;
use App\Modules\Automation\Events\SecondaryScreenerSubmittedEvent;
use App\PublicFormSubmission\Actions\FormatAndSaveAnswers;
use App\PublicFormSubmission\Actions\SaveLeadSituations;
use App\PublicFormSubmission\Actions\SecondaryScreener\CreateOrUpdateAdditionalCampaignForm;
use App\PublicFormSubmission\Actions\SecondaryScreener\DeleteLeadSituations;
use App\PublicFormSubmission\Actions\SetCampaignForm;
use App\PublicFormSubmission\Actions\SituationTreeWalker;
use App\Services\LeadService;
use Closure;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Pipeline;
use Inertia\Inertia;
use Inertia\Response as InertiaResponse;

class LeadAnswerController extends Controller
{
    use ApiResponserTrait;
    use FlattenQuestionsAnswers;

    public function create(Request $request, Lead $leadLimitByUser, CampaignForm $campaignForm, LeadService $leadService): InertiaResponse|RedirectResponse
    {
        $this->authorize('viewReferralPhi');
        $this->authorize('viewReferralPii');

        $leadAnswerCount = $leadLimitByUser->leadAnswers()
            ->whereNull('parent_id')
            ->whereHas('question', function ($query) use ($campaignForm) {
                $query->where('campaign_form_id', $campaignForm->id);
            });

        if ($leadAnswerCount->exists()) {
            return redirect()->route('referral-answers.show', ['leadLimitByUser' => $leadLimitByUser->uuid, 'campaignFormForLead' => $campaignForm->uuid]);
        }

        $lead = $leadService->getLeadForManagePage($leadLimitByUser);

        $campaignForm->load('questionsForBuilder');

        $additionalCampaignForms = $leadService->additionalCampaignFormsForLead($lead);

        return Inertia::render('ReferralAnswers/Create', [
            'additionalCampaignForms' => $additionalCampaignForms->map->only(['uuid', 'campaign_name', 'is_editable', 'form_public', 'hasAnswers', 'qualifiedStatus']),
            'campaignForm'            => $campaignForm->only(['uuid', 'campaign_name', 'is_editable', 'form_public', 'hasAnswers', 'qualifiedStatus']),
            'embedFormData'           => (new EmbedFormResource($campaignForm))->resolve(),
            'formId'                  => $campaignForm->form_public,
            'initialFormValues'       => new \stdClass(),
            'lang'                    => $request->lang,
            'referral'                => (new LeadResource($lead))->resolve(),
        ]);
    }

    public function store(LeadAnswerSubmitRequest $request, Lead $leadLimitByUser): JsonResponse
    {
        $leadLimitByUser->load(['study', 'campaignForm']);

        $campaignFormSubmissionDto = CampaignFormSubmissionDto::from(array_merge(
            $request->validated(),
            ['referral' => $leadLimitByUser, 'isNewReferral' => false],
        ));

        /** @var CampaignFormSubmissionDto $campaignFormSubmissionDto */
        $campaignFormSubmissionDto = Pipeline::send($campaignFormSubmissionDto)
            ->through([
                SetCampaignForm::class,
                SituationTreeWalker::class,
                CreateOrUpdateAdditionalCampaignForm::class,
                SaveLeadSituations::class,
                FormatAndSaveAnswers::class,
                function (CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next) {
                    SecondaryScreenerSubmittedEvent::dispatch(
                        $campaignFormSubmissionDto->referral->id,
                        $campaignFormSubmissionDto->campaignForm->id,
                    );

                    return $next($campaignFormSubmissionDto);
                },
            ])
            ->thenReturn();

        return $this->successResponse([
            'message'          => 'Secondary Screener Saved',
            'description'      => 'The form was saved successfully',
            'campaignFormUUID' => $campaignFormSubmissionDto->campaignForm->uuid,
        ]);
    }

    public function edit(Request $request, Lead $leadLimitByUser, CampaignForm $campaignForm, LeadService $leadService): InertiaResponse
    {
        $this->authorize('viewReferralPhi');
        $this->authorize('viewReferralPii');

        $lead = $leadService->getLeadForManagePage($leadLimitByUser);

        $lead
            ->load([
                'leadAnswers' => function (HasMany $q) use ($campaignForm) {
                    $q
                        ->whereNull('parent_id')
                        ->whereHas('question', function ($query) use ($campaignForm) {
                            $query->where('questions.campaign_form_id', $campaignForm->id);
                        })
                        ->with(['children', 'question', 'questionType'])
                        ->orderBy('order_by');
                },
            ]);

        $campaignForm->load('questionsForBuilder');

        /** We have to flatten the answers first to account for child questions */
        $flattenedLeadAnswers = $this->flattenAnswersFromCollection($lead->leadAnswers, false, true);

        /** Initial values populated on the screener form */
        $initialFormValues = collect($flattenedLeadAnswers)->mapWithKeys(function (LeadAnswer $answer) {
            return [$answer->question->uuid => $answer->formatForInitialFormValues()];
        });

        $additionalCampaignForms = $leadService->additionalCampaignFormsForLead($lead);

        $additionalCampaignFormLead = $campaignForm->additionalCampaignFormLeads()->where('lead_id', $lead->id)->first();

        return Inertia::render('ReferralAnswers/Edit', [
            'additionalCampaignForms' => $additionalCampaignForms->map->only(['uuid', 'campaign_name', 'is_editable', 'form_public', 'hasAnswers', 'qualifiedStatus', 'is_draft']),
            'campaignForm'            => $campaignForm->only(['uuid', 'campaign_name', 'form_public', 'is_editable', 'hasAnswers', 'qualifiedStatus']),
            'embedFormData'           => (new EmbedFormResource($campaignForm))->resolve(),
            'formId'                  => $campaignForm->form_public,
            'initialFormValues'       => $initialFormValues->isNotEmpty() ? $initialFormValues : new \stdClass(),
            'lang'                    => $request->lang,
            'referral'                => (new LeadResource($lead))->resolve(),
            'isDraft'                 => $additionalCampaignFormLead && $additionalCampaignFormLead->is_draft,
        ]);
    }

    public function update(LeadAnswerSubmitRequest $request, Lead $leadLimitByUser): JsonResponse
    {
        $leadLimitByUser->load(['study', 'campaignForm']);

        $campaignFormSubmissionDto = CampaignFormSubmissionDto::from(array_merge(
            $request->validated(),
            ['referral' => $leadLimitByUser, 'isNewReferral' => false],
        ));

        /** @var CampaignFormSubmissionDto $campaignFormSubmissionDto */
        $campaignFormSubmissionDto = Pipeline::send($campaignFormSubmissionDto)
            ->through([
                SetCampaignForm::class,
                DeleteLeadSituations::class,
                SituationTreeWalker::class,
                CreateOrUpdateAdditionalCampaignForm::class,
                SaveLeadSituations::class,
                FormatAndSaveAnswers::class,
                function (CampaignFormSubmissionDto $campaignFormSubmissionDto, Closure $next) {
                    SecondaryScreenerSubmittedEvent::dispatch(
                        $campaignFormSubmissionDto->referral->id,
                        $campaignFormSubmissionDto->campaignForm->id,
                    );

                    return $next($campaignFormSubmissionDto);
                },
            ])
            ->thenReturn();

        return $this->successResponse([
            'message'          => 'Secondary Screener Updated',
            'description'      => 'The form was updated successfully',
            'campaignFormUUID' => $campaignFormSubmissionDto->campaignForm->uuid,
        ]);
    }

    /**
     * Get Lead List table data for Lead List page
     */
    public function show(Lead $leadLimitByUser, CampaignForm $campaignForm, LeadService $leadService): InertiaResponse|RedirectResponse
    {
        $this->authorize('viewReferralPhi');
        $this->authorize('viewReferralPii');

        $lead        = $leadService->getLeadForManagePage($leadLimitByUser);
        $leadAnswers = $lead->leadAnswers()
            ->whereNull('parent_id')
            ->with(LeadAnswer::eagerLoadRelationshipsForReferral())
            ->whereHas('question', function ($query) use ($campaignForm) {
                $query->where('campaign_form_id', $campaignForm->id);
            })
            ->orderBy('order_by')
            ->get();

        if ($leadAnswers->isEmpty() && $campaignForm->id !== $lead->campaign_form_id) {
            return redirect()->route('referral-answers.create', ['leadLimitByUser' => $lead->uuid, 'campaignFormForLead' => $campaignForm->uuid]);
        }

        $campaignForm
            ->loadCount([
                'leadSituations as qualifiedStatus' => function ($query) use ($lead) {
                    $query->where('lead_situation.lead_id', $lead->id);
                },
                'questions as hasAnswers'           => function ($query) use ($lead) {
                    $query->whereHas('leadAnswers', fn ($query) => $query->where('lead_answers.lead_id', $lead->id));
                },
            ]);

        $additionalCampaignForms = $leadService->additionalCampaignFormsForLead($lead);

        $qualificationBuckets = Situation::query()
            ->where('situations.campaign_form_id', $campaignForm->id)
            ->withCount([
                'leadSituations as qualifiedStatus' => function ($query) use ($lead) {
                    $query->where('lead_situation.lead_id', $lead->id);
                },
            ])
            ->get();

        $additionalCampaignFormLead = $campaignForm->additionalCampaignFormLeads()->where('lead_id', $lead->id)->first();

        return Inertia::render('ReferralAnswers/Show', [
            'additionalCampaignForms'  => $additionalCampaignForms->map->only(['uuid', 'campaign_name', 'is_editable', 'form_public', 'hasAnswers', 'qualifiedStatus', 'is_draft']),
            'campaignForm'             => $campaignForm->only(['uuid', 'campaign_name', 'is_editable', 'form_public', 'hasAnswers', 'qualifiedStatus']),
            'leadAnswers'              => (new LeadAnswerCollection($leadAnswers))->resolve(),
            'referral'                 => (new LeadResource($lead))->resolve(),
            'isAdditionalCampaignForm' => $lead->campaign_form_id !== $campaignForm->id,
            'isDraft'                  => $additionalCampaignFormLead && $additionalCampaignFormLead->is_draft,
            'qualificationBuckets'     => $qualificationBuckets->map(fn ($bucket) => [
                'value'           => $bucket->uuid,
                'label'           => $bucket->name,
                'qualifiedStatus' => $bucket->qualifiedStatus,
            ]),
        ]);
    }
}
