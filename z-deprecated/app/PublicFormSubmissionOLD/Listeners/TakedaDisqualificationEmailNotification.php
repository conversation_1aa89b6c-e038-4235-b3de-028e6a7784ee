<?php

namespace App\PublicFormSubmission\Listeners;

use App\PublicFormSubmission\Events\DisqualificationEmailNotificationEvent;
use App\PublicFormSubmission\Notifications\TakedaLeadDisqualifiedConfirmation;

class TakedaDisqualificationEmailNotification
{
    public function handle(DisqualificationEmailNotificationEvent $event): void
    {
        $lead = $event->lead;

        if ($lead->isDisqualified($lead->campaignForm) && ! empty($lead->email) && ! empty($event->requestData['isNewLead'])) {
            $lead->notify(new TakedaLeadDisqualifiedConfirmation(
                ! empty($lead->acc_lead_name) ? $lead->acc_lead_name : 'Participant',
                ! empty($lead->lang) ? $lead->lang : 'en',
                $lead->campaignForm,
            ));
        }
    }
}
