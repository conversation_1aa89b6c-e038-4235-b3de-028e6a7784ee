<?php

namespace App\PublicFormSubmission\Listeners;

use App\Models\TakedaLink;
use App\PublicFormSubmission\Events\QualificationEmailNotificationEvent;
use App\Services\ChatbotService;
use AshAllenDesign\ShortURL\Facades\ShortURL;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Lang;

class TakedaQualificationSmsNotification implements ShouldQueue
{
    private ChatbotService $chatbotService;

    public function __construct(ChatbotService $chatbotService)
    {
        $this->chatbotService = $chatbotService;
    }

    /**
     * @throws \AshAllenDesign\ShortURL\Exceptions\ShortURLException
     * @throws \Twilio\Exceptions\ConfigurationException
     * @throws \Twilio\Exceptions\TwilioException
     */
    public function handle(QualificationEmailNotificationEvent $event): void
    {
        $lead = $event->lead;

        if (
            $lead->isQualified($lead->campaignForm) &&
            ! empty($lead->phone) &&
            ! empty($event->requestData['isNewLead']) &&
            tenant()?->getTwilioSetting()
        ) {
            $leadName      = $lead->first_name ?: 'Participant';
            $qualtricsLink = TakedaLink::where('lead_id', $lead->id)->first();

            if (! empty($qualtricsLink->link)) {
                $shortURLObject = ShortURL::destinationUrl($qualtricsLink->link)->make();
                $shortURL       = $shortURLObject->default_short_url;

                $message = Lang::get('takeda.qualified_thank_you_sms', [
                    'name'     => $leadName,
                    'shortURL' => $shortURL,
                ], $lead->lang ?: 'en');

                $this->chatbotService->sendSmsMessage(
                    $lead->uuid,
                    $message,
                    '1nHealth',
                );
            }
        }
    }
}
