<?php

namespace App\PublicFormSubmission\Listeners;

use App\PublicFormSubmission\Events\DisqualificationEmailNotificationEvent;
use App\PublicFormSubmission\Notifications\FractylLeadDisqualifiedConfirmation;

class FractylDisqualificationEmailNotification
{
    public function handle(DisqualificationEmailNotificationEvent $event): void
    {
        $lead = $event->lead;

        if ($lead->isQualified($lead->campaignForm) && ! empty($lead->email) && ! empty($event->requestData['isNewLead'])) {
            $leadName = ! empty($lead->acc_lead_name) ? $lead->acc_lead_name : 'Participant';
            $lead->notify(new FractylLeadDisqualifiedConfirmation($leadName, $lead->campaignForm));
        }
    }
}
