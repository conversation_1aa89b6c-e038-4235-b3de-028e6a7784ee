<?php

namespace App\PublicFormSubmission\Listeners;

use App\Jobs\RegisterRippleScienceParticipant;
use App\PublicFormSubmission\Events\AfterQualificationEmailNotificationEvent;

class MedRhythmsStrokeRippleIntegration
{
    public function handle(AfterQualificationEmailNotificationEvent $event): void
    {
        $lead              = $event->lead;
        $rippleIntegration = $lead->study->getRippleScienceIntegration();

        if ($rippleIntegration && ! empty($event->requestData['isNewLead'])) {
            RegisterRippleScienceParticipant::dispatch($lead);
        }
    }
}
