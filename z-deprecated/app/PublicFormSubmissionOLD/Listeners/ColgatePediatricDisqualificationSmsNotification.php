<?php

namespace App\PublicFormSubmission\Listeners;

use App\PublicFormSubmission\Events\DisqualificationEmailNotificationEvent;
use App\Services\ChatbotService;
use Illuminate\Support\Facades\Lang;

class ColgatePediatricDisqualificationSmsNotification
{
    private ChatbotService $chatbotService;

    public function __construct(ChatbotService $chatbotService)
    {
        $this->chatbotService = $chatbotService;
    }

    public function handle(DisqualificationEmailNotificationEvent $event): void
    {
        $lead = $event->lead;

        if (
            $lead->isDisqualified($lead->campaignForm) &&
            ! empty($lead->phone) &&
            ! empty($event->requestData['isNewLead']) &&
            tenant()?->getTwilioSetting()
        ) {
            $leadName = ! empty($lead->first_name) ? $lead->first_name : 'Participant';

            $message = Lang::get('colgate.pediatrics_disqualified_sms', [
                'name' => $leadName,
            ], ! empty($lead->lang) ? $lead->lang : 'en');

            $this->chatbotService->sendSmsMessage(
                $lead->uuid,
                $message,
                '1nHealth',
            );
        }
    }
}
