<?php

namespace App\PublicFormSubmission\Listeners;

use App\PublicFormSubmission\Events\QualificationEmailNotificationEvent;
use App\PublicFormSubmission\Notifications\FractylLeadQualifiedConfirmation;

class FractylQualificationEmailNotification
{
    public function handle(QualificationEmailNotificationEvent $event): void
    {
        $lead = $event->lead;

        if ($lead->isQualified($lead->campaignForm) && ! empty($lead->email) && ! empty($event->requestData['isNewLead'])) {
            $leadName = ! empty($lead->acc_lead_name) ? $lead->acc_lead_name : 'Participant';
            $lead->notify(new FractylLeadQualifiedConfirmation($leadName, $lead->campaignForm));
        }
    }
}
