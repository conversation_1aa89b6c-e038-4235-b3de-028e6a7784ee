<?php

namespace App\PublicFormSubmission\Listeners;

use App\PublicFormSubmission\Events\DisqualificationEmailNotificationEvent;
use App\Services\ChatbotService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Lang;

class CuravoShinglesDisqualificationSmsNotification implements ShouldQueue
{
    private ChatbotService $chatbotService;

    public function __construct(ChatbotService $chatbotService)
    {
        $this->chatbotService = $chatbotService;
    }

    /**
     * @throws \Twilio\Exceptions\ConfigurationException
     * @throws \Twilio\Exceptions\TwilioException
     */
    public function handle(DisqualificationEmailNotificationEvent $event): void
    {
        $lead = $event->lead;

        if (
            $lead->isDisqualified($lead->campaignForm) &&
            ! empty($lead->phone) &&
            ! empty($event->requestData['isNewLead']) &&
            tenant()?->getTwilioSetting()
        ) {
            $leadName = $lead->first_name ?: 'Participant';

            $message = Lang::get('curavo.disqualified_sms', [
                'name' => $leadName,
            ], $lead->lang ?: 'en');

            $this->chatbotService->sendSmsMessage(
                $lead->uuid,
                $message,
                '1nHealth',
            );
        }
    }
}
