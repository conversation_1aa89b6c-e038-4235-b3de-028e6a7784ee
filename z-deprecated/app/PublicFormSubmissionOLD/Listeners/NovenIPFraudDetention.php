<?php

namespace App\PublicFormSubmission\Listeners;

use App\Models\Lead;
use App\Models\LeadAnswer;
use App\Models\LeadStatus;
use App\PublicFormSubmission\Events\BeforeQualificationEmailNotificationEvent;
use Ipdata\ApiClient\Ipdata;
use <PERSON>yholm\Psr7\Factory\Psr17Factory;
use Symfony\Component\HttpClient\Psr18Client;

/**
 * @deprecated
 * TODO: NM - We're keeping this class to use as a reference to implement
 *       fraud detection throughout the system.
 */
class NovenIPFraudDetention
{
    public LeadAnswer $leadAnswer;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(BeforeQualificationEmailNotificationEvent $event): void
    {
        $requestData      = $event->requestData;
        $lead             = $event->lead;
        $this->leadAnswer = $lead->leadAnswers()
            ->whereHas('question', function ($query) {
                $query->where('questions.uuid', 'e54a4643-6272-4eb9-8d23-67cdfb3aa969');
            })
            ->first();

        if (! empty($campaignFormSubmissionDto->isNewReferral) || ! isset($this->leadAnswer->answer['ondata_eval']['validIP'])) {
            $this->evaluationThreat($event->lead);
        } elseif ($this->leadAnswer->answer['ondata_eval']['validIP'] == false) {
            request()->merge(['qualifiedUrl' => $lead->campaignForm->thank_you_url_disqualified['en']]);
        }
    }

    private function evaluationThreat(Lead $lead)
    {
        $validIP      = true;
        $httpClient   = new Psr18Client();
        $psr17Factory = new Psr17Factory();

        try {
            $apiKey = str(config('services.ipData.api_key'))->replace('"', '')->value();
            $ipData = new Ipdata(
                $apiKey,
                $httpClient,
                $psr17Factory,
            );

            $results        = $ipData->lookup($lead->origin_ip);
            $isUnitedStates = $results['country_code'] == 'US';
            $threats        = $results['threat'];
            $scores         = $threats['scores'];

            unset($threats['scores']);

            $hasThreats       = $this->hasThreats($threats);
            $failedVPN        = $this->failedVPN($scores);
            $failedProxy      = $this->failedProxy($scores);
            $failedTrustScore = $this->failedTrustScore($scores);

            if (! $isUnitedStates || $hasThreats || $failedVPN || $failedProxy || $failedTrustScore) {
                $validIP             = false;
                $leadStatusWorkflows = $lead
                    ->workflow
                    ->leadStatusWorkflows()
                    ->whereHas('leadStatus', function ($query) {
                        $query->whereIn('lead_status', ['Qualified Fraud', 'Disqualified Fraud']);
                    })
                    ->get();
                $newLeadStatusWorkflow = $leadStatusWorkflows->where('leadStatusWorkflow.lead_status', 'Disqualified Fraud')->first();

                /*$leadStatuses = LeadStatus::whereIn('lead_status', ['Qualified Fraud', 'Disqualified Fraud'])
                    ->get();
                $newLeadStatus = $leadStatuses->where('lead_status', 'Disqualified Fraud')->first();*/

                request()->merge(['qualifiedUrl' => $lead->campaignForm->thank_you_url_disqualified['en']]);

                if ($lead->isQualified($lead->campaignForm)) {
                    $newLeadStatusWorkflow = $leadStatusWorkflows->where('leadStatusWorkflow.lead_status', 'Qualified Fraud')->first();
                }

                // Update existing transition log
                $transitionLog                          = $lead->leadTransitionLogs()->oldest('transition_to_at')->first();
                $transitionLog->lead_status_workflow_id = $newLeadStatusWorkflow->id;
                $transitionLog->save();

                $lead->lead_status_workflow_id = $newLeadStatusWorkflow->id;
                $lead->save();
            }

            $results = array_merge($results, [
                'ondata_eval' => [
                    'validIP'          => $validIP,
                    'isUnitedStates'   => $isUnitedStates,
                    'hasThreats'       => $hasThreats,
                    'failedVPN'        => $failedVPN,
                    'failedProxy'      => $failedProxy,
                    'failedTrustScore' => $failedTrustScore,
                ],
            ]);

            if ($this->leadAnswer) {
                $this->leadAnswer->answer = $results;
                $this->leadAnswer->saveQuietly();
            }
        } catch (\Exception $e) {
            logger($e);
            report($e);
            app('sentry')->captureException($e);
        }
    }

    private function hasThreats(array $threats)
    {
        return collect($threats)
            ->filter(fn ($threat) => ! empty($threat))
            ->isNotEmpty();
    }

    private function failedVPN(array $scores)
    {
        return $scores['vpn_score'] >= 80;
    }

    private function failedProxy(array $scores)
    {
        return $scores['proxy_score'] >= 80;
    }

    private function failedTrustScore(array $scores)
    {
        return $scores['trust_score'] <= 50;
    }
}
