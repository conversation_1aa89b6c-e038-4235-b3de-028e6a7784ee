<?php

namespace App\PublicFormSubmission\Listeners;

use App\PublicFormSubmission\Events\QualificationEmailNotificationEvent;
use App\PublicFormSubmission\Notifications\TakedaLeadQualifiedConfirmation;

class TakedaQualificationEmailNotification
{
    public function handle(QualificationEmailNotificationEvent $event): void
    {
        $lead = $event->lead;

        if (! empty($lead->situations) && ! empty($lead->email) && ! empty($event->requestData['isNewLead'])) {
            $leadName     = ! empty($lead->first_name) ? $lead->first_name : 'Participant';
            $qualifiedUrl = $event->requestData['qualifiedUrl'];
            $lead->notify(new TakedaLeadQualifiedConfirmation($leadName, $qualifiedUrl, $lead->lang, $lead->campaignForm));
        }
    }
}
