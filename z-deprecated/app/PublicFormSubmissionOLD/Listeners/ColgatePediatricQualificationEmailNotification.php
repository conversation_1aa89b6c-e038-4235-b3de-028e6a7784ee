<?php

namespace App\PublicFormSubmission\Listeners;

use App\PublicFormSubmission\Events\QualificationEmailNotificationEvent;
use App\PublicFormSubmission\Notifications\ColgatePediatricLeadQualifiedConfirmation;

class ColgatePediatricQualificationEmailNotification
{
    public function handle(QualificationEmailNotificationEvent $event): void
    {
        $lead = $event->lead;

        if ($lead->isQualified($lead->campaignForm) && ! empty($lead->email) && ! empty($event->requestData['isNewLead'])) {
            $leadName = ! empty($lead->first_name) ? $lead->first_name : 'Participant';
            $lang     = ! empty($lead->lang) ? $lead->lang : 'en';

            $lead->notify(new ColgatePediatricLeadQualifiedConfirmation($leadName, $lang, $lead->campaignForm));
        }
    }
}
