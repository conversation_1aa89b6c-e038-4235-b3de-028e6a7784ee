<?php

namespace App\PublicFormSubmission\Listeners;

use App\PublicFormSubmission\Events\QualificationEmailNotificationEvent;
use App\PublicFormSubmission\Notifications\ImmunocareLeadQualifiedConfirmation;
use Illuminate\Contracts\Queue\ShouldQueue;

class ImmunocareQualificationEmailNotification implements ShouldQueue
{
    public function handle(QualificationEmailNotificationEvent $event): void
    {
        $lead = $event->lead;

        if ($lead->isQualified($lead->campaignForm) && ! empty($lead->email) && ! empty($event->requestData['isNewLead'])) {
            $leadName = ! empty($lead->first_name) ? $lead->first_name : 'Participant';

            $lead->notify(new ImmunocareLeadQualifiedConfirmation($leadName, $lead->campaignForm));
        }
    }
}
