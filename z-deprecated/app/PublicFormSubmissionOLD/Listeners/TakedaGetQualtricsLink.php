<?php

namespace App\PublicFormSubmission\Listeners;

use App\Models\TakedaLink;
use App\PublicFormSubmission\Events\BeforeQualificationEmailNotificationEvent;

class TakedaGetQualtricsLink
{
    public function handle(BeforeQualificationEmailNotificationEvent $event): void
    {
        $lang         = ! empty($event->lead->lang) ? $event->lead->lang : $event->requestData['lang'];
        $qualLangUrls = [
            'en' => 'https://1nhealth.com/takeda-infusion-study-thank-you-page',
            'uk' => 'https://1nhealth.com/takeda-infusion-study-thank-you-page-uk',
            'fr' => 'https://1nhealth.com/takeda-infusion-study-thank-you-page-france',
            'de' => 'https://1nhealth.com/takeda-infusion-study-thank-you-page-germany',
            'es' => 'https://1nhealth.com/takeda-infusion-study-thank-you-page-spain',
        ];
        $disQualLangUrls = [
            'en' => 'https://1nhealth.com/takeda-infusion-study-thank-you-2',
            'uk' => 'https://1nhealth.com/takeda-infusion-study-thank-you-uk',
            'fr' => 'https://1nhealth.com/takeda-infusion-study-thank-you-france',
            'de' => 'https://1nhealth.com/takeda-infusion-study-thank-you-germany',
            'es' => 'https://1nhealth.com/takeda-infusion-study-thank-you-spain',
        ];

        if (! empty($event->lead->situations) && ! empty($event->requestData['isNewLead'])) {
            $link = TakedaLink::where('lang', $lang)
                ->where('link_expiration', '>', now())
                ->whereNull('lead_id')
                ->first();

            if (! empty($link)) {
                $link->fill(['lead_id' => $event->lead->id])->save();
                // request()->merge(['qualifiedUrl' => $link->link]);
            }
        }

        if (! empty($qualLangUrls[$lang])) {
            request()->merge(['qualifiedUrl' => $qualLangUrls[$lang]]);
        }

        if (! empty($disQualLangUrls[$lang])) {
            request()->merge(['disqualifiedUrl' => $disQualLangUrls[$lang]]);
        }
    }
}
