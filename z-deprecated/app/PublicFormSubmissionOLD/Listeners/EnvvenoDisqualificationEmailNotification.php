<?php

namespace App\PublicFormSubmission\Listeners;

use App\PublicFormSubmission\Events\DisqualificationEmailNotificationEvent;
use App\PublicFormSubmission\Notifications\EnvvenoLeadDisqualifiedConfirmation;
use Illuminate\Contracts\Queue\ShouldQueue;

class EnvvenoDisqualificationEmailNotification implements ShouldQueue
{
    public function handle(DisqualificationEmailNotificationEvent $event): void
    {
        $lead = $event->lead;

        if ($lead->isDisqualified($lead->campaignForm) && ! empty($lead->email) && ! empty($event->requestData['isNewLead'])) {
            $leadName = ! empty($lead->acc_lead_name) ? $lead->acc_lead_name : 'Participant';
            $lead->notify(new EnvvenoLeadDisqualifiedConfirmation($leadName, $lead->campaignForm));
        }
    }
}
