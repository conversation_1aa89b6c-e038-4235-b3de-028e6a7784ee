<?php

namespace App\PublicFormSubmission\Listeners;

use App\PublicFormSubmission\Events\QualificationEmailNotificationEvent;
use App\PublicFormSubmission\Notifications\ExactLeadQualifiedConfirmation;

class ExactQualificationEmailNotification
{
    public function handle(QualificationEmailNotificationEvent $event): void
    {
        $lead = $event->lead;

        if (! empty($lead->situations) && ! empty($lead->email) && ! empty($event->requestData['isNewLead'])) {
            $leadName = ! empty($lead->acc_lead_name) ? $lead->acc_lead_name : 'Participant';
            $lead->notify(new ExactLeadQualifiedConfirmation($leadName, $event->requestData, $lead->campaignForm));
        }
    }
}
