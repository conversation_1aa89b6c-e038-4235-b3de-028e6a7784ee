<?php

namespace App\PublicFormSubmission\Listeners;

use App\PublicFormSubmission\Events\QualificationEmailNotificationEvent;
use App\Services\ChatbotService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Lang;
use <PERSON><PERSON><PERSON>\Exceptions\ConfigurationException;
use Twilio\Exceptions\TwilioException;

class CuravoShinglesQualificationSmsNotification implements ShouldQueue
{
    private ChatbotService $chatbotService;

    public function __construct(ChatbotService $chatbotService)
    {
        $this->chatbotService = $chatbotService;
    }

    /**
     * @throws ConfigurationException
     * @throws TwilioException
     */
    public function handle(QualificationEmailNotificationEvent $event): void
    {
        $lead = $event->lead;

        if (
            $lead->isQualified($lead->campaignForm) &&
            ! empty($lead->phone) &&
            ! empty($event->requestData['isNewLead']) &&
            tenant()?->getTwilioSetting()
        ) {
            $leadName = $lead->first_name ?: 'Participant';

            $message = Lang::get('curavo.qualified_sms', [
                'name' => $leadName,
            ], $lead->lang ?: 'en');

            $this->chatbotService->sendSmsMessage(
                $lead->uuid,
                $message,
                '1nHealth',
            );
        }
    }
}
