<?php

namespace App\PublicFormSubmission\Listeners;

use App\PublicFormSubmission\Events\BeforeQualificationEmailNotificationEvent;

class CaliwayAssignSite
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(BeforeQualificationEmailNotificationEvent $event): void
    {
        if (empty($event->lead->site_id)) {
            $site_id = match ($event->lead->campaign_form_id) {
                297     => 361, // Plugerville - Caliway to Site 205- Austin Institute for Clinical Research
                332     => 368, // Baton Rouge - Caliway to Site 212- DelRicht Research - LA Dermatology Associates
                333     => 362, // New Orleans - Caliway to Site 206- Lupo Center for Aesthetic and General Dermatology
                334     => 363, // New York - Caliway to Site 207- Lorenc Aesthetic Plastic Surgery Center
                335     => 365, // Nashville - Caliway to Site 209- Tennessee Clinical Research Centre
                336     => 360, // Omaha - Caliway to Site 204- Skin Specialist, PC
                default => null,
            };

            if ($site_id) {
                $event->lead->site_id = $site_id;
                $event->lead->save();
            }
        }
    }
}
