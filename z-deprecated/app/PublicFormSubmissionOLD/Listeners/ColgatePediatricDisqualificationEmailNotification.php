<?php

namespace App\PublicFormSubmission\Listeners;

use App\PublicFormSubmission\Events\DisqualificationEmailNotificationEvent;
use App\PublicFormSubmission\Notifications\ColgatePediatricLeadDisqualifiedConfirmation;

class ColgatePediatricDisqualificationEmailNotification
{
    public function handle(DisqualificationEmailNotificationEvent $event): void
    {
        $lead = $event->lead;

        if ($lead->isDisqualified($lead->campaignForm) && ! empty($lead->email) && ! empty($event->requestData['isNewLead'])) {
            $leadName = ! empty($lead->acc_lead_name) ? $lead->acc_lead_name : 'Participant';
            $lang     = ! empty($lead->lang) ? $lead->lang : 'en';

            $lead->notify(new ColgatePediatricLeadDisqualifiedConfirmation($leadName, $lang, $lead->campaignForm));
        }
    }
}
