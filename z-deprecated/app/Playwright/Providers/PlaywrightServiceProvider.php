<?php

namespace App\Playwright\Providers;

use App\Playwright\Console\Commands\InitializeDatabase;
use App\Playwright\Console\Commands\TruncateDatabase;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;

class PlaywrightServiceProvider extends ServiceProvider
{
    public function boot()
    {
        if (! $this->app->environment(str(config('playwright.environments'))->explode(',')->toArray())) {
            return;
        }

        $this->addRoutes();

        $this->commands([
            InitializeDatabase::class,
            TruncateDatabase::class,
        ]);
    }

    protected function addRoutes()
    {
        Route::namespace('')
            // ->middleware(config('playwright.route.middleware'))
            ->group(dirname(__DIR__, 1) . '/routes/playwright.php');
    }
}
