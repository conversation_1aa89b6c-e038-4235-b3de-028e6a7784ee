<?php

namespace App\Playwright\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\CentralUser;
use App\Models\User;
use App\Playwright\Actions\TruncateDatabase;
use App\Playwright\Seeders\Visual\SetupVisualSeeder;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Str;
use Tests\Concerns\UserRoleTrait;

class PlaywrightController extends Controller
{
    use UserRoleTrait;

    protected ?User $tenantUser = null;

    protected ?CentralUser $centralUser = null;

    public function truncateDatabase(TruncateDatabase $truncateDatabase)
    {
        $truncateDatabase->handle(tenant());

        return response()->json([
            'message' => 'Database truncated',
        ]);
    }

    public function seedForVisualTests()
    {
        $tenant = tenant();

        $tenant->run(function () {
            Artisan::call('db:seed', [
                '--class' => SetupVisualSeeder::class,
            ]);
        });

        return response()->json([
            'message' => 'Database seeded',
        ]);
    }

    public function createAuthNewUser(Request $request)
    {
        $newUser = $this->createUser($request);

        return response()->json($newUser + ['password' => $this->getUserPassword()]);
    }

    public function login(Request $request)
    {
        $attributes = $request->input('attributes');

        $tenantUser = User::query()
            ->where($attributes ?? ['id' => 0])
            ->first();

        if (! $tenantUser) {
            ['tenantUser' => $tenantUser, 'centralUser' => $centralUser] = $this->createUser($request);
        } else {
            $centralUser = CentralUser::firstWhere('global_id', $tenantUser->global_id);
        }

        $tenantUser->load(array_merge(['roles'], $request->input('load', [])));

        return tap($tenantUser, function ($user) use ($centralUser) {
            auth()->login($centralUser);

            $user->setHidden([])->setVisible([]);
        });
    }

    private function createUser(Request $request)
    {
        $roleNames  = $request->input('roleNames', 'superAdmin');
        $attributes = $request->input('attributes', []);

        return $this->createAuthUser($roleNames, $attributes);
    }

    public function factory(Request $request)
    {
        $factory = $this->factoryBuilder(
            $request->input('model'),
            $request->input('state', []),
        )
            ->count(intval($request->input('count', 1)))
            ->create($request->input('attributes'))
            ->each(fn ($model) => $model->setHidden([])->setVisible([]))
            ->load($request->input('load', []))
            ->pipe(function ($collection) {
                return $collection->count() > 1
                    ? $collection
                    : $collection->first();
            });

        return response()->json($factory);
    }

    public function artisan(Request $request)
    {
        $request->validate([
            'command'    => [
                'required',
                'string',
            ],
            'parameters' => [
                'nullable',
                'array',
            ],
        ]);

        Artisan::call(
            command: $request->input('command'),
            parameters: $request->input('parameters'),
        );

        return response()->json(Artisan::output(), 202);
    }

    public function csrfToken()
    {
        return response()->json(csrf_token());
    }

    protected function factoryBuilder($model, $states = [])
    {
        $factory = $model::factory();

        $states = Arr::wrap($states);

        foreach ($states as $state => $attributes) {
            if (is_int($state)) {
                $state      = $attributes;
                $attributes = [];
            }

            $attributes = Arr::wrap($attributes);

            $factory = $factory->{$state}(...$attributes);
        }

        return $factory;
    }

    public function getConfig(Request $request)
    {
        $config = config($request->input('key'));

        return response()->json($config);
    }

    public function runPhp(Request $request)
    {
        logger('runPhp', $request->all());
        $code = $request->input('command');

        if ($code[-1] !== ';') {
            $code .= ';';
        }

        if (! Str::contains($code, 'return')) {
            $code = 'return ' . $code;
        }

        return response()->json([
            'result' => eval($code),
        ]);
    }

    public function runServiceMethod(Request $request)
    {
        $service    = $request->input('service');
        $method     = $request->input('method');
        $parameters = Arr::wrap($request->input('params', []));

        if (! class_exists($service)) {
            return response()->json([
                'error' => 'Service not found',
            ], 404);
        }

        $playwrightService = new $service();

        if (! method_exists($playwrightService, $method)) {
            return response()->json([
                'error' => 'Method not found',
            ], 404);
        }

        return response()->json([
            'result' => $playwrightService->{$method}(...$parameters),
        ]);
    }
}
