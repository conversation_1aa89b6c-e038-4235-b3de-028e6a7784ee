<?php

namespace App\Playwright\Seeders\Visual;

use App\Models\Lead;
use App\Models\LeadAnswer;
use App\Models\LeadAnswerLog;
use App\Models\LeadAnswerResult;
use App\Models\LeadSituation;
use App\Models\LeadTracking;
use App\Models\Question;
use App\Modules\MarketingServices\Models\Ad;
use App\Modules\MarketingServices\Models\AdGroup;

class LeadSeeder extends BaseVisualSeeder
{
    private ?Question $oneOfQuestion = null;

    private ?Question $numberQuestion = null;

    private array $standardQuestions = [];

    /**
     * Run the seeder to create default leads for visual tests.
     *
     * @throws \Exception if prerequisite data (study, site, campaignForm, situation, questions) is missing.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding Leads, Situations, and Answers (Visual)...</comment>');

        $this->ensurePrerequisites();
        $this->createDefaultLeads();
        $this->createDefaultLeadSituations();
        $this->createDefaultLeadAnswers();

        $this->command?->getOutput()->writeln('<info>  Leads, Situations, and Answers (Visual) seeded successfully.</info>');
    }

    private function ensurePrerequisites(): void
    {
        if (! $this->getStudy()) {
            throw new \Exception('Study not found. Ensure Visual\StudySeeder is run first.');
        }

        if (! $this->getSite()) {
            throw new \Exception('Site not found. Ensure Visual\SiteSeeder is run first.');
        }

        if (! $this->getCampaignForm()) {
            throw new \Exception('CampaignForm not found. Ensure Visual\CampaignFormSeeder is run first.');
        }

        if (! $this->getFirstWorkflow()) {
            throw new \Exception('Workflow not found. Ensure Visual\WorkflowSeeder is run first.');
        }

        if (! $this->getFirstLeadStatusWorkflow()) {
            throw new \Exception('LeadStatusWorkflow not found. Ensure Visual\LeadStatusWorkflowSeeder is run first.');
        }

        if (! $this->getSituation()) {
            throw new \Exception('Situation not found in BaseVisualSeeder. Ensure SituationSeeder runs before LeadSeeder.');
        }

        if (empty($this->getQuestions())) {
            throw new \Exception('Questions not found in BaseVisualSeeder. Ensure QuestionSeeder runs before LeadSeeder.');
        }

        foreach ($this->getQuestions() as $question) {
            match ($question->questionType->name) {
                'oneof'  => $this->oneOfQuestion                                                            = $question,
                'number' => $this->numberQuestion                                                           = $question,
                'firstName', 'lastName', 'email' => $this->standardQuestions[$question->questionType->name] = $question,
                default  => null,
            };
        }

        if (! $this->oneOfQuestion) {
            throw new \Exception('QuestionType "oneof" not found. Ensure it is seeded by QuestionSeeder.');
        }
        if (! $this->numberQuestion) {
            throw new \Exception('QuestionType "number" not found. Ensure it is seeded by QuestionSeeder.');
        }
        if (count($this->standardQuestions) < 3) {
            throw new \Exception('Missing one or more standard questions (firstName, lastName, email). Ensure they are seeded by QuestionSeeder.');
        }
    }

    private function createDefaultLeads(): void
    {
        static::$leads = [];

        foreach ($this->getRecordCount() as $count) {
            static::$leads[] = Lead::factory()->create([
                'study_id'                => $this->getStudy()->id,
                'campaign_form_id'        => $this->getCampaignForm()->id,
                'site_id'                 => $this->getSite()->id,
                'workflow_id'             => $this->getFirstWorkflow()->id,
                'lead_status_workflow_id' => $count === 1 ? $this->getQualifiedLeadStatusWorkflow()->id : $this->getDisqualifiedLeadStatusWorkflow()->id,
                'first_name'              => "Test {$count}",
                'last_name'               => "Referral {$count}",
                'email'                   => "test.referral-{$count}@example.com",
                'origin_ip'               => '127.0.0.1',
                'phone'                   => "+123456789{$count}",
                'lang'                    => 'en',
                'version'                 => 1,
                'best_time'               => null,
                'billed_at'               => null,
                'created_at'              => $this->getDefaultDate(),
                'updated_at'              => $this->getDefaultDate(),
            ]);
        }
    }

    private function createDefaultLeadSituations(): void
    {
        if (! $this->getFirstLead()) {
            throw new \Exception('Lead not available for creating LeadSituation.');
        }

        LeadSituation::factory()->create([
            'lead_id'          => $this->getFirstLead()->id,
            'situation_id'     => $this->getSituation()->id,
            'campaign_form_id' => $this->getCampaignForm()->id,
            'created_at'       => $this->getDefaultDate(),
            'updated_at'       => $this->getDefaultDate(),
        ]);
    }

    private function createDefaultLeadAnswers(): void
    {
        if (! $this->getLeads()) {
            throw new \Exception('Leads not available for creating LeadAnswers.');
        }

        foreach ($this->getLeads() as $key => $lead) {
            LeadAnswer::factory()->create([
                'lead_id'          => $lead->id,
                'question_id'      => $this->standardQuestions['firstName']->id,
                'question_type_id' => $this->standardQuestions['firstName']->questionType->id,
                'question_label'   => $this->standardQuestions['firstName']->question['en'],
                'answer'           => $lead->first_name,
                'is_predefined'    => true,
                'is_encrypted'     => false,
                'is_required'      => false,
                'order_by'         => $this->standardQuestions['firstName']->order_by,
                'created_at'       => $this->getDefaultDate(),
                'updated_at'       => $this->getDefaultDate(),
            ]);

            LeadAnswer::factory()->create([
                'lead_id'          => $lead->id,
                'question_id'      => $this->standardQuestions['lastName']->id,
                'question_type_id' => $this->standardQuestions['lastName']->questionType->id,
                'question_label'   => $this->standardQuestions['lastName']->question['en'],
                'answer'           => $lead->last_name,
                'is_predefined'    => true,
                'is_encrypted'     => false,
                'is_required'      => false,
                'order_by'         => $this->standardQuestions['lastName']->order_by,
                'created_at'       => $this->getDefaultDate(),
                'updated_at'       => $this->getDefaultDate(),
            ]);

            LeadAnswer::factory()->create([
                'lead_id'          => $lead->id,
                'question_id'      => $this->standardQuestions['email']->id,
                'question_type_id' => $this->standardQuestions['email']->questionType->id,
                'question_label'   => $this->standardQuestions['email']->question['en'],
                'answer'           => $lead->email,
                'is_predefined'    => true,
                'is_encrypted'     => false,
                'is_required'      => false,
                'order_by'         => $this->standardQuestions['email']->order_by,
                'created_at'       => $this->getDefaultDate(),
                'updated_at'       => $this->getDefaultDate(),
            ]);

            // Create 'oneof' type LeadAnswer.
            $oneOfAnswer = LeadAnswer::factory()->create([
                'lead_id'          => $lead->id,
                'question_id'      => $this->oneOfQuestion->id,
                'question_type_id' => $this->oneOfQuestion->questionType->id,
                'question_label'   => $this->oneOfQuestion->question['en'],
                'answer'           => $key === 0 ? [$this->oneOfQuestion->options['en'][0]] : [$this->oneOfQuestion->options['en'][1]],
                'is_predefined'    => true,
                'is_encrypted'     => false,
                'is_required'      => false,
                'order_by'         => $this->oneOfQuestion->order_by,
                'created_at'       => $this->getDefaultDate(),
                'updated_at'       => $this->getDefaultDate(),
            ]);

            // Create 'number' type LeadAnswer.
            $numberAnswer = LeadAnswer::factory()->create([
                'lead_id'          => $lead->id,
                'question_id'      => $this->numberQuestion->id,
                'question_type_id' => $this->numberQuestion->questionType->id,
                'question_label'   => $this->numberQuestion->question['en'],
                'answer'           => $key === 0 ? 1 : 2, // Initial answer.
                'is_predefined'    => true,
                'is_encrypted'     => false,
                'is_required'      => false,
                'order_by'         => $this->numberQuestion->order_by,
                'created_at'       => $this->getDefaultDate(),
                'updated_at'       => $this->getDefaultDate(),
            ]);

            // Create LeadAnswerLog entry for oneOfAnswer (e.g., an updated answer).
            $this->_createLeadAnswerLogEntry($oneOfAnswer, [
                'answer' => $key === 0 ? [$this->oneOfQuestion->options['en'][1]] : [$this->oneOfQuestion->options['en'][0]],
            ]);

            // Create LeadAnswerLog entry for numberAnswer (e.g., an updated answer).
            $this->_createLeadAnswerLogEntry($numberAnswer, [
                'answer' => $key === 0 ? 2 : 3,
            ]);

            $lead
                ->refresh()
                ->load([
                    'campaignForm',
                ]);

            // Process situation groups and create LeadAnswerResult entries.
            $situationGroups = $lead
                ->campaignForm
                ->situations()
                ->with('situationGroups.situationGroupConditions.question')
                ->get()[0]
                ->situationGroups;

            foreach ($situationGroups as $situationGroup) {
                foreach ($situationGroup->situationGroupConditions as $condition) {
                    if ($condition->question_id == $this->oneOfQuestion->id) {
                        $this->_createLeadAnswerResultEntry($oneOfAnswer, [
                            'question_id'                  => $condition->question_id,
                            'situation_group_condition_id' => $condition->id,
                            'conditions'                   => $condition->conditions,
                            'is_correct'                   => $key === 0,
                        ]);
                    }

                    if ($condition->question_id == $this->numberQuestion->id) {
                        $this->_createLeadAnswerResultEntry($numberAnswer, [
                            'question_id'                  => $condition->question_id,
                            'situation_group_condition_id' => $condition->id,
                            'conditions'                   => $condition->conditions,
                            'is_correct'                   => $key === 0,
                        ]);
                    }
                }
            }
        }
    }

    /**
     * Creates a LeadAnswerLog entry for a given LeadAnswer.
     *
     * @param LeadAnswer $leadAnswer   The parent LeadAnswer.
     * @param array      $specificData Specific data for the log entry (e.g., 'answer').
     */
    private function _createLeadAnswerLogEntry(LeadAnswer $leadAnswer, array $specificData): void
    {
        LeadAnswerLog::factory()->create(array_merge([
            'lead_answer_id' => $leadAnswer->id,
            'created_at'     => $this->getDefaultDate(),
            'updated_at'     => $this->getDefaultDate(),
        ], $specificData));
    }

    /**
     * Creates a LeadAnswerResult entry for a given LeadAnswer.
     *
     * @param LeadAnswer $leadAnswer   The parent LeadAnswer.
     * @param array      $specificData Specific data for the result entry (e.g., 'question_id', 'result', 'score').
     */
    private function _createLeadAnswerResultEntry(LeadAnswer $leadAnswer, array $specificData): void
    {
        LeadAnswerResult::factory()->create(array_merge([
            'lead_answer_id' => $leadAnswer->id,
            'created_at'     => $this->getDefaultDate(),
            'updated_at'     => $this->getDefaultDate(),
        ], $specificData));
    }

    public function createDefaultLeadTracking(): void
    {
        $campaignForm = $this->getCampaignForm();
        $adPlatform   = $this->getAdPlatform();
        $adCampaign   = $this->getAdCampaign();
        $adGroup      = $this->getAdGroup(); // Fetch AdGroup
        $ad           = $this->getAd();           // Fetch Ad

        if (! empty($this->getLeads()) || ! $campaignForm || ! $adPlatform || ! $adCampaign || ! $adGroup || ! $ad) { // Updated check
            throw new \Exception('Prerequisite data (Lead, CampaignForm, AdPlatform, AdCampaign, AdGroup, or Ad) not found for LeadTrackingSeeder.');
        }

        foreach ($this->getLeads() as $lead) {
            LeadTracking::factory()->create([
                // Foreign Keys from seeded data
                'lead_id'          => $lead->id,
                'campaign_form_id' => $campaignForm->id,
                'ad_platform_id'   => $adPlatform->id,
                'ad_campaign_id'   => $adCampaign->id,
                'ad_group_id'      => $adGroup->id, // Uses fetched AdGroup
                'ad_id'            => $ad->id,           // Uses fetched Ad

                // Static UTM values
                'utm'              => [
                    'utm_source'   => $adPlatform->utm_slug ?? $adPlatform->platform,
                    'utm_medium'   => $adGroup->utm_slug,    // Uses fetched AdGroup
                    'utm_campaign' => $adCampaign->utm_slug,
                    'utm_content'  => $ad->utm_slug,        // Uses fetched Ad
                    'utm_term'     => 'visual-test-term',
                ],

                // Other static values for consistency
                'lang'             => 'en',
                'origin_ip'        => '127.0.0.1',
                'user_agent'       => 'Playwright Visual Test Agent',
                'params'           => [
                    'visual_test_param' => 'static_value',
                    'source_id'         => 'playwright',
                ],
                'created_at'       => $this->getDefaultDate(),
                'updated_at'       => $this->getDefaultDate(),
            ]);
        }
    }
}
