<?php

namespace App\Playwright\Seeders\Visual;

use App\Models\Study;

class StudySeeder extends BaseVisualSeeder
{
    /**
     * Run the seeder to create the default study and associate user roles for visual tests.
     *
     * @throws \Exception if prerequisite data is missing.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding Study and Associations (Visual)...</comment>');

        $this->ensurePrerequisites();

        $this->createDefaultStudy();
        $this->associateUserAndRolesToStudy();
        $this->generateStudyActivityLog();

        /** Load study with relationships */
        $this->getStudy()->load([
            'workflows'                     => fn ($query) => $query->orderBy('name'),
            'workflows.leadStatusWorkflows' => fn ($query) => $query->orderBy('status_order_by'),
        ]);

        $this->command?->getOutput()->writeln('<info>  Study and Associations (Visual) seeded successfully.</info>');
    }

    private function ensurePrerequisites(): void
    {
        if (! $this->getUser()) {
            throw new \Exception('User not found. Ensure Visual\UserSeeder is run first.');
        }

        if (! $this->getClients()[0]) {
            throw new \Exception('Client not found. Ensure Visual\CoreDataSeeder is run first.');
        }

        if (! $this->getTherapeuticArea()) {
            throw new \Exception('TherapeuticArea not found. Ensure Visual\CoreDataSeeder is run first.');
        }

        if (! $this->getStudyStatus()) {
            throw new \Exception('StudyStatus not found. Ensure Visual\CoreDataSeeder is run first.');
        }

        if (empty($this->getStudyRoles())) {
            $this->command?->getOutput()->writeln('<warning>  StudyRoles (Visual) are empty. User will not be associated via specific roles in this seeder.</warning>');
        }
    }

    private function createDefaultStudy(): void
    {
        $client = $this->getClients()[0];

        static::$study = Study::factory()->create([
            'study_name'           => 'Test Study',
            'audience_description' => 'Test Audience Description',
            'total_revenue_goal'   => 100000,
            'start_at'             => $this->getDefaultDate(),
            'finish_at'            => $this->getDefaultDate(),
            'enroll_start_at'      => $this->getDefaultDate(),
            'enroll_finish_at'     => $this->getDefaultDate(),
            'created_at'           => $this->getDefaultDate(),
            'updated_at'           => $this->getDefaultDate(),
            'client_id'            => $client->id,
            'sponsor_id'           => $client->id,
            'study_status_id'      => $this->getStudyStatus()->id,
            'therapeutic_area_id'  => $this->getTherapeuticArea()->id,
        ]);

        $this->getStudy()->refresh();
    }

    private function associateUserAndRolesToStudy(): void
    {
        /** Attach user to study */
        $this->getStudy()->users()->attach($this->getUser()->id);

        foreach ($this->getStudyRoles() as $studyRole) {
            $studyRole->users()->attach($this->getUser()->id, ['study_id' => $this->getStudy()->id]);
        }
    }

    private function generateStudyActivityLog(): void
    {
        $originalName = $this->getStudy()->study_name;

        /** Generate multiple study names to generate activity logs */
        foreach ($this->getRecordCount() as $count) {
            $this->getStudy()->study_name = "{$originalName} {$count}";
            $this->getStudy()->save();
        }

        /** Reset study name */
        $this->getStudy()->study_name = $originalName;
        $this->getStudy()->save();
    }
}
