<?php

namespace App\Playwright\Seeders\Visual;

use App\Enums\IntegrationEnum;
use App\Models\Client;
use App\Models\Integration;
use App\Models\SiteStatus;
use App\Models\StudyStatus;
use App\Models\TherapeuticArea;
use App\Modules\MarketingServices\Models\AdPlatform;

class CoreDataSeeder extends BaseVisualSeeder
{
    /**
     * Run the seeder to create core data like clients, sponsors,
     * therapeutic areas, and study statuses for Playwright visual tests.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding Core Data (Visual)...</comment>');

        $this->createDefaultClientAndSponsor();
        $this->setDefaultTherapeuticArea();
        $this->setDefaultStudyStatus();
        $this->setDefaultSiteStatus();
        $this->setDefaultIntegrations();
        $this->setDefaultAdPlatform();

        $this->command?->getOutput()->writeln('<info>  Core Data (Visual) seeded successfully.</info>');
    }

    private function createDefaultClientAndSponsor(): void
    {
        static::$clients = [];

        foreach ($this->getRecordCount() as $count) {
            static::$clients[] = Client::factory()
                ->create([
                    'client_name' => "Client {$count}",
                    'created_at'  => $this->getDefaultDate(),
                    'updated_at'  => $this->getDefaultDate(),
                ]);
        }
    }

    private function setDefaultTherapeuticArea(): void
    {
        static::$therapeuticArea = TherapeuticArea::query()->orderBy('id', 'asc')->first();

        if (! $this->getTherapeuticArea()) {
            throw new \Exception('No Therapeutic Area found in the database. Please ensure at least one exists or seed them first.');
        }
    }

    private function setDefaultStudyStatus(): void
    {
        static::$studyStatus = StudyStatus::query()->orderBy('id', 'asc')->first();

        if (! $this->getStudyStatus()) {
            throw new \Exception('No Study Status found in the database. Please ensure at least one exists or seed them first.');
        }
    }

    private function setDefaultSiteStatus(): void
    {
        static::$siteStatus = SiteStatus::query()
            ->orderBy('id')
            ->first();

        if (! $this->getSiteStatus()) {
            throw new \Exception('No site status found.');
        }
    }

    private function setDefaultIntegrations(): void
    {
        $integrationNames = [
            IntegrationEnum::rippleScience()->value,
            IntegrationEnum::obvioHealth()->value,
            IntegrationEnum::calendarEngine()->value,
        ];

        static::$integrations = Integration::query()
            ->whereIn('alias', $integrationNames)
            ->get()
            ->keyBy('alias')
            ->all();

        foreach ($integrationNames as $name) {
            if (! isset($this->getIntegrations()[$name])) {
                $this->command?->getOutput()->writeln('<warning>  Integration "' . $name . '" not found. Skipping its options.</warning>');
            }
        }
    }

    private function setDefaultAdPlatform(): void
    {
        static::$adPlatform = AdPlatform::whereNull('parent_id')
            ->orderBy('platform', 'asc')
            ->first();

        if (! $this->getAdPlatform()) {
            throw new \Exception('No ad platform found.');
        }
    }
}
