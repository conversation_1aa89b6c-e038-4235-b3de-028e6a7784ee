<?php

namespace App\Playwright\Seeders\Visual;

use App\Models\Study;
use App\Modules\Automation\Enums\AutomationStatusEnum;
use App\Modules\Automation\Models\Automation;

class AutomationSeeder extends BaseVisualSeeder
{
    /**
     * Run the seeder to create default automations for visual tests.
     *
     * @throws \Exception if prerequisite data is missing.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding Automations (Visual)...</comment>');

        $this->ensurePrerequisites();
        $this->createDefaultAutomation();

        $this->command?->getOutput()->writeln('<info>  Automations (Visual) seeded successfully.</info>');
    }

    private function ensurePrerequisites(): void
    {
        if (! $this->getStudy()) {
            throw new \Exception('Study not found. Ensure Visual\StudySeeder is run first.');
        }
    }

    private function createDefaultAutomation(): void
    {
        static::$automation = Automation::factory()->create([
            'name'             => 'Test Automation',
            'automatable_type' => Study::class,
            'automatable_id'   => $this->getStudy()->id,
            'status'           => AutomationStatusEnum::paused()->value,
            'created_at'       => $this->getDefaultDate(),
            'updated_at'       => $this->getDefaultDate(),
        ]);
    }
}
