<?php

namespace App\Playwright\Seeders\Visual;

use App\Models\AggregateFunnelMetric;
use App\Models\AggregateMetric;
use App\Models\AggregateStatusFunnelMetric;
use Exception;
use Illuminate\Support\Carbon;

class AggregateSeeder extends BaseVisualSeeder
{
    /**
     * Run the seeder to create aggregate metrics for the last 30 days for Playwright visual tests.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding Aggregate Metrics (Visual)...</comment>');

        $this->ensurePrerequisites();
        $this->seedMetricsForDateRange();

        $this->command?->getOutput()->writeln('<info>  Aggregate Metrics (Visual) seeded successfully.</info>');
    }

    private function ensurePrerequisites(): void
    {
        if (! $this->getStudy()) {
            throw new Exception('Static Study not found in BaseVisualSeeder.');
        }

        if (! $this->getSite()) {
            throw new Exception('Static Site not found in BaseVisualSeeder.');
        }

        if (! $this->getFirstWorkflow()) {
            throw new Exception('Static Workflow not found in BaseVisualSeeder.');
        }

        if (! $this->getCampaignForm()) {
            throw new Exception('Static CampaignForm not found in BaseVisualSeeder.');
        }

        if (! $this->getAdPlatform()) {
            throw new Exception('Static AdPlatform not found in BaseVisualSeeder.');
        }

        if (! $this->getAdCampaign()) {
            throw new Exception('Static AdCampaign not found in BaseVisualSeeder.');
        }

        if (! $this->getAdGroup()) {
            throw new Exception('Static AdGroup not found in BaseVisualSeeder.');
        }

        if (! $this->getFirstLeadStatusWorkflow()) {
            throw new Exception('Static LeadStatusWorkflow not found in BaseVisualSeeder.');
        }

        $this->getFirstLeadStatusWorkflow()->refresh()->load('leadStage');
    }

    private function seedMetricsForDateRange(): void
    {
        $startDate = Carbon::parse($this->getDefaultDate())->subDays(29);

        for ($i = 0; $i < 30; $i++) {
            $currentDate = $startDate->copy()->addDays($i)->toDateString();

            // Pass the loop index $i to vary metrics per day
            $this->seedAggregateMetricForDate($currentDate, $i);
            $this->seedAggregateFunnelMetricForDate($currentDate, $i);
            $this->seedAggregateStatusFunnelMetricForDate($currentDate, $i);
        }
    }

    private function seedAggregateMetricForDate(string $currentDate, int $index): void
    {
        // Base static values, modified by index for daily variation
        $baseSpend              = 100;
        $baseClicks             = 200;
        $baseQualified          = 20;
        $baseDisqualified       = 5;
        $baseSubmissions        = 30;
        $baseConversions        = 25;
        $baseMilestoneTotal     = 10;
        $baseMilestoneRevenue   = 1000;
        $baseDailyNotes         = 2;
        $baseTotalNotes         = 10 * ($index + 1);
        $baseQualifiedWithNotes = 5;

        AggregateMetric::factory()->create([
            'date'                      => $currentDate,
            'study_id'                  => $this->getStudy()->id,
            'site_id'                   => $this->getSite()->id,
            'workflow_id'               => $this->getFirstWorkflow()->id,
            'campaign_form_id'          => $this->getCampaignForm()->id,
            'ad_platform_id'            => $this->getAdPlatform()->id,
            'ad_campaign_id'            => $this->getAdCampaign()->id,
            'ad_group_id'               => $this->getAdGroup()->id,
            // Static, day-variant metrics
            'spend'                     => $baseSpend + ($index * 2.5),
            'clicks'                    => $baseClicks + ($index * 5),
            'qualified'                 => max(0, $baseQualified + $index - ($index % 3)), // Introduce some non-linear variation
            'disqualified'              => max(0, $baseDisqualified + ($index % 5) - 2),
            'submissions'               => $baseSubmissions + $index,
            'conversions'               => max(0, $baseConversions + $index - ($index % 4)), // Ensure conversions <= submissions logic if complex
            'milestone_total'           => max(0, $baseMilestoneTotal + ($index % 6) - 3),
            'milestone_total_revenue'   => $baseMilestoneRevenue + ($index * 50.75),
            'daily_note_count'          => max(0, $baseDailyNotes + ($index % 3) - 1),
            'total_note_count'          => $baseTotalNotes + $index,
            'qualified_with_note_total' => max(0, $baseQualifiedWithNotes + ($index % 4) - 2),
        ]);
    }

    private function seedAggregateFunnelMetricForDate(string $currentDate, int $index): void
    {
        // Base static values, modified by index
        $baseTotal                    = 50;
        $baseTodayTotal               = 5;
        $baseDurationTotal            = 100;
        $baseDuration                 = 3000;
        $baseDurationToMilestoneTotal = 20;
        $baseDurationToMilestone      = 1500;

        AggregateFunnelMetric::factory()->create([
            'date'                        => $currentDate,
            'study_id'                    => $this->getStudy()->id,
            'workflow_id'                 => $this->getFirstWorkflow()->id,
            'site_id'                     => $this->getSite()->id,
            'lead_stage_id'               => $this->getFirstLeadStatusWorkflow()->leadStage->id,
            'campaign_form_id'            => $this->getCampaignForm()->id,
            'ad_platform_id'              => $this->getAdPlatform()->id,
            // Static, day-variant metrics
            'total'                       => $baseTotal + $index * 2,
            'today_total'                 => max(0, $baseTodayTotal + ($index % 4) - 2),
            'duration_total'              => $baseDurationTotal + ($index * 10),
            'duration'                    => $baseDuration + ($index * 100.5),
            'duration_to_milestone_total' => max(0, $baseDurationToMilestoneTotal + ($index % 5) - 2),
            'duration_to_milestone'       => $baseDurationToMilestone + ($index * 50.25),
        ]);
    }

    private function seedAggregateStatusFunnelMetricForDate(string $currentDate, int $index): void
    {
        // Base static values, modified by index
        $baseTotal         = 40;
        $baseTodayTotal    = 3;
        $baseDurationTotal = 80;
        $baseDuration      = 2000;

        AggregateStatusFunnelMetric::factory()->create([
            'date'                    => $currentDate,
            'study_id'                => $this->getStudy()->id,
            'site_id'                 => $this->getSite()->id,
            'workflow_id'             => $this->getFirstWorkflow()->id,
            'lead_status_workflow_id' => $this->getFirstLeadStatusWorkflow()->id,
            'campaign_form_id'        => $this->getCampaignForm()->id,
            'ad_platform_id'          => $this->getAdPlatform()->id,
            // Static, day-variant metrics
            'total'                   => $baseTotal + $index,
            'today_total'             => max(0, $baseTodayTotal + ($index % 3) - 1),
            'duration_total'          => $baseDurationTotal + ($index * 5),
            'duration'                => $baseDuration + ($index * 75.5),
        ]);
    }
}
