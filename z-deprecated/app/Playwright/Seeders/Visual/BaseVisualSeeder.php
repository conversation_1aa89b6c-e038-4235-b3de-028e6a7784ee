<?php

namespace App\Playwright\Seeders\Visual;

use App\Models\CampaignForm;
use App\Models\CampaignFormType;
use App\Models\Client;
use App\Models\FeasibilityReport;
use App\Models\Integration;
use App\Models\IntegrationOption;
use App\Models\IntegrationStudy;
use App\Models\Lead;
use App\Models\LeadStatusWorkflow;
use App\Models\Question;
use App\Models\Site;
use App\Models\SiteLocation;
use App\Models\SiteStatus;
use App\Models\Situation;
use App\Models\Study;
use App\Models\StudyRole;
use App\Models\StudyStatus;
use App\Models\TherapeuticArea;
use App\Models\User;
use App\Models\UserPreference;
use App\Models\Workflow;
use App\Modules\Automation\Models\Automation;
use App\Modules\MarketingServices\Models\Ad;
use App\Modules\MarketingServices\Models\AdAccount;
use App\Modules\MarketingServices\Models\AdCampaign;
use App\Modules\MarketingServices\Models\AdGroup;
use App\Modules\MarketingServices\Models\AdPlatform;
use App\Modules\MessageEditor\Models\EmailContent;
use App\Modules\MessageEditor\Models\SmsContent;
use Illuminate\Database\Seeder;

class BaseVisualSeeder extends Seeder
{
    /** Properties to hold seeded data instances for access by subsequent seeders */
    protected static ?User $user = null;

    /** @var Client[] */
    protected static array $clients = [];

    protected static ?StudyStatus $studyStatus = null;

    protected static ?TherapeuticArea $therapeuticArea = null;

    /** @var StudyRole[] */
    protected static array $studyRoles = [];

    protected static ?Study $study = null;

    /** @var Integration[] */
    protected static array $integrations = [];

    /** @var IntegrationOption[] */
    protected static array $integrationOptions = [];

    /** @var IntegrationStudy[] */
    protected static array $integrationStudies = [];

    protected static ?FeasibilityReport $feasibilityReport = null;

    protected static ?Site $site = null;

    protected static ?SiteLocation $siteLocation = null;

    protected static ?SiteStatus $siteStatus = null;

    protected static ?CampaignForm $campaignForm = null;

    protected static ?CampaignFormType $campaignFormType = null;

    protected static ?EmailContent $emailContent = null;

    protected static ?SmsContent $smsContent = null;

    protected static ?AdPlatform $adPlatform = null;

    protected static ?AdAccount $adAccount = null;

    protected static ?AdCampaign $adCampaign = null;

    protected static ?AdGroup $adGroup = null;

    protected static ?Ad $ad = null;

    /** @var Lead[] */
    protected static array $leads = [];

    protected static ?Automation $automation = null;

    protected static ?Situation $situation = null;

    /** @var Question[] */
    protected static array $questions = [];

    /** @var UserPreference[] */
    protected static array $userPreferences = [];

    protected static array $recordCount = [1, 2, 3];

    protected static string $defaultDate = '2024-01-01 00:00:00';

    /**
     * Get the shared User instance.
     */
    public function getUser(): ?User
    {
        return static::$user;
    }

    /**
     * Get the shared Study instance.
     */
    public function getStudy(): ?Study
    {
        return static::$study;
    }

    public function getRecordCount(): array
    {
        return static::$recordCount;
    }

    public function getDefaultDate(): string
    {
        return static::$defaultDate;
    }

    public function getSiteStatus(): ?SiteStatus
    {
        return static::$siteStatus;
    }

    public function getSite(): ?Site
    {
        return static::$site;
    }

    public function getSiteLocation(): ?SiteLocation
    {
        return static::$siteLocation;
    }

    public function getTherapeuticArea(): ?TherapeuticArea
    {
        return static::$therapeuticArea;
    }

    public function getStudyStatus(): ?StudyStatus
    {
        return static::$studyStatus;
    }

    public function getStudyRoles(): array
    {
        return static::$studyRoles;
    }

    public function getClients(): array
    {
        return static::$clients;
    }

    public function getIntegrations(): array
    {
        return static::$integrations;
    }

    public function getFeasibilityReport(): ?FeasibilityReport
    {
        return static::$feasibilityReport;
    }

    public function getCampaignForm(): ?CampaignForm
    {
        return static::$campaignForm;
    }

    public function getCampaignFormType(): ?CampaignFormType
    {
        return static::$campaignFormType;
    }

    public function getEmailContent(): ?EmailContent
    {
        return static::$emailContent;
    }

    public function getSmsContent(): ?SmsContent
    {
        return static::$smsContent;
    }

    public function getAdPlatform(): ?AdPlatform
    {
        return static::$adPlatform;
    }

    public function getAdAccount(): ?AdAccount
    {
        return static::$adAccount;
    }

    public function getAdCampaign(): ?AdCampaign
    {
        return static::$adCampaign;
    }

    public function getAdGroup(): ?AdGroup
    {
        return static::$adGroup;
    }

    public function getAd(): ?Ad
    {
        return static::$ad;
    }

    public function getFirstLead(): ?Lead
    {
        return static::$leads[0];
    }

    public function getLeads(): array
    {
        return static::$leads;
    }

    public function getFirstWorkflow(): ?Workflow
    {
        return static::$study->workflows[0];
    }

    public function getFirstLeadStatusWorkflow(): ?LeadStatusWorkflow
    {
        return static::$study->workflows[0]->leadStatusWorkflows[0];
    }

    public function getQualifiedLeadStatusWorkflow(): ?LeadStatusWorkflow
    {
        $workflow            = static::$study->workflows[0];
        $leadStatusWorkflows = $workflow->leadStatusWorkflows;

        return $leadStatusWorkflows->where('lead_status_id', $workflow->new_lead_status_id)->first();
    }

    public function getDisqualifiedLeadStatusWorkflow(): ?LeadStatusWorkflow
    {
        $workflow            = static::$study->workflows[0];
        $leadStatusWorkflows = $workflow->leadStatusWorkflows;

        return $leadStatusWorkflows->where('lead_status_id', $workflow->new_lead_status_id)->first();
    }

    public function getAutomation(): ?Automation
    {
        return static::$automation;
    }

    public function getSituation(): ?Situation
    {
        return static::$situation;
    }

    /**
     * @return Question[]
     */
    public function getQuestions(): array
    {
        return static::$questions;
    }

    public function getUserPreferences(): array
    {
        return static::$userPreferences;
    }
}
