<?php

namespace App\Playwright\Seeders\Visual;

use App\Modules\MarketingServices\Models\Ad;
use App\Modules\MarketingServices\Models\AdAccount;
use App\Modules\MarketingServices\Models\AdCampaign;
use App\Modules\MarketingServices\Models\AdGroup;

class AdSeeder extends BaseVisualSeeder
{
    /**
     * Run the seeder to create default ad platform, account, and campaign for visual tests.
     *
     * @throws \Exception if prerequisite data (study) is missing.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding Ad Platform, Account, Campaign, AdGroup, and Ad (Visual)...</comment>');

        $this->ensurePrerequisites();
        $this->createDefaultAdAccount();
        $this->createDefaultAdCampaign();
        $this->createDefaultAdGroup();
        $this->createDefaultAd();

        $this->command?->getOutput()->writeln('<info>  Ad Platform, Account, Campaign, AdGroup, and Ad (Visual) seeded successfully.</info>');
    }

    private function ensurePrerequisites(): void
    {
        if (! $this->getStudy()) {
            throw new \Exception('Study not found. Ensure Visual\StudySeeder is run first before AdSeeder.');
        }

        if (! $this->getCampaignForm()) {
            throw new \Exception('Campaign Form not found. Ensure Visual\PlatformSeeder is run first before AdSeeder.');
        }

        if (! $this->getAdPlatform()) {
            throw new \Exception('AdPlatform not found. Ensure Visual\PlatformSeeder is run first before AdSeeder.');
        }
    }

    private function createDefaultAdAccount(): void
    {
        static::$adAccount = AdAccount::factory()->create([
            'ad_platform_id'   => $this->getAdPlatform()->id,
            'name'             => 'Test Ad Account',
            'status'           => 'active',
            'account_currency' => 'USD',
            'budget'           => 10000.00,
            'created_at'       => $this->getDefaultDate(),
            'updated_at'       => $this->getDefaultDate(),
        ]);
    }

    private function createDefaultAdCampaign(): void
    {
        if (! $this->getAdAccount()) {
            throw new \Exception('AdAccount not found. Ensure createDefaultAdAccount is called first.');
        }

        static::$adCampaign = AdCampaign::factory()->create([
            'study_id'            => $this->getStudy()->id,
            'ad_account_id'       => $this->getAdAccount()->id,
            'ad_platform_id'      => $this->getAdPlatform()->id,
            'name'                => 'Test Ad Campaign',
            'status'              => 'active',
            'utm_slug'            => 'test-ad-campaign',
            'account_currency'    => 'USD',
            'budget'              => 5000.00,
            'entity_type'         => 'campaign_group',
            'entity_id'           => 'entity_12345',
            'first_ad_spend_date' => $this->getDefaultDate(),
            'created_at'          => $this->getDefaultDate(),
            'updated_at'          => $this->getDefaultDate(),
        ]);
    }

    private function createDefaultAdGroup(): void
    {
        $adCampaign = $this->getAdCampaign();
        $adPlatform = $this->getAdPlatform();

        if (! $adCampaign || ! $adPlatform) {
            throw new \Exception('AdCampaign or AdPlatform not found. Ensure they are created before AdGroup in AdSeeder.');
        }

        static::$adGroup = AdGroup::factory()->create([
            'ad_campaign_id'   => $adCampaign->id,
            'ad_platform_id'   => $adPlatform->id,
            'campaign_form_id' => $this->getCampaignForm()->id,
            'name'             => 'Visual Test Ad Group',
            'utm_slug'         => 'visual-adgroup-slug',
            'status'           => 'ENABLED',
            'account_currency' => $adCampaign->account_currency ?? 'USD',
            'budget'           => 1000,
            'created_at'       => $this->getDefaultDate(),
            'updated_at'       => $this->getDefaultDate(),
        ]);
    }

    private function createDefaultAd(): void
    {
        $adGroup    = $this->getAdGroup();
        $adPlatform = $this->getAdPlatform();

        if (! $adGroup || ! $adPlatform) {
            throw new \Exception('AdGroup or AdPlatform not found. Ensure they are created before Ad in AdSeeder.');
        }

        static::$ad = Ad::factory()->create([
            'ad_group_id'      => $adGroup->id,
            'ad_platform_id'   => $adPlatform->id,
            'name'             => 'Visual Test Ad',
            'utm_slug'         => 'visual-ad-slug',
            'status'           => 'ENABLED',
            'account_currency' => $adGroup->account_currency ?? 'USD',
            'created_at'       => $this->getDefaultDate(),
            'updated_at'       => $this->getDefaultDate(),
        ]);
    }
}
