<?php

namespace App\Playwright\Seeders\Visual;

// This is the main orchestrating seeder for Playwright Visual Regression tests.
use App\Models\Activity;
use Illuminate\Support\Carbon;

class SetupVisualSeeder extends BaseVisualSeeder
{
    /**
     * Run the database seeds for Playwright visual regression tests.
     */
    public function run(): void
    {
        // Early return if we are not in the visual testing environment or similar check.
        if (app()->environment() !== 'playwright') { // Or your specific env key
            $this->command?->getOutput()->writeln('<info>Not in playwright testing environment. Skipping visual seeders.</info>');

            return;
        }

        if ($this->command) {
            $this->command->getOutput()->writeln('<comment>Starting Playwright Visual Data Setup...</comment>');
        }

        $this->call(UserSeeder::class);
        $this->call(CoreDataSeeder::class);
        $this->call(StudySeeder::class);

        // We will add calls to specialized seeders here, for example:
        $this->call(IntegrationOptionSeeder::class);
        $this->call(IntegrationStudySeeder::class);
        $this->call(FeasibilityReportSeeder::class); // Should be before SiteSeeder generally
        $this->call(SiteSeeder::class);

        // We will add calls to specialized seeders here, for example:
        $this->call(CampaignFormSeeder::class);
        $this->call(QuestionSeeder::class);
        $this->call(SituationSeeder::class);

        // We will add calls to specialized seeders here, for example:
        $this->call(MessageContentSeeder::class);

        // We will add calls to specialized seeders here, for example:
        $this->call(AdSeeder::class);

        // We will add calls to specialized seeders here, for example:
        $this->call(LeadSeeder::class);

        // We will add calls to specialized seeders here, for example:
        $this->call(AutomationSeeder::class);

        // Seeder for Aggregate Metrics
        $this->call(AggregateSeeder::class);

        $this->adjustActivityLogTimestamps();

        if ($this->command) {
            $this->command->getOutput()->writeln('<info>Playwright Visual Data Setup completed successfully.</info>');
        }
    }

    /**
     * Adjusts the created_at timestamps of specific activity logs for consistent visual testing.
     * Mimics the functionality of updateActivityLogCreatedAt from the original seeder.
     */
    private function adjustActivityLogTimestamps(): void
    {
        if ($this->command) {
            $this->command->getOutput()->writeln('<comment>  Adjusting Activity Log Timestamps (Visual)...</comment>');
        }

        /** Update activity log created_at to be consistent for visual testing */
        $activityLogs = Activity::query()->orderBy('id', 'asc')->get();
        $date         = new Carbon($this->getDefaultDate());

        foreach ($activityLogs as $activityLog) {
            $date                    = $date->addHours(12);
            $activityLog->created_at = $date;
            $activityLog->updated_at = $date;
            $activityLog->save();
        }
    }
}
