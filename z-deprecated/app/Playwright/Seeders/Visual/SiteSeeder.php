<?php

namespace App\Playwright\Seeders\Visual;

use App\Models\Country;
use App\Models\Site;
use App\Models\SiteLocation;

// Assuming Country is needed for SiteLocation

class SiteSeeder extends BaseVisualSeeder
{
    /**
     * Run the seeder to create a default site, site location, and site status for visual tests.
     *
     * @throws \Exception if prerequisite data (study) is missing.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding Site, Location, and Status (Visual)...</comment>');

        $this->ensurePrerequisites();
        $this->getDefaultSiteLocation();
        $this->createDefaultSite();

        $this->command?->getOutput()->writeln('<info>  Site, Location, and Status (Visual) seeded successfully.</info>');
    }

    private function ensurePrerequisites(): void
    {
        if (! $this->getStudy()) {
            throw new \Exception('Study not found. Ensure Visual\StudySeeder is run first before SiteSeeder.');
        }

        if (! $this->getSiteStatus()) {
            throw new \Exception('Study not found. Ensure Visual\StudySeeder is run first before SiteSeeder.');
        }
    }

    private function getDefaultSiteLocation(): void
    {
        $country = Country::firstWhere('iso2', 'US');
        $state   = $country->states()->firstWhere('name', 'West Virginia');

        static::$siteLocation = SiteLocation::factory()->createOne([
            'address'     => '123 Main St',
            'suite'       => 'Suite 123',
            'country_id'  => $country->id,
            'state_id'    => $state->id,
            'city'        => 'Some City',
            'postal_code' => '12345',
            'created_at'  => $this->getDefaultDate(),
            'updated_at'  => $this->getDefaultDate(),
        ]);
    }

    private function createDefaultSite(): void
    {
        if (! $this->getSiteLocation()) {
            throw new \Exception('SiteLocation not found. Ensure getDefaultSiteLocation is called first.');
        }

        if (! $this->getSiteStatus()) {
            throw new \Exception('SiteStatus not found. Ensure getDefaultSiteStatus is called first.');
        }

        static::$site = Site::factory()->create([
            'name'             => 'Test Site',
            'study_id'         => $this->getStudy()->id,
            'site_location_id' => $this->getSiteLocation()->id,
            'site_status_id'   => $this->getSiteStatus()->id,
            'pi_last_name'     => 'Test Doctor',
            'site_grade'       => 5,
            'site_number'      => 1,
            'site_public'      => 'test_site_public_id_123',
            'created_at'       => $this->getDefaultDate(),
            'updated_at'       => $this->getDefaultDate(),
        ]);
    }
}
