<?php

namespace App\Playwright\Seeders\Visual;

use App\Models\FeasibilityReport;

class FeasibilityReportSeeder extends BaseVisualSeeder
{
    /**
     * Run the seeder to create a default feasibility report for visual tests.
     *
     * @throws \Exception if prerequisite data (study) is missing.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding Feasibility Report (Visual)...</comment>');

        $this->ensurePrerequisites();
        $this->createDefaultFeasibilityReport();

        $this->command?->getOutput()->writeln('<info>  Feasibility Report (Visual) seeded successfully.</info>');
    }

    private function ensurePrerequisites(): void
    {
        if (! $this->getStudy()) {
            throw new \Exception('Study not found. Ensure Visual\StudySeeder is run first before FeasibilityReportSeeder.');
        }

        if (! $this->getTherapeuticArea()) {
            throw new \Exception('TherapeuticArea not found. Ensure getDefaultTherapeuticArea is called first.');
        }
    }

    private function createDefaultFeasibilityReport(): void
    {
        static::$feasibilityReport = FeasibilityReport::factory()->create([
            'study_id'                         => $this->getStudy()->id,
            'therapeutic_area_id'              => $this->getTherapeuticArea()->id,
            'conversion_rate'                  => 0.1234,
            'cpql'                             => 150.75,
            'qualified_rate'                   => 0.3567,
            'cpm'                              => 25.50,
            'cpc'                              => 3.25,
            'ctr'                              => 0.0255,
            'duration_to_milestone'            => 90.5,
            'qualified_to_signed_icf_rate'     => 0.7521,
            'qualified_to_milestone_rate'      => 0.6011,
            'signed_icf_to_enrolled_rate'      => 0.8500,
            'final_disp_to_screen_failed_rate' => 0.1500,
            'date'                             => $this->getDefaultDate(),
            'created_at'                       => $this->getDefaultDate(),
            'updated_at'                       => $this->getDefaultDate(),
        ]);
    }
}
