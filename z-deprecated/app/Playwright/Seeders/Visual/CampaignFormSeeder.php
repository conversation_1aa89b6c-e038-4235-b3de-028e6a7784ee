<?php

namespace App\Playwright\Seeders\Visual;

use App\Models\CampaignForm;
use App\Models\CampaignFormType;

class CampaignFormSeeder extends BaseVisualSeeder
{
    /**
     * Run the seeder to create a default campaign form and its type for visual tests.
     *
     * @throws \Exception if prerequisite data (study) is missing.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding Campaign Form and Type (Visual)...</comment>');

        $this->ensurePrerequisites();
        $this->getDefaultCampaignFormType();
        $this->createDefaultCampaignForm();

        $this->getCampaignForm()
            ->refresh()
            ->load('questions.questionType');

        $this->command?->getOutput()->writeln('<info>  Campaign Form and Type (Visual) seeded successfully.</info>');
    }

    private function ensurePrerequisites(): void
    {
        if (! $this->getStudy()) {
            throw new \Exception('Study not found. Ensure Visual\StudySeeder is run first before CampaignFormSeeder.');
        }
    }

    private function getDefaultCampaignFormType(): void
    {
        static::$campaignFormType = CampaignFormType::orderBy('id', 'asc')->first();

        if (! $this->getCampaignFormType()) {
            throw new \Exception('No CampaignFormType found in the database.');
        }
    }

    private function createDefaultCampaignForm(): void
    {
        if (! $this->getCampaignFormType()) {
            throw new \Exception('CampaignFormType not initialized. Ensure getDefaultCampaignFormType is called first.');
        }

        static::$campaignForm = CampaignForm::factory()->create([
            'answer_schema'              => null,
            'audience_description'       => 'Test Audience Description',
            'campaign_code'              => '1234',
            'campaign_form_type_id'      => $this->getCampaignFormType()->id,
            'campaign_name'              => 'Test Campaign Form',
            'form_public'                => 'test_form_public_id_123',
            'product'                    => 'Test Product',
            'study_id'                   => $this->getStudy()->id,
            'thank_you_url_disqualified' => 'https://example.com/disqualified',
            'thank_you_url_qualified'    => 'https://example.com/qualified',
            'exclude_study_duplicates'   => 0,
            'studio_flow_id'             => null,
            'twilio_flow_name'           => null,
            'twilio_flowId'              => null,
            'created_at'                 => $this->getDefaultDate(),
            'updated_at'                 => $this->getDefaultDate(),
        ]);
    }
}
