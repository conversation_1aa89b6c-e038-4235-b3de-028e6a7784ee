<?php

namespace App\Playwright\Seeders\Visual;

use App\Modules\MessageEditor\Models\EmailContent;
use App\Modules\MessageEditor\Models\SmsContent;

class MessageContentSeeder extends BaseVisualSeeder
{
    /**
     * Run the seeder to create default email and SMS content for visual tests.
     *
     * @throws \Exception if prerequisite data (study) is missing.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding Email and SMS Content (Visual)...</comment>');

        $this->ensurePrerequisites(); // Though content might be global, study_id is often used for association
        $this->createDefaultEmailContent();
        $this->createDefaultSmsContent();

        $this->command?->getOutput()->writeln('<info>  Email and SMS Content (Visual) seeded successfully.</info>');
    }

    private function ensurePrerequisites(): void
    {
        if (! $this->getStudy()) {
            $this->command?->getOutput()->writeln('<warning>  Study not found for MessageContentSeeder, proceeding with global content creation.</warning>');
        }
    }

    private function createDefaultEmailContent(): void
    {
        static::$emailContent = EmailContent::factory()->create([
            'name'           => 'Test Email',
            'email_template' => 'emails.react-email.default',
            'status'         => 'draft',
            'sender_name'    => 'Test User',
            'sender_email'   => '<EMAIL>',
            'study_id'       => $this->getStudy()->id,
            'created_at'     => $this->getDefaultDate(),
            'updated_at'     => $this->getDefaultDate(),
        ]);
    }

    private function createDefaultSmsContent(): void
    {
        static::$smsContent = SmsContent::factory()->create([
            'name'       => 'Test SMS Content',
            'status'     => 'draft',
            'message'    => null,
            'study_id'   => $this->getStudy()->id,
            'created_at' => $this->getDefaultDate(),
            'updated_at' => $this->getDefaultDate(),
        ]);
    }
}
