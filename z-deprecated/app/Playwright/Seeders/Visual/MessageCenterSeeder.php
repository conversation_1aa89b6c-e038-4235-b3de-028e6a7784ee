<?php

namespace App\Playwright\Seeders\Visual;

use App\Models\Country;
use App\Models\Site;
use App\Models\SiteLocation;
use App\Modules\MessageEditor\Models\EmailContent;
use App\Modules\MessageEditor\Models\SmsContent;

// Assuming Country is needed for SiteLocation

class MessageCenterSeeder extends BaseVisualSeeder
{
    /**
     * Run the seeder to create a default site, site location, and site status for visual tests.
     *
     * @throws \Exception if prerequisite data (study) is missing.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding Email & SMS Content (Visual)...</comment>');

        $this->ensurePrerequisites();
        $this->createDefaultEmailContent();
        $this->createDefaultSmsContent();

        $this->command?->getOutput()->writeln('<info> Email & SMS Content (Visual) seeded successfully.</info>');
    }

    private function ensurePrerequisites(): void
    {
        if (! $this->getStudy()) {
            throw new \Exception('Study not found. Ensure Visual\StudySeeder is run first before SiteSeeder.');
        }
    }

    private function createDefaultEmailContent(): EmailContent
    {
        if (! $this->getStudy()) {
            throw new \Exception('Study not found. Ensure createDefaultStudy is called first.');
        }

        return EmailContent::factory()->create([
            'name'           => 'Test Email',
            'email_template' => 'emails.react-email.default',
            'status'         => 'draft',
            'sender_name'    => 'Test User',
            'sender_email'   => '<EMAIL>',
            'study_id'       => $this->getStudy()->id,
            'created_at'     => '2021-01-01 00:00:00',
            'updated_at'     => '2021-01-01 00:00:00',
        ]);
    }

    private function createDefaultSmsContent(): SmsContent
    {
        if (! $this->getStudy()) {
            throw new \Exception('Study not found. Ensure createDefaultStudy is called first.');
        }

        return SmsContent::factory()->create([
            'name'       => 'Test SMS Content',
            'status'     => 'draft',
            'message'    => null,
            'study_id'   => $this->getStudy()->id,
            'created_at' => '2021-01-01 00:00:00',
            'updated_at' => '2021-01-01 00:00:00',
        ]);
    }
}
