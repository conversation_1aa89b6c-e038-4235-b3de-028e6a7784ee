<?php

namespace App\Playwright\Seeders\Visual;

use App\Models\StudyRole;
use App\Models\User;
use App\Models\UserPreference;

class UserSeeder extends BaseVisualSeeder
{
    /**
     * Run the seeder to create the default user, study roles, and user preferences for Playwright visual tests.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding User, Study Roles, and User Preferences (Visual)...</comment>');

        $this->createDefaultUser();
        $this->createDefaultStudyRoles();
        $this->createDefaultUserPreferences();

        $this->command?->getOutput()->writeln('<info>  User, Study Roles, and User Preferences (Visual) seeded successfully.</info>');
    }

    private function createDefaultUser(): void
    {
        $tenant = tenant();

        static::$user = User::factory()->create([
            'first_name'              => 'Test',
            'last_name'               => 'User',
            'email'                   => "test.user-{$tenant->id}@example.com",
            'password'                => 'Password.20224!', // UserFactory should handle hashing
            'last_password_change_at' => $this->getDefaultDate(),
            'created_at'              => $this->getDefaultDate(),
            'updated_at'              => $this->getDefaultDate(),
        ]);
    }

    private function createDefaultStudyRoles(): void
    {
        static::$studyRoles = [];

        foreach ($this->getRecordCount() as $count) {
            static::$studyRoles[] = StudyRole::factory()->create([
                'study_role' => "Study Role {$count}",
                'created_at' => $this->getDefaultDate(),
                'updated_at' => $this->getDefaultDate(),
            ]);
        }
    }

    private function createDefaultUserPreferences(): void
    {
        if (! static::$user) {
            throw new \Exception('User not created. Cannot create user preferences.');
        }

        static::$userPreferences = [];
        $moduleNames             = ['dashboard', 'profile_settings', 'notification_rules'];
        $preferenceDetails       = [
            ['theme' => 'dark', 'show_welcome_banner' => false, 'items_per_page' => 25],
            ['language'       => 'en-US', 'timezone' => 'America/New_York', 'email_notifications' => true],
            ['desktop_alerts' => true, 'sound_alerts' => false, 'alert_type' => 'critical_only'],
        ];

        foreach ($this->getRecordCount() as $index => $count) {
            if (! isset($moduleNames[$index]) || ! isset($preferenceDetails[$index])) {
                // Fallback if recordCount has more items than our predefined static data
                // This shouldn't happen if recordCount is [1,2,3]
                continue;
            }

            static::$userPreferences[] = UserPreference::factory()->create([
                'user_id'     => static::$user->id,
                'module_name' => $moduleNames[$index],
                'preference'  => $preferenceDetails[$index],
                'created_at'  => $this->getDefaultDate(),
                'updated_at'  => $this->getDefaultDate(),
            ]);
        }
    }
}
