<?php

namespace App\Playwright\Seeders\Visual;

use App\Enums\CalendarEngineProviderEnum;
use App\Enums\IntegrationEnum;
use App\Models\IntegrationStudy;

class IntegrationStudySeeder extends BaseVisualSeeder
{
    /**
     * Run the seeder to create integration-study links for visual tests.
     *
     * @throws \Exception if prerequisite data (study or integrations) is missing.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding Integration-Study Links (Visual)...</comment>');

        $this->ensurePrerequisites();
        $this->createIntegrationStudyLinks();

        $this->command?->getOutput()->writeln('<info>  Integration-Study Links (Visual) seeded successfully.</info>');
    }

    private function ensurePrerequisites(): void
    {
        if (! $this->getStudy()) {
            throw new \Exception('Study not found. Ensure Visual\StudySeeder is run first.');
        }

        if (empty($this->getIntegrations())) {
            $this->command?->getOutput()->writeln('<warning>  Integrations array is empty. Ensure IntegrationOptionSeeder ran and fetched integrations.</warning>');
        }
    }

    private function createIntegrationStudyLinks(): void
    {
        static::$integrationStudies[] = [];

        static::$integrationStudies[] = IntegrationStudy::factory()->create([
            'integration_id' => $this->getIntegrations()[IntegrationEnum::obvioHealth()->value]->id,
            'study_id'       => $this->getStudy()->id,
            'is_active'      => 1,
            'study_alias'    => 'obvioStudy',
            'options'        => [
                'base_url'             => 'https://obviohealth.example.com/api',
                'client_id'            => 'obvio_client_id_123',
                'oauth_url'            => 'https://obviohealth.example.com/oauth/token',
                'study_code'           => 'OBV_STUDY_001',
                'client_secret'        => 'super_secret_obvio_client_secret',
                'external_system_code' => 'OBV_SYS_CODE',
            ],
            'created_at'     => $this->getDefaultDate(),
            'updated_at'     => $this->getDefaultDate(),
        ]);

        static::$integrationStudies[] = IntegrationStudy::factory()->create([
            'integration_id' => $this->getIntegrations()[IntegrationEnum::rippleScience()->value]->id,
            'study_id'       => $this->getStudy()->id,
            'is_active'      => 1,
            'study_alias'    => 'rippleStudy',
            'options'        => [
                'api_key'  => 'ripple_api_key_static_value',
                'base_url' => 'https://api.ripplescience.com/v1',
                'hmac_key' => 'ripple_hmac_key_static_value_shhh',
            ],
            'created_at'     => $this->getDefaultDate(),
            'updated_at'     => $this->getDefaultDate(),
        ]);

        static::$integrationStudies[] = IntegrationStudy::factory()->create([
            'integration_id' => $this->getIntegrations()[IntegrationEnum::calendarEngine()->value]->id,
            'study_id'       => $this->getStudy()->id,
            'is_active'      => 1,
            'study_alias'    => 'calendlyStudy',
            'options'        => [
                'calendly_team'  => [
                    'uri'    => 'https://api.calendly.com/organizations/ORG_UUID/teams/TEAM_UUID_STATIC',
                    'name'   => 'Static Test Calendly Team',
                    'type'   => 'Team',
                    'uuid'   => 'TEAM_UUID_STATIC_HERE_012345',
                    'hidden' => true,
                ],
                'provider_alias' => CalendarEngineProviderEnum::calendly()->value,
            ],
            'created_at'     => $this->getDefaultDate(),
            'updated_at'     => $this->getDefaultDate(),
        ]);
    }
}
