<?php

namespace App\Playwright\Seeders\Visual;

use App\Models\Question;
use App\Models\Situation;
use App\Models\SituationGroup;
use App\Models\SituationGroupCondition;

class SituationSeeder extends BaseVisualSeeder
{
    private ?Question $oneOfQuestion = null;

    private ?Question $numberQuestion = null;

    private ?SituationGroup $situationGroup = null;

    /**
     * Run the database seeds.
     *
     * @throws \RuntimeException If prerequisites are not met.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding Situation, Group, and Conditions (Visual)...</comment>');

        $this->ensurePrerequisites();
        $this->createDefaultSituation();
        $this->createDefaultSituationGroup();
        $this->createSituationGroupConditions();

        $this->getSituation()
            ->refresh()
            ->load('situationGroups.situationGroupConditions.question.questionType');

        $this->command?->getOutput()->writeln('<info>  Situation, Group, and Conditions (Visual) seeded successfully.</info>');
    }

    /**
     * Ensures that all prerequisite data is available.
     *
     * @throws \RuntimeException If prerequisites are not met.
     */
    private function ensurePrerequisites(): void
    {
        if (! static::$campaignForm) {
            throw new \RuntimeException(
                'CampaignForm not found in BaseVisualSeeder. Ensure CampaignFormSeeder runs before SituationSeeder.',
            );
        }

        if (empty(static::$questions)) {
            throw new \RuntimeException(
                'Questions not found in BaseVisualSeeder. Ensure QuestionSeeder runs before SituationSeeder.',
            );
        }

        foreach (static::$questions as $question) {
            if ($question->questionType->name === 'oneof') {
                $this->oneOfQuestion = $question;
            }
            if ($question->questionType->name === 'number') {
                $this->numberQuestion = $question;
            }
        }

        if (! $this->oneOfQuestion) {
            throw new \RuntimeException('QuestionType with name "oneof" not found among seeded Questions. Ensure QuestionSeeder correctly seeds this type and it is available in static::$questions.');
        }

        if (! $this->numberQuestion) {
            throw new \RuntimeException('QuestionType with name "number" not found among seeded Questions. Ensure QuestionSeeder correctly seeds this type and it is available in static::$questions.');
        }
    }

    /**
     * Creates the default Situation.
     */
    private function createDefaultSituation(): void
    {
        static::$situation = Situation::factory()->create([
            'campaign_form_id' => static::$campaignForm->id,
            'name'             => 'Test Situation',
            'qualified_url'    => 'http://google.com',
            'order_by'         => 0,
            'created_at'       => $this->getDefaultDate(),
            'updated_at'       => $this->getDefaultDate(),
        ]);
    }

    /**
     * Creates the default SituationGroup linked to the seeded Situation.
     */
    private function createDefaultSituationGroup(): void
    {
        if (! static::$situation) {
            throw new \RuntimeException('Situation not created. Ensure createDefaultSituation() runs first.');
        }

        $this->situationGroup = SituationGroup::factory()->create([
            'situation_id'   => static::$situation->id,
            'group_operator' => '&',
            'operator'       => '&',
            'level'          => 1,
            'order_by'       => 0,
            'created_at'     => $this->getDefaultDate(),
            'updated_at'     => $this->getDefaultDate(),
        ]);
    }

    /**
     * Creates the SituationGroupConditions for specific questions.
     */
    private function createSituationGroupConditions(): void
    {
        if (! $this->situationGroup) {
            throw new \RuntimeException('Situation Group not created. Ensure createDefaultSituationGroup() runs first.');
        }

        SituationGroupCondition::factory()->create([
            'situation_group_id' => $this->situationGroup->id,
            'question_id'        => $this->oneOfQuestion->id,
            'operator'           => 'eq',
            'conditions'         => [$this->oneOfQuestion->options['en'][0]],
            'order_by'           => 0,
            'created_at'         => $this->getDefaultDate(),
            'updated_at'         => $this->getDefaultDate(),
        ]);

        SituationGroupCondition::factory()->create([
            'situation_group_id' => $this->situationGroup->id,
            'question_id'        => $this->numberQuestion->id,
            'operator'           => 'eq',
            'conditions'         => 1,
            'order_by'           => 1,
            'created_at'         => $this->getDefaultDate(),
            'updated_at'         => $this->getDefaultDate(),
        ]);
    }
}
