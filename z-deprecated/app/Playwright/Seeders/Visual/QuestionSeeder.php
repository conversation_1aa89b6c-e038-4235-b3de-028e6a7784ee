<?php

namespace App\Playwright\Seeders\Visual;

use App\Models\Question;
use App\Models\QuestionType;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;

class QuestionSeeder extends BaseVisualSeeder
{
    private EloquentCollection $questionTypes;

    /**
     * Run the database seeds.
     *
     * @throws \RuntimeException If prerequisites are not met.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding Questions (Visual)...</comment>');

        $this->ensurePrerequisites();
        $this->createDefaultQuestions();

        $this->command?->getOutput()->writeln('<info>  Questions (Visual) seeded successfully.</info>');
    }

    /**
     * Ensures that all prerequisite data is available.
     *
     * @throws \RuntimeException If prerequisites are not met.
     */
    private function ensurePrerequisites(): void
    {
        if (! static::$campaignForm) {
            throw new \RuntimeException(
                'CampaignForm not found in BaseVisualSeeder. Ensure CampaignFormSeeder runs before QuestionSeeder.',
            );
        }

        $questionTypeNames   = ['firstName', 'lastName', 'email', 'number', 'oneof'];
        $this->questionTypes = QuestionType::whereIn('name', $questionTypeNames)->get();

        if ($this->questionTypes->count() !== count($questionTypeNames)) {
            $missingNames = array_diff(
                $questionTypeNames,
                $this->questionTypes->pluck('alias')->all(),
            );

            throw new \RuntimeException(
                'Missing required QuestionTypes with aliases: ' . implode(', ', $missingAliases) . '. Ensure these QuestionTypes are seeded.',
            );
        }
    }

    /**
     * Creates the default questions for visual testing.
     */
    private function createDefaultQuestions(): void
    {
        $order = 0;

        foreach ($this->questionTypes as $questionType) {
            $question = Question::factory()->create([
                'campaign_form_id' => $this->getCampaignForm()->id,
                'question_type_id' => $questionType->id,
                'question'         => ['en' => $questionType->alias],
                'is_required'      => $questionType->is_allow_required ?? false,
                'is_predefined'    => $questionType->is_predefined ?? false,
                'is_encrypted'     => 0,
                'options'          => $questionType->name == 'oneof' ? [
                    'en' => [
                        ['label' => 'Yes', 'value' => '2eadff28-810a-4128-bb8d-352bfebcdadd'],
                        ['label' => 'No', 'value' => 'ca6415fd-b9dc-4821-8438-849d20023e2e'],
                    ],
                ] : null,
                'condition'        => null,
                'conditionSource'  => null,
                'level'            => 0,
                'order_by'         => $order++,
                'created_at'       => $this->getDefaultDate(),
                'updated_at'       => $this->getDefaultDate(),
            ]);

            $question->refresh()->load('questionType');

            static::$questions[] = $question;
        }
    }
}
