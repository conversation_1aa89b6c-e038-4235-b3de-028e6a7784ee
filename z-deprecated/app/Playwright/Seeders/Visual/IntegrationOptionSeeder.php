<?php

namespace App\Playwright\Seeders\Visual;

use App\Enums\IntegrationEnum;
use App\Models\Integration;
use App\Models\IntegrationOption;

class IntegrationOptionSeeder extends BaseVisualSeeder
{
    /**
     * Run the seeder to create integration options for visual tests.
     */
    public function run(): void
    {
        $this->command?->getOutput()->writeln('<comment>  Seeding Integration Options (Visual)...</comment>');

        $this->createDefaultIntegrationOptions();

        $this->command?->getOutput()->writeln('<info>  Integration Options (Visual) seeded successfully.</info>');
    }

    private function createDefaultIntegrationOptions(): void
    {
        $integrationsData = [
            IntegrationEnum::obvioHealth()->value    => [
                'options' => [
                    'base_url'             => 'https://api.obviohealth.com/v1',
                    'client_id'            => 'obvio_client_id_static_value',
                    'oauth_url'            => 'https://api.obviohealth.com/oauth/token',
                    'client_secret'        => 'obvio_client_secret_static_value_shhh',
                    'external_system_code' => 'OBV_SYS_001',
                ],
            ],
            IntegrationEnum::rippleScience()->value  => [
                'options' => [
                    'api_key'  => 'ripple_api_key_static_value',
                    'base_url' => 'https://api.ripplescience.com/v1',
                    'hmac_key' => 'ripple_hmac_key_static_value_shhh',
                ],
            ],
            IntegrationEnum::calendarEngine()->value => [
                'options' => [
                    'provider_alias'        => 'calendly',
                    'signing_secret'        => 'calendly_signing_secret_static_shhh',
                    'personal_access_token' => 'calendly_pat_static_xxxxxxxxxxxx',
                ],
            ],
        ];

        foreach ($integrationsData as $integrationName => $data) {
            $integration = $this->getIntegrations()[$integrationName] ?? null;

            if (! $integration) {
                throw new \Exception("Integration with name '{$integrationName}' not found. Please ensure it is seeded.");
            }

            static::$integrationOptions[$integrationName] = IntegrationOption::factory()->create([
                'integration_id' => $integration->id,
                'is_active'      => 1,
                'options'        => $data['options'],
                'created_at'     => $this->getDefaultDate(),
                'updated_at'     => $this->getDefaultDate(),
            ]);
        }
    }
}
