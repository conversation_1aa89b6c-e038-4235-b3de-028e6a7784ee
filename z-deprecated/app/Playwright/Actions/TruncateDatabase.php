<?php

namespace App\Playwright\Actions;

use App\Models\Tenant;
use App\Playwright\Concerns\PlaywrightDatabaseTruncation;
use Illuminate\Foundation\Application;

class TruncateDatabase
{
    use PlaywrightDatabaseTruncation;

    protected Application $app;

    public function __construct(Application $app)
    {
        $this->app = $app;
    }

    public function handle(?Tenant $tenant = null)
    {
        if ($this->app->environment() !== 'playwright') {
            throw new \Exception('This command can only be run in the playwright environment.');
        }

        if (! $tenant) {
            throw new \Exception('The tenant is required to truncate the database.');
        }

        $this->setTenant($tenant);

        $this->truncateDatabaseTables();
    }
}
