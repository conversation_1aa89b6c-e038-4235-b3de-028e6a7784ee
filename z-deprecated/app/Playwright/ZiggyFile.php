<?php

namespace App\Playwright;

use Tighten\Ziggy\Output\File;

class ZiggyFile extends File
{
    public function __toString(): string
    {
        $json = $this->ziggy->toJson();
        $json = str($json)
            ->replace("\/", '/')
            ->value();

        return <<<JAVASCRIPT
        const Ziggy = {$json};
        if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
          Object.assign(Ziggy.routes, window.Ziggy.routes);
        }
        export { Ziggy };

        JAVASCRIPT;
    }
}
