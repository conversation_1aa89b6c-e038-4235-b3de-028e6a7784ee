#!/bin/bash

echo '  1. Run initialize database'

cd ./

if [[ -z "$CI" ]]; then
    output=$(docker compose exec test_core_laravel_tenancy sh -c "php artisan playwright:initialize-database" 2>&1)
    exit_code=$?
else
    output=$(docker compose exec --user www-data test_core_laravel_tenancy sh -c "php artisan playwright:initialize-database" 2>&1)
    exit_code=$?
fi

if [ $exit_code -ne 0 ]; then
    # Format the error message in a way that can be parsed later
    echo "PLAYWRIGHT_ERROR: $output" >&2
    exit 1
fi

exit 0
