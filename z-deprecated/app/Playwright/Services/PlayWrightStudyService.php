<?php

namespace App\Playwright\Services;

use App\Models\Study;
use App\Models\User;
use Illuminate\Support\Arr;

class PlayWrightStudyService
{
    public function assignUsersToStudies(array|int $studyIds, array|int $userIds)
    {
        $studyIds = Arr::wrap($studyIds);
        $userIds  = Arr::wrap($userIds);
        $users    = User::whereIn('id', $userIds)->get();
        $studies  = Study::whereIn('id', $studyIds)->get();

        $studies
            ->each(function (Study $study) use ($users) {
                $users->each(fn (User $user) => $study->users()->attach($user));
            });

        $studies->each->load('users');

        return $studies;
    }

    /**
     * Update a study with the given attributes.
     *
     * @param  string     $uuid       The UUID of the study to update.
     * @param  array      $attributes The attributes to update.
     * @return Study|null The updated study or null if not found.
     */
    public function updateStudy(string $uuid, array $attributes): ?Study
    {
        $study = Study::where('uuid', $uuid)->first();

        if ($study) {
            $study->update($attributes);

            return $study->refresh(); // Return the updated model
        }

        return null; // Or throw an exception
    }
}
