<?php

namespace App\Playwright\Console\Commands;

use App\Playwright\Concerns\PlaywrightDatabaseTruncation;
use Illuminate\Console\Command;
use Illuminate\Foundation\Application;
use Stancl\Tenancy\Concerns\HasTenantOptions;
use Stancl\Tenancy\Concerns\TenantAwareCommand;

class TruncateDatabase extends Command
{
    use HasTenantOptions;
    use PlaywrightDatabaseTruncation;
    use TenantAwareCommand;

    protected $signature = 'playwright:truncate-database {--tenants=*}';

    protected $description = 'Truncate the database for the Playwright test suite.';

    protected Application $app;

    public function __construct(Application $app)
    {
        parent::__construct();

        $this->app = $app;
    }

    public function handle()
    {
        $tenant = tenant();

        if ($this->app->environment() !== 'playwright') {
            throw new \Exception('This command can only be run in the playwright environment.');
        }

        $this->setTenant($tenant);

        $this->truncateDatabaseTables();
    }
}
