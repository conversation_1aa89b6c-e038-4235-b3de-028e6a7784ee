<?php

namespace App\Playwright\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Foundation\Application;
use Stancl\Tenancy\Database\Models\Domain;
use Tests\Concerns\InitializesDatabasesTrait;

class InitializeDatabase extends Command
{
    use InitializesDatabasesTrait;

    protected $signature = 'playwright:initialize-database';

    protected $description = 'Initialize the database for the Playwright test suite.';

    protected Application $app;

    public function __construct(Application $app)
    {
        parent::__construct();

        $this->app = $app;
    }

    public function handle()
    {
        if ($this->app->environment() !== 'playwright') {
            throw new \Exception('This command can only be run in the playwright environment.');
        }

        $this->initializeDatabases();
        $this->seedDatabases();

        Domain::where('domain', 'like', '%.test%')
            ->each(function ($domain) {
                $domain->domain = str_replace('.test', '.localhost', $domain->domain);
                dump($domain->domain);
                $domain->save();
            });
    }
}
