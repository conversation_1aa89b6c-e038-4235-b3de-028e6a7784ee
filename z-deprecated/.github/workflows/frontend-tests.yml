name: Frontend Tests

on:
  pull_request:
    types: [ opened, reopened, synchronize, ready_for_review ]

jobs:
  playwright:
    name: P<PERSON> Browser e2e Tests
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Prepare The Environment
        run: cp .env.testing .env

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Docker Image
        run: APP_ENV=testing docker buildx bake --allow=fs.write=/ --file docker-compose.yml --file docker-compose-cache.json --load

      - name: Start Docker Compose Services
        run: APP_ENV=testing docker compose --profile testing --file docker-compose.yml up -d

      - name: Install Composer Dependencies
        env:
          GITHUB_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          # Exit immediately if a command exits with a non-zero status.
          # Configure token globally (as user www-data)
          # Configure safe directory (as root or git user)
          # Run composer install (as user www-data), passing the token explicitly via -e
          # Optional verification
        run: |
          set -e

          docker compose --profile testing exec -T \
          -e GITHUB_ACCESS_TOKEN=${{ secrets.GH_ACCESS_TOKEN }} \
          test_core_laravel_tenancy \
          composer config --global github-oauth.github.com $GITHUB_ACCESS_TOKEN

          echo "--- Running Composer Install ---"

          docker compose --profile testing exec -T \
          -e GITHUB_ACCESS_TOKEN=${{ secrets.GH_ACCESS_TOKEN }} \
          test_core_laravel_tenancy \
          composer install --no-interaction --no-progress  --optimize-autoloader

      - name: Generate Application Key
        # Run artisan commands as www-data (UID 1000)
        run: docker compose --profile testing exec -T test_core_laravel_tenancy php artisan key:generate

      - name: Change Permissions & Clear Cache
        run: |
          docker compose --profile testing exec -T test_core_laravel_tenancy php artisan optimize:clear
          docker compose --profile testing exec -T test_core_laravel_tenancy chown -R www-data:www-data /var/www/html/storage /var/www/html/bootstrap/cache

      # OPTIMIZED: Rely on the runner's persistent npm cache (~/.npm)
      - name: Install NPM Dependencies
        env:
          FONTAWESOME_NPM_AUTH_TOKEN: ${{ secrets.FONTAWESOME_NPM_AUTH_TOKEN }}
          TIPTAP_PRO_TOKEN: ${{ secrets.TIPTAP_PRO_TOKEN }}
        run: |
          npm config set "//npm.fontawesome.com/:_authToken" $FONTAWESOME_NPM_AUTH_TOKEN
          npm config set "@tiptap-pro:registry" https://registry.tiptap.dev/
          npm config set "@tiptap-cloud:registry" https://registry.tiptap.dev/
          npm config set "//registry.tiptap.dev/:_authToken" $TIPTAP_PRO_TOKEN
          npm ci

      - name: Build Frontend Assets
        run: npm run pw:build

      - name: Update package lists and accept PPA label changes
        run: sudo apt-get update --allow-releaseinfo-change

      - name: Playwright Browsers
        run: npx playwright install --with-deps

      - name: Run Playwright Server
        run: |
          npx playwright run-server --host 0.0.0.0 --port 9222 &
          sleep 5 # Give the server a moment to start up
        env:
          GH_PAT: ${{ github.token }}

      - name: Run Browser Tests
        if: always()
        run: docker compose --profile testing exec -T test_core_laravel_tenancy vendor/bin/pest --group frontend --log-events-verbose-text tests/Browser/test.log

      - name: Accessing Laravel logs
        if: always()
        run: |
          docker compose cp test_core_laravel_tenancy:/var/www/html/storage/logs/laravel.log browser_test_laravel_logs.txt || \
          echo 'No Laravel logs found' > browser_test_laravel_logs.txt

      - name: Accessing Browser Test logs
        if: always()
        run: |
          docker compose cp test_core_laravel_tenancy:/var/www/html/tests/browser/test.log browser_test_logs.txt || \
          echo 'No Laravel test logs found' > browser_test_logs.txt

      - name: Upload Laravel Logs
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: browser_test_laravel_logs.txt
          path: browser_test_laravel_logs.txt
          retention-days: 14

      - name: Upload Browser Test Logs
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: browser_test_logs.txt
          path: browser_test_logs.txt
          retention-days: 14
