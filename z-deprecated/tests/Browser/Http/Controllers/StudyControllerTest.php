<?php

namespace Tests\Browser\Http\Controllers;

use App\Models\Client;
use App\Models\Study;
use App\Models\TherapeuticArea;
use App\Models\UserPreference;
use Lara<PERSON>\Dusk\Browser;
use Tests\Browser\Components\DatePicker;
use Tests\Browser\Pages\Studies\StudyCreateEdit;
use Tests\Browser\Pages\Studies\StudyList;
use Tests\DuskTestCase;

class StudyControllerTest extends DuskTestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->createAuthUser('superAdmin');

        $this->markTestSkipped('Need to fix route redirection on login by role');
    }

    public function test_list_studies(): void
    {
        $studies = Study::factory()->count(5)->create();

        $this->browse(function (Browser $browser) use ($studies) {
            $browser = $this->loginBrowserUser($browser);

            $browser
                ->waitForText('/ page')
                ->assertSee($studies->first()->study_name);
        });
    }

    public function test_navigate_to_create_study(): void
    {
        $this->browse(function (Browser $browser) {
            $browser = $this->loginBrowserUser($browser);

            $browser
                ->pause(5000)
                ->visit(new StudyList)
                ->waitForText('Studies')
                ->screenshot('after_login')
                ->waitFor('@actions-button')
                ->click('@actions-button')
                ->waitForLink('Create Study')
                ->clickLink('Create Study')
                ->waitForRoute('admin.studies.create')
                ->assertRouteIs('admin.studies.create');
        });
    }

    public function test_create_study(): void
    {
        $clients         = Client::factory()->count(5)->create();
        $client          = $clients->random();
        $sponsor         = $clients->random();
        $therapeuticArea = TherapeuticArea::orderBy('name')->limit(20)->get()->random();

        $payload = [
            'study_name'           => $this->faker->name(),
            'audience_description' => $this->faker->text(),
            'total_budget'         => $this->faker->randomFloat(2, 1000, 100000),
            'additional_revenue'   => $this->faker->randomFloat(2, 1000, 100000),
        ];

        UserPreference::create([
            'user_id'     => $this->centralUser->id,
            'module_name' => 'sidebar',
            'global_id'   => $this->faker->unique()->uuid(),
            'preference'  => ['collapsed' => true],
        ]);

        $this->browse(function (Browser $browser) use ($client, $sponsor, $therapeuticArea, $payload) {
            $browser = $this->loginBrowserUser($browser);

            $browser
                ->pause(5000)
                ->visit(new StudyCreateEdit)
                ->waitFor('@study-name-input')
                ->type('@study-name-input', $payload['study_name'])
                ->type('@audience-description-input', $payload['audience_description'])

                ->selectFromDropdown('@client-select', '@client-select-focused', $client->client_name)
                ->selectFromDropdown('@sponsor-select', '@sponsor-select-focused', $sponsor->client_name)
                ->selectFromDropdown('@therapeutic-area-select', '@therapeutic-area-select-focused', $therapeuticArea->name)

                ->within(new DatePicker(), function (Browser $browser) {
                    $date = $this->faker->dateTimeBetween('-1 year', '-1 month');

                    $browser->selectdate('@start-date-input', $date->format('m-d-Y'), $date->format('Y-m-d'));
                })
                ->pause(500)
                ->within(new DatePicker(), function (Browser $browser) {
                    $date = $this->faker->dateTimeBetween('+1 month', '+1 year');

                    $browser->selectdate('@finish-date-input', $date->format('m-d-Y'), $date->format('Y-m-d'));
                })
                ->pause(500)

                ->type('@total-budget-input', $payload['total_budget'])
                ->pause(500)
                ->type('@additional-revenue-input', $payload['additional_revenue'])
                ->pause(500)

                ->press('Save Study')
                ->pause(500)
                ->waitForText('Study Added Successfully')

                ->screenshot('after_save_study')
                ->assertSee('Study Added Successfully');
        });

        $this->assertDatabaseHas('studies', $payload + [
            'client_id'           => $client->id,
            'sponsor_id'          => $sponsor->id,
            'therapeutic_area_id' => $therapeuticArea->id,
        ]);
    }

    public function test_edit_study(): void
    {
        $clients         = Client::factory()->count(5)->create();
        $study           = Study::factory()->create();
        $client          = $clients->random();
        $sponsor         = $clients->random();
        $therapeuticArea = TherapeuticArea::orderBy('name')->limit(20)->get()->random();
        $payload         = [
            'study_name'           => $this->faker->name(),
            'audience_description' => $this->faker->text(),
            'total_budget'         => $this->faker->randomFloat(2, 1000, 100000),
            'additional_revenue'   => $this->faker->randomFloat(2, 1000, 100000),
        ];

        $this->browse(function (Browser $browser) use ($study, $client, $sponsor, $therapeuticArea, $payload) {
            $study->refresh();

            $browser = $this->loginBrowserUser($browser);

            $browser
                ->visit(new StudyCreateEdit($study->uuid))
                ->waitFor('@study-name-input')
                ->click('.study-name-input .ant-input-clear-icon')
                ->pause(500)
                ->type('@study-name-input', $payload['study_name'])

                ->click('.audience-description-input .ant-input-clear-icon')
                ->pause(500)
                ->type('@audience-description-input', $payload['audience_description'])

                ->selectFromDropdown('@client-select', '@client-select-focused', $client->client_name)
                ->selectFromDropdown('@sponsor-select', '@sponsor-select-focused', $sponsor->client_name)
                ->selectFromDropdown('@therapeutic-area-select', '@therapeutic-area-select-focused', $therapeuticArea->name)

                ->click('.start-on-input .ant-picker-clear')
                ->pause(500)
                ->within(new DatePicker(), function (Browser $browser) {
                    $date = $this->faker->dateTimeBetween('-1 year', '-1 month');

                    $browser->selectdate('@start-date-input', $date->format('m-d-Y'), $date->format('Y-m-d'));
                })
                ->pause(500)
                ->click('.finish-on-input .ant-picker-clear')
                ->pause(500)
                ->within(new DatePicker(), function (Browser $browser) {
                    $date = $this->faker->dateTimeBetween('+1 month', '+1 year');

                    $browser->selectdate('@finish-date-input', $date->format('m-d-Y'), $date->format('Y-m-d'));
                })
                ->pause(500)

                ->type('@total-budget-input', $payload['total_budget'])
                ->pause(500)
                ->type('@additional-revenue-input', $payload['additional_revenue'])
                ->pause(500)

                ->press('Save Study')
                ->pause(500)
                ->waitForText('Study Updated Successfully')

                ->screenshot('after_save_study')
                ->assertSee('Study Updated Successfully');
        });

        $this->assertDatabaseHas('studies', $payload + [
            'client_id'           => $client->id,
            'sponsor_id'          => $sponsor->id,
            'therapeutic_area_id' => $therapeuticArea->id,
        ]);
    }
}
