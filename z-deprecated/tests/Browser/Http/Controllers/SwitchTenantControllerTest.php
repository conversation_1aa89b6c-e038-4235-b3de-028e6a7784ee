<?php

namespace Tests\Browser\Http\Controllers;

use App\Models\Tenant;
use App\Models\User;
use Lara<PERSON>\Dusk\Browser;
use Stancl\Tenancy\Facades\Tenancy;
use Tests\DuskTestCase;

class SwitchTenantControllerTest extends DuskTestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->createAuthUser('superAdmin');
    }

    public function test_can_switch_between_all_tenants_and_verify_correct_tenant_user(): void
    {
        $tenants             = Tenant::where('tenants.id', '<>', tenant()->id)->limit(1)->get();
        $firstTenantDomain   = tenant()->getDefaultDomain();
        $firstTenantStudyUrl = tenant_route($firstTenantDomain, 'studies.index');

        /**
         * Create several users in each tenant so that the auth user's primary key is
         * different across all tenants
         */

        /** Create default tenant users */
        User::factory()->count(3)->create();

        /** Create users for each of the other tenants */
        $tenants->each(function ($tenant) {
            $tenant->run(function () {
                User::factory()->count(rand(4, 8))->create();
            });
        });

        /** Creat the auth user */
        $this->createAuthUser('superAdmin');

        /** Attach the auth user to all tenants */
        $this->centralUser->tenants()->attach($tenants);

        $this->browse(function (Browser $browser) use ($tenants, $firstTenantStudyUrl) {
            $browser = $this->loginBrowserUser($browser);

            $browser
                ->waitForText('Study Hub')
                ->assertTenantAuthUserAs($this->tenantUser)
                ->pause(500)
                ->visit($firstTenantStudyUrl)
                ->waitForText('Study Hub')
                ->assertSee(tenant()->name);

            /** Assign the authenticated user the super admin role on the other tenants */
            $tenants->each(function ($tenant) use ($browser) {
                $tenantUser = null;

                $this->endTenancy();

                $tenant
                    ->run(function () use (&$tenantUser) {
                        $tenantUser = Tenancy::tenantUserFromCentralUser($this->centralUser);
                        $this->setTenantAsBaseUrl();
                        $this->assignUserRole($tenantUser, 'superAdmin');
                    });

                $domain   = $tenant->getDefaultDomain();
                $studyUrl = tenant_route($domain, 'studies.index');
                $uuid     = str_replace('-', '', $tenant->uuid);

                $browser
                    ->click('@switch-tenant-button')
                    ->pause(500)
                    ->waitForText($tenant->name)
                    ->assertSeeIn('.switch-tenant-dropdown', $tenant->name)
                    ->pause(500)
                    ->click("@switch-tenant-{$uuid}")
                    ->waitForText($tenant->name)
                    ->pause(1000)
                    ->assertTenantAuthUserAs($tenantUser)
                    ->pause(1000)
                    ->visit($studyUrl)
                    ->waitForText('Study Hub')
                    ->pause(1000);

                $this->startTenancy();
            });
        });
    }
}
