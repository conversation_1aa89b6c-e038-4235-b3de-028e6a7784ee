<?php

namespace Tests\Browser\Pages\Auth;

use <PERSON><PERSON>\Dusk\Browser;
use <PERSON><PERSON>\Dusk\Page;

class Login extends Page
{
    /**
     * Get the URL for the page.
     */
    public function url(): string
    {
        return route('login', [], false);
    }

    /**
     * Assert that the browser is on the page.
     */
    public function assert(Browser $browser): void
    {
        $browser->assertPathIs($this->url());
    }

    /**
     * Get the element shortcuts for the page.
     *
     * @return array<string, string>
     */
    public function elements(): array
    {
        return [];
    }

    public function loginUser(Browser $browser, string $email, string $password): Browser
    {
        $browser
            ->type('@email-input', $email)
            ->screenshot('login-email')
            ->type('@password-input', $password)
            ->screenshot('password-input')
            ->click('@login-button');

        return $browser;
    }
}
