<?php

namespace Tests\Browser\Pages\Studies;

use <PERSON><PERSON>\Dusk\Browser;
use <PERSON><PERSON>\Dusk\Page;

class StudyList extends Page
{
    /**
     * Get the URL for the page.
     */
    public function url(): string
    {
        return route('studies.index', [], false);
    }

    /**
     * Assert that the browser is on the page.
     */
    public function assert(Browser $browser): void
    {
        $browser->assertPathIs($this->url());
    }

    /**
     * Get the element shortcuts for the page.
     *
     * @return array<string, string>
     */
    public function elements(): array
    {
        return [
            '@actions-button' => '.study-actions-button',
        ];
    }
}
