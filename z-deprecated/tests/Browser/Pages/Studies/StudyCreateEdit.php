<?php

namespace Tests\Browser\Pages\Studies;

use <PERSON><PERSON>\Dusk\Browser;
use <PERSON><PERSON>\Dusk\Page;
use Tests\Browser\Components\ReactSelect;

class StudyCreateEdit extends Page
{
    public function __construct(protected ?string $study_uuid = null)
    {
    }

    /**
     * Get the URL for the page.
     */
    public function url(): string
    {
        if ($this->study_uuid) {
            return route('admin.studies.edit', ['study' => $this->study_uuid], false);
        }

        return route('admin.studies.create', [], false);
    }

    /**
     * Assert that the browser is on the page.
     */
    /*public function assert(Browser $browser): void
    {
        $browser->assertPathIs($this->url());
    }*/

    /**
     * Get the element shortcuts for the page.
     *
     * @return array<string, string>
     */
    public function elements(): array
    {
        return [
            '@client-select'                   => '#client-select .react-select__control',
            '@client-select-focused'           => '#client-select .react-select__control--is-focused',
            '@sponsor-select'                  => '#sponsor-select .react-select__control',
            '@sponsor-select-focused'          => '#sponsor-select .react-select__control--is-focused',
            '@therapeutic-area-select'         => '#therapeutic-area-select .react-select__control',
            '@therapeutic-area-select-focused' => '#therapeutic-area-select .react-select__control--is-focused',
        ];
    }

    public function selectFromDropdown(Browser $browser, $selector, $focusedSelector, $value)
    {
        $browser
            ->within(new ReactSelect($selector, $focusedSelector), function (Browser $browser) use ($value) {
                $browser->selectSingleOption($value);
            });
    }
}
