<?php

namespace Tests\Browser\Components;

use Facebook\WebDriver\WebDriverBy;
use <PERSON><PERSON>\Dusk\Browser;
use <PERSON><PERSON>\Dusk\Component as BaseComponent;

class DatePicker extends BaseComponent
{
    public function __construct()
    {
    }

    /**
     * Get the root selector for the component.
     */
    public function selector(): string
    {
        return '';
    }

    /**
     * Get the element shortcuts for the component.
     *
     * @return array<string, string>
     */
    public function elements(): array
    {
        return [];
    }

    public function selectDate(Browser $browser, string $selector, string $value, string $title): void
    {
        $browser
            ->type($selector, $value)
            ->pause(1000)
            ->driver->findElement(WebDriverBy::xpath("//td[@title='{$title}']"))
            ->click();

        $browser
            ->pause(500);
    }
}
