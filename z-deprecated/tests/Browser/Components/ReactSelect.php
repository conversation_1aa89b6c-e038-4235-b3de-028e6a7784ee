<?php

namespace Tests\Browser\Components;

use Facebook\WebDriver\WebDriverBy;
use Facebook\WebDriver\WebDriverKeys;
use <PERSON><PERSON>\Dusk\Browser;
use <PERSON><PERSON>\Dusk\Component as BaseComponent;

class ReactSelect extends BaseComponent
{
    public function __construct(protected string $selector = '#react-select', protected string $focusedSelector = '')
    {
    }

    /**
     * Get the root selector for the component.
     */
    public function selector(): string
    {
        return '';
    }

    /**
     * Assert that the browser page contains the component.
     */
    public function assert(Browser $browser): void
    {
        $browser->assertVisible($this->selector());
    }

    /**
     * Get the element shortcuts for the component.
     *
     * @return array<string, string>
     */
    public function elements(): array
    {
        return [];
    }

    public function selectSingleOption(Browser $browser, string $option): void
    {
        $browser
            ->click($this->selector)
            ->waitForText($option, 30)
            ->driver->findElement(WebDriverBy::xpath("//div[contains(@class, 'react-select__option') and text() = \"{$option}\"]"))
            ->click();

        $browser
            ->pause(500);

        $browser->driver->getKeyboard()->sendKeys(WebDriverKeys::TAB);

        if ($this->focusedSelector) {
            $browser
                ->waitUntilMissing($this->focusedSelector, 30);
        }

        $browser
            ->pause(500);
    }
}
