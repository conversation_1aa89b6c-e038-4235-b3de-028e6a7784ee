<?php

namespace Tests\Browser;

use <PERSON><PERSON>\Dusk\Browser;
use Tests\Browser\Pages\Auth\Login;
use Tests\Browser\Pages\HomePage;
use Tests\DuskTestCase;

class AuthenticatedSessionControllerTest extends DuskTestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->createAuthUser('superAdmin');

        $this->markTestSkipped('Need to fix route redirection on login by role');
    }

    public function test_homepage_redirects_to_login(): void
    {
        $this->setCentralAsBaseUrl();

        $this->browse(function (Browser $browser) {
            $browser
                ->visit(new HomePage)
                ->screenshot('homepage')
                ->on(new Login)
                ->assertRouteIs('login');
        });
    }

    public function test_super_admin_login_redirect_to_studies(): void
    {
        $this->browse(function (Browser $browser) {
            $browser = $this->loginBrowserUser($browser);

            $browser
                ->waitForRoute('studies.index', [], 30)
                ->screenshot('wait-for-route')
                ->assertRouteIs('studies.index');
        });
    }

    public function test_email_and_password_is_required(): void
    {
        $this->browse(function (Browser $browser) {
            $browser
                ->visit(new Login)
                ->click('@login-button')
                ->pause(1000)
                ->waitForText('The email field is required.', 30)
                ->waitForText('The password field is required.', 30)
                ->assertSee('The email field is required.')
                ->assertSee('The password field is required.');
        });
    }
}
