import { QuestionOption, RadioCheckboxSelectOption } from '@/types/question-options';

const getRadioCheckboxSelectOptions = (options: QuestionOption[]): RadioCheckboxSelectOption[] =>
    options.map((questionOption: QuestionOption, optionIndex: number) => ({
        index: optionIndex,
        label: questionOption.label,
        value: questionOption.value,
    }));

export default getRadioCheckboxSelectOptions;
