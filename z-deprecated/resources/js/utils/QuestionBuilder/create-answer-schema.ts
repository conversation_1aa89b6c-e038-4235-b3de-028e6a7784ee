import { ModifiedQuestion } from '@/types/question';
import { QuestionnaireSchema } from '@/types/questionnaire-schema';
import questionsToAnswerSchemas from '@/utils/QuestionBuilder/questions-to-answer-schemas';

const createAnswerSchema = (questions: ModifiedQuestion[]): QuestionnaireSchema => ({
    $schema: 'http://json-schema.org/draft-07/schema#',
    items: questionsToAnswerSchemas(questions),
    maxItems: questions.length,
    minItems: questions.length,
    type: 'array',
});

export default createAnswerSchema;
