import { FormItemSettings } from '@/types/question-builder-settings';
import { background, border, borderRadius, height, margin, padding, width } from '@/utils/QuestionBuilder/settings';

const formItems: FormItemSettings = {
    formItem: {
        container: {
            style: {
                ...background,
                ...border,
                ...borderRadius,
                ...height,
                ...margin,
                ...padding,
                ...width,
            },
        },
        control: {
            style: {
                ...background,
                ...border,
                ...borderRadius,
                ...height,
                ...margin,
                ...padding,
                ...width,
            },
        },
        label: {
            style: {
                ...background,
                ...border,
                ...borderRadius,
                ...height,
                ...margin,
                ...padding,
                ...width,
            },
        },
        row: {
            style: {
                ...background,
                ...border,
                ...borderRadius,
                ...height,
                ...margin,
                ...padding,
                ...width,
            },
        },
    },
};

export default formItems;
