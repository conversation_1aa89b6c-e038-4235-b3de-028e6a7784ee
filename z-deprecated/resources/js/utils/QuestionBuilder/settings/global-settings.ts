import { merge } from 'lodash';

import { GlobalSettings } from '@/types/question-builder-settings';
import { font } from '@/utils/QuestionBuilder/settings';
import checkbox from '@/utils/QuestionBuilder/settings/checkbox';
import form from '@/utils/QuestionBuilder/settings/form';
import formItems from '@/utils/QuestionBuilder/settings/form-items';
import input from '@/utils/QuestionBuilder/settings/input';
import multiScreen from '@/utils/QuestionBuilder/settings/multi-screen';
import radio from '@/utils/QuestionBuilder/settings/radio';
import fontWeights from '@/utils/QuestionBuilder/styles/font-weights';
import textTransform from '@/utils/QuestionBuilder/styles/text-transform';

const [{ label, value }] = textTransform.filter(transform => transform.label === 'Normal');
const [{ label: fontWeightLabel, value: fontWeightValue }] = fontWeights.filter(fontWeight =>
    fontWeight.label.includes('700')
);

const globalSettings: GlobalSettings = {
    ...checkbox,
    form: merge({}, form.form, {
        label: {
            style: {
                fontWeight: { label: fontWeightLabel, value: fontWeightValue },
                textTransform: { label, value },
            },
        },
    }),
    formItem: merge({}, formItems.formItem, {
        container: {
            style: {
                width: '100%',
            },
        },
    }),
    general: {
        customFont: {
            link: null,
        },
        style: {
            'font-family': null,
            ...font,
        },
    },
    ...input,
    ...multiScreen,
    ...radio,
};

export default globalSettings;
