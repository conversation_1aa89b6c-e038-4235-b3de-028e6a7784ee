import { merge } from 'lodash';

import { MultiScreenSettings } from '@/types/question-builder-settings';
import { background, border, font, margin, padding, width } from '@/utils/QuestionBuilder/settings';
import button from '@/utils/QuestionBuilder/settings/button';

// @ts-ignore
const multiScreen: MultiScreenSettings = {
    pageBreak: {
        container: {
            style: {
                ...background,
                ...border,
                ...margin,
                ...padding,
                ...width,
            },
        },
        header: null,
        headerContainer: {
            style: {
                ...border,
                ...margin,
                ...padding,
                ...width,
            },
        },
        nextButton: merge({}, button, {
            btnLabel: {
                text: 'Next',
            },
        }),
        paging: {
            style: {
                ...font,
            },
            title: 'Page',
        },
        showPaging: {
            value: '1',
            label: 'Yes',
        },
        title: {
            style: {
                ...font,
            },
        },
    },
};

export default multiScreen;
