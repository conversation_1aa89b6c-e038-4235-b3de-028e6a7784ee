import { InputStyleSettings } from '@/types/question-builder-settings';
import {
    background,
    border,
    borderRadius,
    font,
    height,
    margin,
    padding,
    width,
} from '@/utils/QuestionBuilder/settings';
import inputTypes from '@/utils/QuestionBuilder/styles/input-types';

const [{ label, value }] = inputTypes.filter(inputType => inputType.value === 'string');

const input: InputStyleSettings = {
    input: {
        addon: {
            style: {
                ...background,
                ...borderRadius,
                ...border,
                ...font,
            },
        },
        container: {
            style: {
                ...height,
                ...margin,
                ...padding,
                ...width,
            },
        },
        control: {
            style: {
                ...background,
                ...border,
                ...borderRadius,
                ...font,
                ...padding,
            },
        },
        type: { label, value },
    },
};

export default input;
