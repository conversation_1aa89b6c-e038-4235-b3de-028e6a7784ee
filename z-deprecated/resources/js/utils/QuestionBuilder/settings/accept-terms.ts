import dayjs from 'dayjs';

import { AcceptTermsSettings } from '@/types/question-builder-settings';
import { background, border, font, height, margin, padding, width } from '@/utils/QuestionBuilder/settings';

const acceptTerms: AcceptTermsSettings = {
    userAgreement: {
        content: {
            style: {
                ...font,
            },
        },
        container: {
            style: {
                ...background,
                ...border,
                ...height,
                ...margin,
                ...padding,
                ...width,
            },
        },
        optionsType: {
            label: 'Radio',
            value: 'radio',
        },
        radioPosition: 'bottom',
        terms: {
            time: dayjs().valueOf(),
            blocks: [
                {
                    type: 'paragraph',
                    data: {
                        text: 'Read the legal terms and agreements by clicking <a href="https://1nhealth.com/privacy-policy" target="_blank" rel="noopener noreferrer">Privacy Policy</a>',
                    },
                },
            ],
            version: '2.28.0',
        },
    },
};

export default acceptTerms;
