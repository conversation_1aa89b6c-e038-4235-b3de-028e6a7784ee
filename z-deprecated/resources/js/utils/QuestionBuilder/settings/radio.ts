import { RadioStyleSettings } from '@/types/question-builder-settings';
import { background, border, displayFlex, font, height, width } from '@/utils/QuestionBuilder/settings';

const radio: RadioStyleSettings = {
    radio: {
        button: {
            style: {
                ...background,
                ...border,
                ...height,
                ...width,
            },
        },
        checked: {
            style: {
                ...background,
                ...border,
            },
        },
        container: {
            style: {
                ...displayFlex,
            },
        },
        label: {
            style: {
                ...font,
            },
        },
        optionsType: {
            label: 'Radio',
            value: 'radio',
        },
    },
};

export default radio;
