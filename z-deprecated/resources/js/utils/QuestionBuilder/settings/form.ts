import { merge } from 'lodash';

import { FormSettings } from '@/types/question-builder-settings';
import { font } from '@/utils/QuestionBuilder/settings';
import button from '@/utils/QuestionBuilder/settings/button';

const form: FormSettings = {
    form: {
        label: {
            style: {
                ...font,
            },
        },
        submitButton: merge({}, button, {
            btnLabel: {
                text: 'Submit',
            },
        }),
    },
};

export default form;
