import { CheckboxStyleSettings } from '@/types/question-builder-settings';
import { background, border, displayFlex, font, height, width } from '@/utils/QuestionBuilder/settings';

const checkbox: CheckboxStyleSettings = {
    checkbox: {
        checked: {
            style: {
                ...background,
                ...border,
            },
        },
        container: {
            style: {
                ...displayFlex,
            },
        },
        input: {
            style: {
                ...background,
                ...border,
                ...height,
                ...width,
            },
        },
        label: {
            style: {
                ...font,
            },
        },
        optionsType: {
            label: 'Checkbox',
            value: 'checkbox',
        },
    },
};

export default checkbox;
