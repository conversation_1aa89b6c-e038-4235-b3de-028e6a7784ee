import { merge } from 'lodash';

import { ButtonSettings } from '@/types/question-builder-settings';
import { background, border, buttonProps, font, margin, padding } from '@/utils/QuestionBuilder/settings';
import { buttonShapes } from '@/utils/QuestionBuilder/styles/button-props';

const [{ label, value }] = buttonShapes.filter(buttonShape => buttonShape.value === 'rounded');

const button: ButtonSettings = {
    btnLabel: {
        style: {
            ...font,
        },
        text: null,
    },
    buttonProps: merge({}, buttonProps, {
        buttonShape: { label, value },
    }),
    button: {
        style: {
            ...background,
            ...border,
            ...margin,
            ...padding,
        },
    },
    icon: null,
};

export default button;
