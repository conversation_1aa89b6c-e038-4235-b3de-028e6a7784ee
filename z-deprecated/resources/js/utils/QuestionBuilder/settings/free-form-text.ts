import dayjs from 'dayjs';

import { FreeFormTextSettings } from '@/types/question-builder-settings';
import { background, border, font, height, margin, padding, width } from '@/utils/QuestionBuilder/settings';

const freeFormText: FreeFormTextSettings = {
    freeFormText: {
        content: {
            style: {
                ...font,
            },
        },
        container: {
            style: {
                ...background,
                ...border,
                ...height,
                ...margin,
                ...padding,
                ...width,
            },
        },
        text: {
            time: dayjs().valueOf(),
            blocks: [
                {
                    type: 'paragraph',
                    data: {
                        text: 'Please enter some text',
                    },
                },
            ],
            version: '2.28.0',
        },
    },
};

export default freeFormText;
