import { isEmpty, merge, omit, pick } from 'lodash';

import {
    IAcceptTermsSettings,
    IAnyOfSettings,
    IBestTimeSettings,
    ICalcBmiSettings,
    IDividerSettings,
    IDobSettings,
    IEmailSettings,
    IEssaySettings,
    IFirstNameSettings,
    IFreeFormTextSettings,
    ILastNameSettings,
    INextButtonSettings,
    INumberSettings,
    IOneOfSettings,
    IOneOfSiteSettings,
    IPageBreakSettings,
    IPhoneSettings,
    IStartPagingSettings,
    IStextSettings,
    ISubmitButtonSettings,
} from '@/types/question-builder-settings';
import acceptTerms from '@/utils/QuestionBuilder/settings/accept-terms';
import calcBMI from '@/utils/QuestionBuilder/settings/calc-bmi';
import checkbox from '@/utils/QuestionBuilder/settings/checkbox';
import dob from '@/utils/QuestionBuilder/settings/dob';
import form from '@/utils/QuestionBuilder/settings/form';
import formItems from '@/utils/QuestionBuilder/settings/form-items';
import freeFormText from '@/utils/QuestionBuilder/settings/free-form-text';
import input from '@/utils/QuestionBuilder/settings/input';
import multiScreen from '@/utils/QuestionBuilder/settings/multi-screen';
import radio from '@/utils/QuestionBuilder/settings/radio';

export const setAcceptTermsSettings = (settings: IAcceptTermsSettings): IAcceptTermsSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge(
              {},
              formItems,
              { form: omit(form.form, 'submitButton') },
              acceptTerms,
              radio
          ) satisfies IAcceptTermsSettings);

export const setFreeFormTextSettings = (settings: IFreeFormTextSettings): IFreeFormTextSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge(
              {},
              formItems,
              { form: omit(form.form, 'submitButton') },
              freeFormText
          ) satisfies IFreeFormTextSettings);

export const setAnyOfSettings = (settings: IAnyOfSettings): IAnyOfSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge({}, formItems, checkbox, { form: omit(form.form, 'submitButton') }) satisfies IAnyOfSettings);

export const setBestTimeSettings = (settings: IBestTimeSettings): IBestTimeSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge(
              {},
              formItems,
              { input: omit(input.input, 'type') },
              { form: omit(form.form, 'submitButton') }
          ) satisfies IBestTimeSettings);

export const setCalcBmiSettings = (settings: ICalcBmiSettings): ICalcBmiSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge(
              {},
              formItems,
              calcBMI,
              { input: omit(input.input, 'type') },
              { form: omit(form.form, 'submitButton') }
          ) as ICalcBmiSettings);

/** TODO: Test the styles to see if we need a new style group for the datepicker input */
export const setDobSettings = (settings: IDobSettings): IDobSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge(
              {},
              formItems,
              dob,
              { input: omit(input.input, 'type') },
              { form: omit(form.form, 'submitButton') }
          ) satisfies IDobSettings);

export const setDividerSettings = (settings: IDividerSettings): IDividerSettings =>
    settings && !isEmpty(settings) ? { ...settings } : (merge({}) satisfies IDividerSettings);

export const setEmailSettings = (settings: IEmailSettings): IEmailSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge(
              {},
              formItems,
              { input: omit(input.input, 'type') },
              { form: omit(form.form, 'submitButton') }
          ) satisfies IEmailSettings);

export const setEssaySettings = (settings: IEssaySettings): IEssaySettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge(
              {},
              formItems,
              { input: omit(input.input, 'type') },
              { form: omit(form.form, 'submitButton') }
          ) satisfies IEssaySettings);

export const setFirstNameSettings = (settings: IFirstNameSettings): IFirstNameSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge(
              {},
              formItems,
              { input: omit(input.input, 'type') },
              { form: omit(form.form, 'submitButton') }
          ) satisfies IFirstNameSettings);

export const setLastNameSettings = (settings: ILastNameSettings): ILastNameSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge(
              {},
              formItems,
              { input: omit(input.input, 'type') },
              { form: omit(form.form, 'submitButton') }
          ) satisfies ILastNameSettings);

export const setNextButtonSettings = (settings: INextButtonSettings): INextButtonSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge({}, { pageBreak: pick(multiScreen.pageBreak, 'nextButton') }) satisfies INextButtonSettings);

export const setNumberSettings = (settings: INumberSettings): INumberSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge(
              {},
              formItems,
              { input: omit(input.input, 'type') },
              { form: omit(form.form, 'submitButton') }
          ) satisfies INumberSettings);

export const setSingleChoiceSettings = (settings: IOneOfSettings): IOneOfSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge({}, formItems, radio, { form: omit(form.form, 'submitButton') }) satisfies IOneOfSettings);

export const setOneOfSiteSettings = (settings: IOneOfSiteSettings): IOneOfSiteSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge({}, formItems, radio, { form: omit(form.form, 'submitButton') }) satisfies IOneOfSiteSettings);

export const setPageBreakSettings = (settings: IPageBreakSettings): IPageBreakSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge({}, { pageBreak: omit(multiScreen.pageBreak, 'nextButton') }) satisfies IPageBreakSettings);

export const setPhoneSettings = (settings: IPhoneSettings): IPhoneSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge(
              {},
              formItems,
              { input: omit(input.input, 'type') },
              { form: omit(form.form, 'submitButton') }
          ) satisfies IPhoneSettings);

export const setStartPagingSettings = (settings: IStartPagingSettings): IStartPagingSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge({}, { pageBreak: omit(multiScreen.pageBreak, 'nextButton') }) satisfies IStartPagingSettings);

export const setStextSettings = (settings: IStextSettings): IStextSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge({}, formItems, { ...input }, { form: omit(form.form, 'submitButton') }) satisfies IStextSettings);

export const setSubmitButtonSettings = (settings: ISubmitButtonSettings): ISubmitButtonSettings =>
    settings && !isEmpty(settings)
        ? { ...settings }
        : (merge({}, { form: pick(form.form, 'submitButton') }) satisfies ISubmitButtonSettings);
