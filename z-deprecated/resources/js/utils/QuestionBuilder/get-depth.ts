import { ModifiedQuestion } from '@/types/question';

export const getDragDepth = (offset: number, indentationWidth: number) => Math.round(offset / indentationWidth);

export const getMaxDepth = ({ previousItem }: { previousItem: ModifiedQuestion }) => {
    if (previousItem) {
        return previousItem.level + 1;
    }

    return 0;
};

export const getMinDepth = ({ nextItem }: { nextItem: ModifiedQuestion }) => {
    if (nextItem) {
        return nextItem.level;
    }

    return 0;
};
