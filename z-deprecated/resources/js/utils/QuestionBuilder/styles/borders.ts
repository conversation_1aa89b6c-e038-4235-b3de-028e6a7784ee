import { KeyValueGeneric } from '@/types/general';
import borderWidths from '@/utils/QuestionBuilder/styles/border-widths';

export const borderHeaders = [
    {
        label: 'Full Border',
        value: 'border-width',
    },
    {
        label: 'Border Top',
        value: 'border-top-width',
    },
    {
        label: 'Border Bottom',
        value: 'border-bottom-width',
    },
    {
        label: 'Border Left',
        value: 'border-left-width',
    },
    {
        label: 'Border Right',
        value: 'border-right-width',
    },
];

const borders = () => {
    const borderStyles = borderHeaders.map((header: KeyValueGeneric<string>) => {
        const options = borderWidths.map((width: number) => ({
            label: `${width}px - ${header.label.replace('Border', '')} `,
            value: `${header.value}: ${width}px`,
        }));

        return {
            label: header.label,
            options,
        };
    });

    borderStyles[0].options = [{ label: 'None', value: 'border: none' }, ...borderStyles[0].options];

    return borderStyles;
};

export default borders;
