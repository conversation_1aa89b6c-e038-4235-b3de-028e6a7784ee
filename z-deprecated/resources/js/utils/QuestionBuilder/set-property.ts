import { UniqueIdentifier } from '@dnd-kit/core';

import { ModifiedQuestion } from '@/types/question';

const setProperty = <T extends keyof ModifiedQuestion>(
    items: ModifiedQuestion[],
    id: UniqueIdentifier,
    property: T,
    setter: (value: ModifiedQuestion[T]) => ModifiedQuestion[T]
) => {
    // TODO: <PERSON><PERSON><PERSON>, convert this so we can remove the eslint-disable
    // eslint-disable-next-line no-restricted-syntax
    for (const item of items) {
        if (item.id === id) {
            item[property] = setter(item[property]);
            // eslint-disable-next-line no-continue
            continue;
        }

        if (item.children.length) {
            item.children = setProperty(item.children, id, property, setter);
        }
    }

    return [...items];
};

export default setProperty;
