import { UniqueIdentifier } from '@dnd-kit/core';

import { ModifiedQuestion } from '@/types/question';

const removeQuestion = (items: ModifiedQuestion[], id: UniqueIdentifier): ModifiedQuestion[] =>
    items.reduce((acc: ModifiedQuestion[], item: ModifiedQuestion) => {
        const newItem = { ...item };

        if (newItem.children.length) {
            newItem.children = removeQuestion(newItem.children, id);
        }

        if (newItem.id !== id) {
            acc.push(newItem);
        }

        return acc;
    }, []);

export default removeQuestion;
