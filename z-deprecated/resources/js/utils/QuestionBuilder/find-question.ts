import { UniqueIdentifier } from '@dnd-kit/core';

import { ModifiedQuestion } from '@/types/question';

export const findQuestion = (items: ModifiedQuestion[], itemId: UniqueIdentifier) =>
    items.find(({ id }) => id === itemId);

export const findQuestionDeep = (items: ModifiedQuestion[], itemId: UniqueIdentifier): ModifiedQuestion | undefined => {
    // TODO: <PERSON><PERSON><PERSON>, change this to use map, reduce, or filter
    // eslint-disable-next-line no-restricted-syntax
    for (const item of items) {
        const { id, children } = item;

        if (id === itemId) {
            return item;
        }

        if (children.length) {
            const child = findQuestionDeep(children, itemId);

            if (child) {
                return child;
            }
        }
    }

    return undefined;
};
