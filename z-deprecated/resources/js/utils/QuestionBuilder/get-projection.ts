import { UniqueIdentifier } from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';

import { ModifiedQuestion } from '@/types/question';
import { findQuestion } from '@/utils/QuestionBuilder/find-question';
import { getDragDepth, getMaxDepth, getMinDepth } from '@/utils/QuestionBuilder/get-depth';

const getProjection = (
    flattenedQuestions: ModifiedQuestion[],
    activeId: UniqueIdentifier,
    overId: UniqueIdentifier,
    dragOffset: number,
    indentationWidth: number
) => {
    const overItemIndex = flattenedQuestions.findIndex(({ id }) => id === overId);
    const activeItemIndex = flattenedQuestions.findIndex(({ id }) => id === activeId);
    let level = 0;

    if (activeItemIndex === -1) {
        const overQuestion = overId ? findQuestion(flattenedQuestions, overId) : null;
        let parentId = null;

        if (overQuestion) {
            parentId = overQuestion.id;
            level = overQuestion.level + 1;
        }

        return { level, maxDepth: 0, minDepth: 0, parentId };
    }

    const activeItem = flattenedQuestions[activeItemIndex];
    const newItems = arrayMove(flattenedQuestions, activeItemIndex, overItemIndex);
    const previousItem = newItems[overItemIndex - 1];
    const nextItem = newItems[overItemIndex + 1];
    const dragDepth = getDragDepth(dragOffset, indentationWidth);
    const projectedDepth = activeItem.level + dragDepth;
    const maxDepth = getMaxDepth({
        previousItem,
    });
    const minDepth = getMinDepth({ nextItem });
    level = projectedDepth;

    if (projectedDepth >= maxDepth) {
        level = maxDepth;
    } else if (projectedDepth < minDepth) {
        level = minDepth;
    }

    return { level, maxDepth, minDepth, parentId: getParentId() };

    function getParentId() {
        if (level === 0 || !previousItem) {
            return null;
        }

        if (level === previousItem.level) {
            return previousItem.parentId;
        }

        if (level > previousItem.level) {
            return previousItem.id;
        }

        const newParent = newItems
            .slice(0, overItemIndex)
            .reverse()
            .find(item => item.depth === level)?.parentId;

        return newParent ?? null;
    }
};

export default getProjection;
