import { UniqueIdentifier } from '@dnd-kit/core';

import buildTree from './build-tree';
import { findQuestionDeep } from './find-question';
import flattenTree from './flatten-tree';

import { ModifiedQuestion } from '@/types/question';

export const countChildren = (items: ModifiedQuestion[], count = 0): number =>
    items.reduce((acc, { children }) => {
        if (children.length) {
            return countChildren(children, acc + 1);
        }

        return acc + 1;
    }, count);

export const getChildCount = (items: ModifiedQuestion[], id: UniqueIdentifier) => {
    const item = findQuestionDeep(buildTree([...items]), id);

    return item ? countChildren(item.children) : 0;
};

export const removeChildrenOf = (items: ModifiedQuestion[], ids: UniqueIdentifier[]) => {
    const excludeParentIds = [...ids];

    return items.map(item => {
        if (item.parentId && excludeParentIds.includes(item.parentId)) {
            if (item.children.length) {
                excludeParentIds.push(item.id);
            }

            return { ...item, hidden: true };
        }

        return item;
    });
};

export const unhideChildren = (flattenedQuestions: ModifiedQuestion[]) =>
    flattenTree(buildTree([...flattenedQuestions])).map(flattenedQuestion => ({
        ...flattenedQuestion,
        hidden: false,
    }));
