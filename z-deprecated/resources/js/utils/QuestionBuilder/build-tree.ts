import { ModifiedQuestion } from '@/types/question';
import { findQuestion } from '@/utils/QuestionBuilder/find-question';

const buildTree = (flattenedQuestions: ModifiedQuestion[]): ModifiedQuestion[] => {
    const root: {
        id: string;
        children: ModifiedQuestion[];
    } = { id: 'root', children: [] };
    const nodes: Record<
        string,
        {
            id: string;
            children: ModifiedQuestion[];
        }
    > = { [root.id]: root };
    const items = flattenedQuestions.map(item => ({ ...item, children: [] }));

    // TODO: Nakisha, Convert this to reduce
    // eslint-disable-next-line no-restricted-syntax
    for (const item of items) {
        // @ts-ignore
        const { id, children } = item;
        const parentId = item.parentId ?? root.id;
        const parent = nodes[parentId] ?? findQuestion(items, parentId);

        nodes[id] = { id, children };
        parent.children.push(item);
    }

    return root.children;
};

export default buildTree;
