import md5 from 'md5';

import { ANY_TODO } from '@/types/general';

const shouldFormItemUpdate = (prevValues: ANY_TODO, curValues: ANY_TODO, index?: number) => {
    const prevValue = index !== undefined ? prevValues.questions[index] : prevValues;
    const curValue = index !== undefined ? curValues.questions[index] : curValues;
    const prevJson = prevValue ? md5(JSON.stringify(prevValue)) : '';
    const curJson = curValue ? md5(JSON.stringify(curValue)) : '';

    return prevJson !== curJson;
};

export default shouldFormItemUpdate;
