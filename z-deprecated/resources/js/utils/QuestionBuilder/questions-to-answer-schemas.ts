import { AnswerSchema } from '@/types/answer-schema';
import { ModifiedQuestion } from '@/types/question';
import { OptionLabel, QuestionOption } from '@/types/question-options';

const questionsToAnswerSchemas = (questions: ModifiedQuestion[], questionParent?: ModifiedQuestion): AnswerSchema[] =>
    questions.map(question => {
        const answerSchema: AnswerSchema = {
            additionalProperties: false,
            properties: {
                id: {
                    const: '',
                },
                question: {
                    enum: [],
                },
            },
            required: ['id', 'question'],
            type: 'object',
        };

        if ('id' in question) {
            answerSchema.properties.id.const = question.id;
        }
        if ('isEncrypted' in question) {
            answerSchema.properties.encrypt = {
                const: question.isEncrypted ? 'true' : 'false',
            };
        }
        if ('questionLabel' in question) {
            answerSchema.properties.question = {
                enum: [question.questionLabel],
            };
        }

        switch (question.questionType.name) {
            case 'anyof':
                answerSchema.required.push('answers', 'options', 'type');
                answerSchema.properties.answers = {
                    type: 'array',
                    maxItems: Math.min(
                        question.settings.max ?? question.optionLabels.length,
                        question.optionLabels.length
                    ),
                    minItems: Math.max(
                        (!questionParent ? Number(question.isRequired) : undefined) ?? 0, // HACK: ignore the min setting, aka the "required" flag, if this is a child question. Solves the problem of required children that are not active due to the parent answer being in a non-matching state for the condition.
                        0
                    ),
                    items: {
                        type: 'integer',
                        maximum: question.optionLabels.length - 1,
                        minimum: 0,
                    },
                };
                if (typeof question.settings.max !== 'undefined') {
                    answerSchema.properties.max = {
                        const: question.settings.max,
                    };
                }
                if (question.isRequired) {
                    answerSchema.properties.min = {
                        const: 1,
                    };
                }
                answerSchema.properties.options = {
                    type: 'array',
                    maxItems: question.optionLabels.length,
                    minItems: question.optionLabels.length,
                    items: question.optionLabels.map((opt: OptionLabel) => ({
                        type: 'string',
                        enum: [opt],
                    })),
                };
                answerSchema.properties.type = {
                    const: question.questionType.name,
                };
                break;

            case 'number':
                // HACK: ignore the !nullable setting, aka the "required" flag, if this is a child question. Solves the problem of required children that are not active due to the parent answer being in a non-matching state for the condition.
                if (!questionParent && question.isRequired) {
                    answerSchema.required.push('answer');
                }
                answerSchema.required.push('type');
                answerSchema.properties.answer = {
                    type: !question.isRequired ? ['number', 'null'] : 'number',
                };
                if (typeof question.settings.max !== 'undefined') {
                    answerSchema.properties.max = {
                        const: question.settings.max,
                    };
                }
                if (typeof question.settings.min !== 'undefined') {
                    answerSchema.properties.min = {
                        const: question.settings.min,
                    };
                }
                if (typeof question.settings.step !== 'undefined') {
                    answerSchema.properties.step = {
                        const: question.settings.step,
                    };
                }
                answerSchema.properties.type = {
                    const: question.questionType.name,
                };
                if (typeof question.settings.unit !== 'undefined') {
                    answerSchema.properties.unit = {
                        enum: Object.values(question.settings.unit),
                    };
                }
                break;

            case 'oneof':
                if (!questionParent && question.isRequired) {
                    answerSchema.required.push('answer');
                }
                answerSchema.required.push('options', 'type');
                answerSchema.properties.answer = {
                    type: !question.isRequired ? ['integer', 'null'] : 'integer',
                    maximum: question.optionLabels.length - 1,
                    minimum: 0,
                };
                answerSchema.properties.options = {
                    type: 'array',
                    maxItems: question.optionLabels.length,
                    minItems: question.optionLabels.length,
                    items: question.optionLabels.map((opt: OptionLabel) => ({
                        type: 'string',
                        enum: [opt],
                    })),
                };
                answerSchema.properties.type = {
                    const: question.questionType.name,
                };
                break;

            case 'oneofsite':
                // HACK: ignore the !nullable setting, aka the "required" flag, if this is a child question. Solves the problem of required children that are not active due to the parent answer being in a non-matching state for the condition.
                if (!questionParent && question.isRequired) {
                    answerSchema.required.push('answer');
                }

                answerSchema.required.push('options', 'type');
                answerSchema.properties.answer = {
                    type: !question.isRequired ? ['integer', 'null'] : 'integer',
                    maximum: (question.options?.length ?? 1) - 1,
                    minimum: 0,
                };
                answerSchema.properties.options = {
                    type: 'array',
                    maxItems: question.options?.length ?? 0,
                    minItems: question.options?.length ?? 0,
                    // TODO: Fix the schema.ts for all option question types
                    items: question.options?.map((opt: QuestionOption) => ({
                        type: 'object',
                        additionalProperties: false,
                        properties: {
                            value: {
                                type: 'string',
                                enum: [opt.value],
                            },
                            label: {
                                type: 'string',
                                enum: [opt.label],
                            },
                        },
                        required: ['site_public', 'text'],
                    })),
                };
                answerSchema.properties.type = {
                    const: question.questionType.name,
                };
                break;

            // case 'essay': -- HW: do be added?

            case 'stext':
                answerSchema.required.push('type');
                answerSchema.properties.answer = {
                    type: 'string',
                    maxLength: question.settings.limit,
                    minLength: 0,
                };
                if (typeof question.settings.limit !== 'undefined') {
                    answerSchema.properties.limit = {
                        const: question.settings.limit,
                    };
                }
                answerSchema.properties.type = {
                    const: question.questionType.name,
                };
                break;
            // no default
        }

        if ('children' in question && question.children?.length) {
            answerSchema.properties.children = {
                items: questionsToAnswerSchemas(question.children, question),
                type: 'array',
                maxItems: question.children.length,
                minItems: question.children.length,
            };
        }

        return answerSchema;
    });

export default questionsToAnswerSchemas;
