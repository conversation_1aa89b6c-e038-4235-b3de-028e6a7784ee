import { UniqueIdentifier } from '@dnd-kit/core';

import { ModifiedQuestion } from '@/types/question';

const flattenTree = (items: ModifiedQuestion[]): ModifiedQuestion[] => flatten(items, null, 0);

export default flattenTree;

const flatten = (items: ModifiedQuestion[], parentId: UniqueIdentifier | null = null, level = 0): ModifiedQuestion[] =>
    items.reduce<ModifiedQuestion[]>((acc, item) => {
        acc.push({
            ...item,
            parentId,
            level,
        });

        return [...acc, ...flatten(item.children, item.id, level + 1)];
    }, []);
