import { ActionCreator } from 'easy-peasy';
import { find, findIndex, hasIn, isArray, isEmpty, merge } from 'lodash';
import { v4 as uuid } from 'uuid';

import { LanguageService } from '@/Components/Languages/services/languageService';
import type { LanguageCode } from '@/Components/Languages/types/languages';
import { store } from '@/store';
import { ModifiedQuestion, QuestionConditionNumberProps } from '@/types/question';
import { QuestionOption } from '@/types/question-options';
import { QuestionType } from '@/types/question-types';
import { SiteDataList } from '@/types/site-data';
import { numberOperators } from '@/utils/number-operators';
import predefinedFieldList from '@/utils/predefined-field-list';
import {
    setAcceptTermsSettings,
    setAnyOfSettings,
    setBestTimeSettings,
    setCalcBmiSettings,
    setDividerSettings,
    setDobSettings,
    setEmailSettings,
    setEssaySettings,
    setFirstNameSettings,
    setFreeFormTextSettings,
    setLastNameSettings,
    setNextButtonSettings,
    setNumberSettings,
    setOneOfSiteSettings,
    setPageBreakSettings,
    setPhoneSettings,
    setSingleChoiceSettings,
    setStartPagingSettings,
    setStextSettings,
    setSubmitButtonSettings,
} from '@/utils/QuestionBuilder/settings/set-default-settings';

const convertQuestionTreeToProps = (
    questionTree: ModifiedQuestion,
    languageCode: LanguageCode,
    parentTree?: ModifiedQuestion
): ModifiedQuestion => {
    const { setQuestionTypes } = store.getActions().questionBuilderModel;
    const { questionTypes, sites } = store.getState().questionBuilderModel;

    let questionProps: ModifiedQuestion = merge({}, questionTree, {
        children: !isEmpty(questionTree.children)
            ? questionTree.children.map((childQuestion: ModifiedQuestion) =>
                  convertQuestionTreeToProps(childQuestion, languageCode, questionTree)
              )
            : questionTree.children,
        conditionOptions: undefined,
        conditionValue: 0,
        isRequired: Number(questionTree.isRequired) ? '1' : '0',
        isEncrypted: Number(questionTree.isEncrypted) ? '1' : '0',
        isConfirmed: Number(questionTree.isConfirmed) ? '1' : '0',
        options: [],
        optionLabels: [],
        parentId: parentTree?.id ?? null,
        questionLabel:
            LanguageService.getContentByLanguage(questionTree.question, languageCode) ?? questionTree.question.en,
        questionType: questionTree.questionType,
        rawOptions: questionTree?.rawOptions,
    });

    questionProps = handlePredefined(questionProps, setQuestionTypes, questionTypes);
    questionProps = setSettingsProperty(questionProps);
    questionProps = handleConditions(questionProps, parentTree, languageCode);
    questionProps = handleAcceptTerms(questionProps, languageCode);
    questionProps = handleMultipleChoice(questionProps, languageCode);
    questionProps = handleSingleChoice(questionProps, languageCode);
    questionProps = handleBestTime(questionProps);
    questionProps = handleCalcBmi(questionProps, languageCode);
    questionProps = handleDbo(questionProps);
    questionProps = handleDivider(questionProps);
    questionProps = handleEmail(questionProps);
    questionProps = handleEssay(questionProps);
    questionProps = handleFirstName(questionProps);
    questionProps = handleLastName(questionProps);
    questionProps = handleNextButton(questionProps);
    questionProps = handleNumber(questionProps);
    questionProps = handleOneOfSite(questionProps, languageCode, sites);
    questionProps = handlePageBreak(questionProps);
    questionProps = handlePhone(questionProps);
    questionProps = handleStartPaging(questionProps);
    questionProps = handleStext(questionProps);
    questionProps = handleSubmitButton(questionProps);

    return questionProps;
};

export default convertQuestionTreeToProps;

export const handleAcceptTerms = (
    questionProps: ModifiedQuestion,
    languageCode: LanguageCode,
    isNewQuestion = false
) => {
    let newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'acceptTerms') {
        newQuestionProps = getOptionLabels(questionProps, languageCode, isNewQuestion);
        newQuestionProps.settings = setAcceptTermsSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handleMultipleChoice = (
    questionProps: ModifiedQuestion,
    languageCode: LanguageCode,
    isNewQuestion = false
) => {
    let newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'anyof') {
        newQuestionProps = getOptionLabels(questionProps, languageCode, isNewQuestion);
        newQuestionProps.settings = setAnyOfSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handleBestTime = (questionProps: ModifiedQuestion) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'bestTime') {
        newQuestionProps.settings = setBestTimeSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handleCalcBmi = (questionProps: ModifiedQuestion, languageCode: LanguageCode) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'calcBMI') {
        newQuestionProps.settings = setCalcBmiSettings(questionProps.settings);

        newQuestionProps.settings.bmi.unit =
            LanguageService.getContentByLanguage(newQuestionProps.settings.bmi.unitOptions ?? {}, languageCode) ?? 'us';
    }

    return newQuestionProps;
};

export const handleDivider = (questionProps: ModifiedQuestion) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'divider') {
        newQuestionProps.settings = setDividerSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handleDbo = (questionProps: ModifiedQuestion) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'dob') {
        newQuestionProps.settings = setDobSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handleEmail = (questionProps: ModifiedQuestion) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'email') {
        newQuestionProps.settings = setEmailSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handleEssay = (questionProps: ModifiedQuestion) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'essay') {
        newQuestionProps.settings = setEssaySettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handleFirstName = (questionProps: ModifiedQuestion) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'firstName') {
        newQuestionProps.settings = setFirstNameSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handleLastName = (questionProps: ModifiedQuestion) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'lastName') {
        newQuestionProps.settings = setLastNameSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handleNextButton = (questionProps: ModifiedQuestion) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'nextButton') {
        newQuestionProps.settings = setNextButtonSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handleNumber = (questionProps: ModifiedQuestion, isNewQuestion = false) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'number') {
        newQuestionProps.settings = setNumberSettings(questionProps.settings);

        if (isNewQuestion) {
            newQuestionProps.conditionOptions = numberOperators.eq.operator;
        }
    }

    return newQuestionProps;
};

export const handleSingleChoice = (
    questionProps: ModifiedQuestion,
    languageCode: LanguageCode,
    isNewQuestion = false
) => {
    let newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'oneof') {
        newQuestionProps = getOptionLabels(questionProps, languageCode, isNewQuestion);
        newQuestionProps.settings = setSingleChoiceSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handleOneOfSite = (
    questionProps: ModifiedQuestion,
    languageCode: LanguageCode,
    sites: SiteDataList[],
    isNewQuestion = false
) => {
    let newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'oneofsite') {
        newQuestionProps = getOptionLabels(questionProps, languageCode, isNewQuestion, sites[0]);
        newQuestionProps.settings = setOneOfSiteSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handlePageBreak = (questionProps: ModifiedQuestion) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'pageBreak') {
        newQuestionProps.settings = setPageBreakSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handlePhone = (questionProps: ModifiedQuestion) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'phone') {
        newQuestionProps.settings = setPhoneSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handleStartPaging = (questionProps: ModifiedQuestion) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'startPaging') {
        newQuestionProps.settings = setStartPagingSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handleStext = (questionProps: ModifiedQuestion) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'stext') {
        newQuestionProps.settings = setStextSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handleSubmitButton = (questionProps: ModifiedQuestion) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'submitButton') {
        newQuestionProps.settings = setSubmitButtonSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handleFreeFormText = (questionProps: ModifiedQuestion) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps.questionType.name === 'freeFormText') {
        newQuestionProps.settings = setFreeFormTextSettings(questionProps.settings);
    }

    return newQuestionProps;
};

export const handlePredefined = (
    questionProps: ModifiedQuestion,
    setQuestionTypes: ActionCreator<QuestionType[]>,
    questionTypes: QuestionType[],
    isNewQuestion = false
) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (questionProps?.questionType?.isPredefined) {
        const questionTypeIndex = findIndex(questionTypes, ['uuid', questionProps?.questionType?.uuid]);
        const newQuestionTypes = [...questionTypes];
        newQuestionProps.isPredefined = true;

        if (isNewQuestion) {
            const predefinedField = find(predefinedFieldList, ['name', questionProps.questionType.name]);
            newQuestionProps.question = predefinedField ? predefinedField.label : questionProps.question;
        }

        if (questionTypeIndex > -1) {
            newQuestionTypes[questionTypeIndex].isDisabled = true;
            setQuestionTypes([...newQuestionTypes]);
        }
    }

    return newQuestionProps;
};

const handleConditions = (
    questionProps: ModifiedQuestion,
    parentTree: ModifiedQuestion | undefined,
    languageCode: LanguageCode
) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (newQuestionProps.conditionSource && parentTree) {
        if (parentTree.questionType.isAllowOptions || parentTree.questionType.isAllowSites) {
            const parentOptions = LanguageService.getContentByLanguage(parentTree.rawOptions ?? {}, languageCode) ?? [];

            newQuestionProps.conditionOptions = parentOptions
                .map((parentOption: QuestionOption) => {
                    if (
                        Array.isArray(newQuestionProps.conditionSource) &&
                        newQuestionProps.conditionSource.includes(parentOption.value)
                    ) {
                        return parentOption.value;
                    }

                    return false;
                })
                .filter((option: string | boolean) => option !== false);
        } else {
            const conditionSource = questionProps.conditionSource as QuestionConditionNumberProps;

            const operator = hasIn(conditionSource, 'operator')
                ? conditionSource.operator
                : numberOperators.eq.operator;

            newQuestionProps.conditionOptions = {
                value: operator,
                label: find(numberOperators, ['operator', operator])?.label ?? 'NOT FOUND',
            };

            newQuestionProps.conditionValue = hasIn(conditionSource, 'value') ? (conditionSource.value as number) : 0;
        }
    }

    return newQuestionProps;
};

const setSettingsProperty = (questionProps: ModifiedQuestion) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (!questionProps.settings || isArray(questionProps.settings)) {
        newQuestionProps.settings = {};
    }

    return newQuestionProps;
};

const getOptionLabels = (
    questionProps: ModifiedQuestion,
    languageCode: LanguageCode,
    isNewQuestion: boolean,
    defaultSite?: SiteDataList
) => {
    const newQuestionProps = merge({}, { ...questionProps });

    if (!isNewQuestion && questionProps.rawOptions && Object.keys(questionProps.rawOptions)?.length > 0) {
        const newOptions = LanguageService.getContentByLanguage(questionProps.rawOptions, languageCode) ?? [];

        newQuestionProps.options = newOptions.filter((option: QuestionOption) => !option.deleted);
        newQuestionProps.optionLabels = newQuestionProps.options?.map((option: QuestionOption) => option.label) ?? [''];
    } else {
        let label = questionProps.questionType.name === 'acceptTerms' ? questionProps.questionType.alias : '';
        let value = uuid();

        if (defaultSite) {
            label = defaultSite.name;
            value = defaultSite.site_public;
        }

        newQuestionProps.options = [{ label, value }];
        newQuestionProps.optionLabels = [label];
    }

    return newQuestionProps;
};
