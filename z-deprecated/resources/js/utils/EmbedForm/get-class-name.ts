import { merge } from 'lodash';

import { KeyValueGeneric } from '@/types/general';
import removeEmptyFromObject from '@/utils/remove-empty-from-object';

const getClassName = (classSetting: KeyValueGeneric, appendClass: string | null = null) => {
    const cloneSetting = classSetting ? merge({}, classSetting) : {};
    const classes = removeEmptyFromObject(cloneSetting);

    return Object.keys(classes).length > 0
        ? Object.keys(classes).map((key: string) => {
              let newClass = classes[key];

              if (classes[key].value ?? false) {
                  newClass = classes[key].value;
              }

              if (appendClass) {
                  return `${appendClass}-${newClass.replace('!', '')}`;
              }

              return newClass;
          })
        : [];
};

export default getClassName;
