import isArray from 'lodash/isArray';
import isObject from 'lodash/isObject';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { ANY_TODO } from '@/types/general';
import { ModifiedQuestion } from '@/types/question';

const evaluateCondition = (
    formQuestion: ModifiedQuestion,
    formQuestionParent: ModifiedQuestion,
    fieldValue: string | number | (string | number)[]
): boolean => {
    const answer = getAnswer(formQuestionParent, fieldValue, formQuestion.condition);
    const condition = formQuestion.condition
        ? formQuestion.condition
              // Support the extra operators.
              .replace(/ eq /g, '===')
              .replace(/ gt /g, '>')
              .replace(/ lt /g, '<')
              .replace(/ ne /g, '!==')
              .replace(/ le /g, '<=')
              .replace(/ ge /g, '>=')
              // Replace the placeholder
              .replace(/ANSWER/g, JSON.stringify(answer))
        : '';

    try {
        // eslint-disable-next-line no-new-func
        return Function(`'use strict';return !!(${condition})`)() as boolean;
    } catch (e: ANY_TODO) {
        return false;
    }
};

export default evaluateCondition;

const getAnswer = (
    formQuestionParent: ModifiedQuestion,
    fieldValue: string | number | (string | number)[],
    condition: string | null
): string | number | (string | number)[] | undefined => {
    let answer;

    if (typeof fieldValue !== 'undefined') {
        answer = fieldValue;

        if (!formQuestionParent.questionType.isAllowNumber && !isArray(fieldValue)) {
            if (
                !isObject(fieldValue) &&
                !isArray(fieldValue) &&
                condition &&
                // .every remains here for backwards compatibility for the question conditions
                (condition.indexOf('.includes') >= 0 ||
                    condition.indexOf('.some') >= 0 ||
                    condition.indexOf('.every') >= 0)
            ) {
                answer = [fieldValue];
            }
        }
    }

    return answer;
};
