import hasIn from 'lodash/hasIn';

import { LanguageService } from '@/Components/Languages/services/languageService';
import type { LanguageCode } from '@/Components/Languages/types/languages';
import { AnswerTree, FormattedAnswer } from '@/types/answer-tree';
import { KeyValueGeneric } from '@/types/general';
import { ModifiedQuestion } from '@/types/question';
import { QuestionOption } from '@/types/question-options';

function convertAnswerPropsToTree(
    formValues: KeyValueGeneric,
    formQuestions: ModifiedQuestion[],
    languageCodes: LanguageCode[]
): FormattedAnswer[] {
    return formQuestions.map((question: ModifiedQuestion): FormattedAnswer => {
        const answer = hasIn(formValues, [question.id]) ? formValues[question.id] : undefined;
        const questionText =
            LanguageService.getContentByLanguage(question.question, languageCodes[0]) ?? "Can't happen.";

        const answerTree: FormattedAnswer = {
            answer,
            children: [],
            is_required: question.isRequired as boolean,
            options: [],
            question_label: questionText as string,
            question_type_uuid: question.questionType.uuid,
            question_uuid: question.id,
        };

        if (
            question.radioCheckboxSelectOptions &&
            (question.questionType.isAllowOptions || question.questionType.isAllowSites)
        ) {
            answerTree.options = question.radioCheckboxSelectOptions.map((option: QuestionOption) => option);
            const formatAnswer = Array.isArray(answer) ? answer : [answer];

            answerTree.answer = question.radioCheckboxSelectOptions.filter((option: QuestionOption) =>
                formatAnswer.includes(option.value)
            );
        }

        if (question.questionType.isAllowNumber) {
            if (typeof answerTree.answer === 'string') {
                answerTree.answer = +answerTree.answer;
            }
        }

        if ('children' in question && question.children.length) {
            answerTree.children = convertAnswerPropsToTree(
                formValues,
                question.children,
                languageCodes
            ) as unknown as AnswerTree[];
        }

        return answerTree;
    });
}

export default convertAnswerPropsToTree;
