import { LanguageService } from '@/Components/Languages/services/languageService';
import type { LanguageCode } from '@/Components/Languages/types/languages';
import { ModifiedQuestion } from '@/types/question';
import { QuestionOption, RadioCheckboxSelectOption } from '@/types/question-options';
import convertQuestionTreeToProps from '@/utils/QuestionBuilder/convert-question-tree-to-props';

const radioCheckboxSelectOptions = (
    questions: ModifiedQuestion[],
    languageCodes: LanguageCode[],
    level: number,
    parentTree?: ModifiedQuestion
): ModifiedQuestion[] =>
    questions.map((question): ModifiedQuestion => {
        const result: ModifiedQuestion =
            // @ts-ignore
            'level' in question ? question : convertQuestionTreeToProps(question, parentTree);

        result.label =
            LanguageService.getContentByLanguage(question.question, languageCodes[0]) ?? '<Language not found>';

        if (
            result.questionType.name === 'anyof' ||
            result.questionType.name === 'oneof' ||
            result.questionType.name === 'oneofsite' ||
            result.questionType.name === 'acceptTerms'
        ) {
            const rawOptions = result.rawOptions as Record<LanguageCode, QuestionOption[]>;
            const options = LanguageService.getContentByLanguage(rawOptions, languageCodes[0]) ?? [];

            result.radioCheckboxSelectOptions = options
                .map((option: QuestionOption, index) => {
                    if (option.deleted) {
                        return false;
                    }

                    return {
                        index,
                        label: option.label,
                        value: option.value,
                    };
                })
                .filter(Boolean) as RadioCheckboxSelectOption[];
        }

        if (question.questionType.name === 'calcBMI' && question.settings?.bmi?.unitOptions) {
            const unitOptions = question.settings.bmi.unitOptions as Record<LanguageCode, string>;
            // TODO: Nakisha - we need an enum or other lookup for BMI unit options
            result.settings.bmi.unit = LanguageService.getContentByLanguage(unitOptions, languageCodes[0]) ?? 'us';
        }

        if ('children' in result && result.children?.length) {
            result.children = radioCheckboxSelectOptions(result.children, languageCodes, level + 1, question);
        }

        return result;
    });

const buildFormQuestions = (questions: ModifiedQuestion[], languageCodes: LanguageCode[]): ModifiedQuestion[] =>
    radioCheckboxSelectOptions(questions, languageCodes, 0);

export default buildFormQuestions;
