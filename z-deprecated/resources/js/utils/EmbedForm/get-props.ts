import { hasIn, merge } from 'lodash';

import { KeyValueGeneric } from '@/types/general';
import removeEmptyFromObject from '@/utils/remove-empty-from-object';

const getProps = (styleSetting: KeyValueGeneric) => {
    const cloneSetting = styleSetting ? merge({}, styleSetting) : {};
    const style = removeEmptyFromObject(cloneSetting);
    const customProps: KeyValueGeneric<string> = {};

    if (Object.keys(style).length > 0) {
        Object.keys(style).forEach((key: string) => {
            if (hasIn(style[key], 'value') && style[key].value) {
                customProps[key] = style[key].value;
            } else {
                customProps[key] = style[key];
            }
        });
    }

    return customProps;
};

export default getProps;
