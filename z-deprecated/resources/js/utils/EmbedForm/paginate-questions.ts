import { PaginatedQuestionReturn, PaginationQuestionProps } from '@/types/embed-form';
import { ModifiedQuestion } from '@/types/question';

const paginateQuestions = ({ formQuestions }: PaginationQuestionProps) => {
    const pageFormQuestions = formQuestions ?? [];

    let chunkIndex = 0;
    let formQuestionIndex = 1;

    const result = pageFormQuestions.reduce(
        (accum: PaginatedQuestionReturn, item: ModifiedQuestion) => {
            const questionType = item.questionType ?? item.questionType;

            if (!accum.containers[chunkIndex]) {
                accum.containers[chunkIndex] = []; // start a new chunk
            }

            if (!accum.ids[formQuestionIndex]) {
                accum.ids[formQuestionIndex] = [];
            }

            if (questionType.name === 'pageBreak') {
                chunkIndex += 1;
                formQuestionIndex += 1;

                if (!accum.containers[chunkIndex]) {
                    accum.containers[chunkIndex] = []; // start a new chunk
                }

                if (!accum.ids[formQuestionIndex]) {
                    accum.ids[formQuestionIndex] = [];
                }

                accum.containers[chunkIndex].push(item);
            } else {
                accum.containers[chunkIndex].push(item);

                if (questionType.name === 'calcBMI') {
                    if (item.bmiUnit === 'us') {
                        accum.ids[formQuestionIndex].push([item.id, 'weight']);
                        accum.ids[formQuestionIndex].push([item.id, 'feet']);
                        accum.ids[formQuestionIndex].push([item.id, 'inches']);
                        accum.ids[formQuestionIndex].push([item.id, 'calculated']);
                    } else {
                        accum.ids[formQuestionIndex].push([item.id, 'kilograms']);
                        accum.ids[formQuestionIndex].push([item.id, 'centimeters']);
                        accum.ids[formQuestionIndex].push([item.id, 'calculated']);
                    }
                } else {
                    accum.ids[formQuestionIndex].push(item.id);
                }
            }

            return accum;
        },
        { containers: [], ids: {} }
    );

    return result;
};

export default paginateQuestions;
