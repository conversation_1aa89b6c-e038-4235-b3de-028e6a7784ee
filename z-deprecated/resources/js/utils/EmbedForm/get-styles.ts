import { hasIn, merge } from 'lodash';

import { KeyValueGeneric } from '@/types/general';
import removeEmptyFromObject from '@/utils/remove-empty-from-object';

const getStyles = (styleSetting: KeyValueGeneric) => {
    const cloneSetting = styleSetting ? merge({}, styleSetting) : {};
    const style = removeEmptyFromObject(cloneSetting);

    return Object.keys(style).length > 0
        ? Object.keys(style).map((key: string) => {
              if (hasIn(style[key], 'value') && style[key].value.includes(':')) {
                  return `${style[key].value};`;
              }

              if (hasIn(style[key], 'value') && style[key].value) {
                  return `${key}: ${style[key].value};`;
              }

              return `${key}: ${style[key]};`;
          })
        : [];
};

export default getStyles;
