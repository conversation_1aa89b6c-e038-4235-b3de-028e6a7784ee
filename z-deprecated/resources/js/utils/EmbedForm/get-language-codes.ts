import { toLower } from 'lodash';

import { LANGUAGES } from '@/Components/Languages/config/languages';
import { ObjLanguages } from '@/types/embed-form';
import { KeyValueGeneric } from '@/types/general';

// TODO: Nakisha - determine if we need to move this to the language service
const getLanguageCodes = (queryStringLang: string, formLanguages: KeyValueGeneric<string>): ObjLanguages => {
    const languageCodes = [];
    let selectedLanguage = null;
    const systemLanguages: KeyValueGeneric<string> = { ...LANGUAGES };

    if (queryStringLang) {
        if (formLanguages[queryStringLang]) {
            languageCodes.push(queryStringLang);
            selectedLanguage = queryStringLang;
        }
    }

    if (navigator.language) {
        const lowerLang = toLower(navigator.language);
        const keyLang = Object.keys(systemLanguages).find(key => systemLanguages[key] === lowerLang);

        if (keyLang && systemLanguages[keyLang]) {
            if (formLanguages[keyLang]) {
                languageCodes.push(keyLang);

                if (!selectedLanguage) {
                    selectedLanguage = keyLang;
                }
            }
        }
    }

    if (navigator.languages.length > 0) {
        navigator.languages.forEach(lang => {
            const lowerLang = toLower(lang);
            const keyLang = Object.keys(systemLanguages).find(key => systemLanguages[key] === lowerLang);

            if (keyLang && systemLanguages[keyLang]) {
                if (formLanguages[keyLang]) {
                    languageCodes.push(keyLang);
                }
            }
        });
    }

    return { selectedLanguage: selectedLanguage ?? LANGUAGES.en, languageCodes };
};

export default getLanguageCodes;
