import { store } from '@/store';
import { ModifiedQuestion } from '@/types/question';

const onFormItemChange = (formQuestion: ModifiedQuestion): void => {
    const { formUpdated } = store.getState().embedFormModel;
    const { setFormUpdated } = store.getActions().embedFormModel;

    if (formQuestion.children.length) {
        setFormUpdated(!formUpdated);
    }
};

export default onFormItemChange;
