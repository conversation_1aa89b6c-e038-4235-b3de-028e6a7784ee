import type { JSX } from 'react';

import { Form } from 'antd';

import { usePage } from '@inertiajs/react';

import '@/Pages/EmbedForm/css/display-form.css';
import '@/Pages/EmbedForm/css/embed-form.css';

import useEmbedForm from '@/hooks/EmbedForm/useEmbedForm';
import useSubmitEmbedForm from '@/hooks/EmbedForm/useSubmitEmbedForm';
import DefaultSubmitButton from '@/Pages/EmbedForm/Partials/FormItems/DefaultSubmitButton';
import FormStyles from '@/Pages/EmbedForm/Partials/FormStyles';
import listQuestions from '@/Pages/EmbedForm/Partials/listQuestions';
import PageBreak from '@/Pages/EmbedForm/Partials/PageBreak';
import { useStoreActions, useStoreState } from '@/store/hooks';
import { EmbedFormPageProps } from '@/types/embed-form';
import { ANY_TODO, PageProps } from '@/types/general';
import Alert from '@/UI-Kit/Alert';
import Skeleton from '@/UI-Kit/Loaders/Skeleton';

const DisplayForm = () => {
    const { formId } = usePage<PageProps<EmbedFormPageProps>>().props;
    const [form] = Form.useForm();
    const { isLoading } = useEmbedForm();
    const formQuestions = useStoreState(state => state.embedFormModel.formQuestions);
    const pageCount = useStoreState(state => state.embedFormModel.pageCount);
    const formContainers = useStoreState(state => state.embedFormModel.formContainers);
    const errorMessage = useStoreState(state => state.embedFormModel.errorMessage);
    const setIsFormSubmitting = useStoreActions(actions => actions.embedFormModel.setIsFormSubmitting);
    const setErrorMessage = useStoreActions(actions => actions.embedFormModel.setErrorMessage);
    const hasSubmitButton = formQuestions.filter(formQuestion => formQuestion.questionType.name === 'submitButton');
    const { submitEmbedForm } = useSubmitEmbedForm();

    useStoreState(state => state.embedFormModel.formUpdated); // Causes the form to re-render when it has children to show conditional questions

    if (isLoading) {
        return <Skeleton rows={20} />;
    }

    return (
        <div id="embed-form" className="relative mb-5 h-full">
            <div className="embed-form-cont h-full">
                <FormStyles />
                {errorMessage && <Alert type="error" showIcon description={errorMessage} className="mb-5" />}
                <Form
                    form={form}
                    name="save-questions"
                    layout="vertical"
                    onFinishFailed={() => {
                        if (errorMessage) {
                            setErrorMessage('');
                        }
                        setIsFormSubmitting(false);
                        setErrorMessage("You've missed one or more questions below. Please double check your answers.");
                    }}
                    onFinish={formData => {
                        setIsFormSubmitting(true);
                        submitEmbedForm({ formValues: formData, formId });
                    }}
                    onValuesChange={() => {
                        if (errorMessage) {
                            setErrorMessage('');
                        }
                    }}
                >
                    {formContainers.length > 1 ? (
                        formContainers?.map(
                            (formContainer: ANY_TODO, containerIndex: number): JSX.Element => (
                                <PageBreak
                                    key={`page-break-${containerIndex.toString()}`}
                                    formContainer={formContainer}
                                    containerIndex={containerIndex}
                                    pageCount={pageCount}
                                >
                                    {listQuestions(formContainer, form)}
                                </PageBreak>
                            )
                        )
                    ) : (
                        <>
                            {formQuestions ? listQuestions(formQuestions, form) : null}
                            {hasSubmitButton.length === 0 && <DefaultSubmitButton />}
                        </>
                    )}
                    {errorMessage && (
                        <Alert
                            type="error"
                            showIcon
                            description="Please scroll to the top of the survey and complete any questions you may have missed."
                            className="mb-5"
                        />
                    )}
                </Form>
            </div>
        </div>
    );
};

export default DisplayForm;
