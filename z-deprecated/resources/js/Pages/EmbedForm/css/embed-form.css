@reference '../../../../css/app.css';

/* stylelint-disable at-rule-empty-line-before,at-rule-name-space-after,at-rule-no-unknown */
/* stylelint-disable no-duplicate-selectors */
/* stylelint-disable */
/* stylelint-disable declaration-bang-space-before,string-no-newline */
#embed-form [class^='ant-']::-ms-clear,
#embed-form [class*='ant-']::-ms-clear,
#embed-form [class^='ant-'] input::-ms-clear,
#embed-form [class*='ant-'] input::-ms-clear,
#embed-form [class^='ant-'] input::-ms-reveal,
#embed-form [class*='ant-'] input::-ms-reveal {
    display: none;
}

#embed-form a {
    text-decoration: none;
    color: #49b7c3;
}

#embed-form .m {
    display: none;
}

#embed-form input:focus {
    outline: none;
}

.embed-form-cont {
    width: auto;
    margin: 0px auto;
    padding: 30px;
}

#embed-form i {
    font-style: italic;
}

#embed-form {
    .ant-radio-wrapper,
    .ant-checkbox-wrapper {
        display: flex;
        margin-bottom: 10px;
    }

    .ant-select-item-option-content {
        white-space: normal !important;
    }
}
