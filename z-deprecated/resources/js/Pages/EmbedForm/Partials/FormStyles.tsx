import { useId } from 'react';

import GlobalStyleSheet, {
    CheckboxGroupStyleSheet,
    InputStyleSheet,
    LabelStyleSheet,
    LinkStyleSheet,
    RadioGroupStyleSheet,
} from '@/Pages/EmbedForm/Partials/GeneralStyles';
import { useStoreState } from '@/store/hooks';

const FormStyles = () => {
    const settings = useStoreState(state => state.embedFormModel.settings);

    return (
        <>
            <GlobalStyleSheet generalSettings={settings} />
            <LabelStyleSheet settings={settings} jsxId={useId()} />
            <LinkStyleSheet settings={settings} jsxId={useId()} />
            <RadioGroupStyleSheet settings={settings} jsxId={useId()} />
            <CheckboxGroupStyleSheet settings={settings} jsxId={useId()} />
            <InputStyleSheet settings={settings} jsxId={useId()} />
        </>
    );
};

export default FormStyles;
