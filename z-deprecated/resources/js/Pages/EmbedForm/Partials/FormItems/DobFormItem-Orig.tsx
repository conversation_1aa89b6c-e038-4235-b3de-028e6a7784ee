import { type JSX, useCallback, useId } from 'react';

import { DatePicker, Form } from 'antd';
import type { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';
import { Rule } from 'rc-field-form/lib/interface';

import { cn } from '@/lib/utils';
import { FormItemStyleSheet, InputStyleSheet, LabelStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import { useStoreState } from '@/store/hooks';
import { ModifiedQuestion } from '@/types/question';

const DobFormItem = ({ formQuestion }: { formQuestion: ModifiedQuestion }): JSX.Element => {
    const rules: Rule[] = [];
    const jsxClass = `jsx-${formQuestion.id}`;
    const generalSettings = useStoreState(state => state.embedFormModel.settings);

    if (formQuestion.isRequired) {
        rules.push({ required: true, message: 'Please enter date of birth' });
    }

    const disabledDate: RangePickerProps['disabledDate'] = useCallback(
        (date: dayjs.Dayjs) =>
            /** Prevent selection of dates in the future */
            date && date > dayjs().endOf('day'),
        []
    );

    return (
        <div className={cn('w-full', jsxClass)}>
            <Form.Item
                name={formQuestion.id}
                label={<span dangerouslySetInnerHTML={{ __html: formQuestion.label as string }} />}
                rules={rules}
            >
                <DatePicker size="large" format="MM/DD/YYYY" disabledDate={disabledDate} />
            </Form.Item>
            <FormItemStyleSheet
                generalSettings={generalSettings}
                itemSettings={formQuestion?.settings}
                classPrefix={jsxClass}
                jsxId={useId()}
            />
            <LabelStyleSheet settings={formQuestion?.settings} classPrefix={jsxClass} jsxId={useId()} />
            <InputStyleSheet settings={formQuestion?.settings} classPrefix={jsxClass} jsxId={useId()} />
        </div>
    );
};

export default DobFormItem;
