import { type JSX, useId } from 'react';

import { Form, Radio } from 'antd';
import { RadioGroupOptionType } from 'antd/lib/radio';
import { Rule } from 'rc-field-form/lib/interface';

import { cn } from '@/lib/utils';
import onFormItemChange from '@/Pages/EmbedForm/callbacks/onFormItemChange';
import { FormItemStyleSheet, LabelStyleSheet, RadioGroupStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import { useStoreState } from '@/store/hooks';
import { ModifiedQuestion } from '@/types/question';

const RadioGroupFormItem = ({
    formQuestion,
    optionType = 'default',
}: {
    formQuestion: ModifiedQuestion;
    optionType?: RadioGroupOptionType;
}): JSX.Element => {
    const rules: Rule[] = [];
    const jsxClass = `jsx-${formQuestion.id}`;
    const generalSettings = useStoreState(state => state.embedFormModel.settings);

    if (formQuestion.isRequired) {
        rules.push({ required: true, message: 'Please select an option above' });
    }

    return (
        <div className={cn('w-full', jsxClass)}>
            <Form.Item
                name={formQuestion.id}
                label={<span dangerouslySetInnerHTML={{ __html: formQuestion.label as string }} />}
                rules={rules}
            >
                <Radio.Group
                    className="block"
                    options={formQuestion.radioCheckboxSelectOptions}
                    optionType={optionType}
                    buttonStyle="solid"
                    onChange={() => onFormItemChange(formQuestion)}
                />
            </Form.Item>
            <FormItemStyleSheet
                generalSettings={generalSettings}
                itemSettings={formQuestion?.settings}
                classPrefix={jsxClass}
                jsxId={useId()}
            />
            <LabelStyleSheet settings={formQuestion?.settings} classPrefix={jsxClass} jsxId={useId()} />
            <RadioGroupStyleSheet settings={formQuestion?.settings} classPrefix={jsxClass} jsxId={useId()} />
        </div>
    );
};

export default RadioGroupFormItem;
