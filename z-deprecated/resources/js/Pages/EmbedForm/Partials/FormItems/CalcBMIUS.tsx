import { useEffect } from 'react';

import { Form, FormInstance, Input } from 'antd';
import { hasIn } from 'lodash';
import { Rule } from 'rc-field-form/lib/interface';

import { cn } from '@/lib/utils';
import { ModifiedQuestion } from '@/types/question';
import getStyles from '@/utils/EmbedForm/get-styles';

type Props = {
    calcRules: Rule[];
    formQuestion: ModifiedQuestion;
    rules: Rule[];
};

const computeBmiUs = (weight: number, feet: number, inches: number): number => {
    /* BMI Formula Reference
     * https://www.cdc.gov/nccdphp/dnpao/growthcharts/training/bmiage/page5_2.html
     */
    if (weight > 0 && feet > 0 && inches >= 0) {
        const height = feet * 12 + Number(inches);

        return (weight / height / height) * 703;
    }

    return 0;
};

const updateBmiUs = (
    formQuestion: ModifiedQuestion,
    getFieldValue: FormInstance['getFieldValue'],
    setFields: FormInstance['setFields']
) => {
    const measures = getFieldValue([formQuestion.id]);

    if (hasIn(measures, 'weight') && hasIn(measures, 'feet') && hasIn(measures, 'inches')) {
        const bmi: number = computeBmiUs(measures.weight, measures.feet, measures.inches);

        setFields([{ name: [formQuestion.id, 'calculated'], value: bmi }]);
    }
};

const CalcBMIUS = ({ calcRules, formQuestion, rules }: Props) => {
    const heightLabelStyle = getStyles(formQuestion.settings?.bmi?.heightLabel?.style);
    const weightLabelStyle = getStyles(formQuestion.settings?.bmi?.weightLabel?.style);

    const form = Form.useFormInstance();

    // If we are editing an existing form submission, we need to calculate our
    // initial BMI value based on the form's initialValues.
    useEffect(() => {
        const initialWeight = form.getFieldValue([formQuestion.id, 'weight']);
        const initialFeet = form.getFieldValue([formQuestion.id, 'feet']);
        const initialInches = form.getFieldValue([formQuestion.id, 'inches']);

        if (initialWeight && initialFeet && initialInches) {
            const initialCalculated = computeBmiUs(initialWeight, initialFeet, initialInches);

            form.setFields([{ name: [formQuestion.id, 'calculated'], value: initialCalculated }]);
        }
    }, [form]);

    return (
        <>
            <Form.Item shouldUpdate={() => false} noStyle>
                {({ getFieldValue, setFields }) => (
                    <div role="row" className={cn('flex flex-wrap gap-x-3 md:flex-nowrap')}>
                        {!formQuestion.settings?.bmi?.heightPosition ||
                            (formQuestion.settings?.bmi?.heightPosition === 'right' && (
                                <div className="w-full md:w-1/3">
                                    <div className="w-full">
                                        {formQuestion.settings?.bmi?.weightLabel?.label ? (
                                            <div className="ant-col ant-form-item-label">
                                                <label>
                                                    <span
                                                        dangerouslySetInnerHTML={{
                                                            __html: formQuestion.settings.bmi.weightLabel
                                                                .label as string,
                                                        }}
                                                    />
                                                    <style jsx={undefined}>{`
                                                    {
                                                        ${weightLabelStyle.join('\n')}
                                                    }
                                                `}</style>
                                                </label>
                                            </div>
                                        ) : null}
                                        <Form.Item name={[formQuestion.id, 'weight']} rules={rules}>
                                            <Input
                                                addonAfter="lbs"
                                                type="number"
                                                min={0}
                                                max={1500}
                                                onChange={() => updateBmiUs(formQuestion, getFieldValue, setFields)}
                                            />
                                        </Form.Item>
                                    </div>
                                </div>
                            ))}
                        <div className="w-full md:w-2/3">
                            {formQuestion.settings?.bmi?.heightLabel?.label ? (
                                <div className="ant-col ant-form-item-label">
                                    <label>
                                        <span
                                            dangerouslySetInnerHTML={{
                                                __html: formQuestion.settings.bmi.heightLabel.label as string,
                                            }}
                                        />
                                        <style jsx={undefined}>{`
                                            {
                                                ${heightLabelStyle.join('\n')}
                                            }
                                        `}</style>
                                    </label>
                                </div>
                            ) : null}
                            <div className="flex gap-x-3">
                                <div className="w-1/2">
                                    <Form.Item name={[formQuestion.id, 'feet']} rules={rules}>
                                        <Input
                                            addonAfter="ft"
                                            type="number"
                                            min={0}
                                            max={10}
                                            onChange={() => updateBmiUs(formQuestion, getFieldValue, setFields)}
                                        />
                                    </Form.Item>
                                </div>
                                <div className="w-1/2">
                                    <Form.Item name={[formQuestion.id, 'inches']} rules={rules}>
                                        <Input
                                            addonAfter="in"
                                            type="number"
                                            min={0}
                                            max={12}
                                            onChange={() => updateBmiUs(formQuestion, getFieldValue, setFields)}
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                        </div>
                        {formQuestion.settings?.bmi?.heightPosition === 'left' && (
                            <div className="w-full md:w-1/3">
                                <div className="w-full">
                                    {formQuestion.settings?.bmi?.weightLabel?.label ? (
                                        <div className="ant-col ant-form-item-label">
                                            <label>
                                                <span
                                                    dangerouslySetInnerHTML={{
                                                        __html: formQuestion.settings.bmi.weightLabel.label as string,
                                                    }}
                                                />
                                                <style jsx={undefined}>{`
                                                    {
                                                        ${weightLabelStyle.join('\n')}
                                                    }
                                                `}</style>
                                            </label>
                                        </div>
                                    ) : null}
                                    <Form.Item name={[formQuestion.id, 'weight']} rules={rules}>
                                        <Input
                                            addonAfter="lbs"
                                            type="number"
                                            min={0}
                                            max={1500}
                                            onChange={() => updateBmiUs(formQuestion, getFieldValue, setFields)}
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                        )}
                    </div>
                )}
            </Form.Item>

            <Form.Item name={[formQuestion.id, 'calculated']} rules={calcRules} hidden>
                <Input disabled />
            </Form.Item>
        </>
    );
};

export default CalcBMIUS;
