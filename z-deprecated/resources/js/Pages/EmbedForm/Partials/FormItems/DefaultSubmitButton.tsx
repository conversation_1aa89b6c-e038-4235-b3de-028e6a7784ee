import { type JSX, useId } from 'react';

import { Form } from 'antd';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { cn } from '@/lib/utils';
import { ButtonStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import { useStoreState } from '@/store/hooks';
import Button from '@/UI-Kit/Button';
import { ButtonProps } from '@/UI-Kit/Button/types';
import { iconList } from '@/utils/dynamic-fontawesome-icons';
import getProps from '@/utils/EmbedForm/get-props';
import { buttonShapes } from '@/utils/QuestionBuilder/styles/button-props';

const DefaultSubmitButton = (): JSX.Element => {
    const isFormSubmitting = useStoreState(state => state.embedFormModel.isFormSubmitting);
    const jsxClass = 'default-submit-button';
    const jsxId = useId();
    const settings = useStoreState(state => state.embedFormModel.settings);
    const globalSettings = settings?.form?.submitButton;

    const generalProps = getProps(globalSettings?.buttonProps);

    const [{ value: defaultButtonShape }] = buttonShapes.filter(buttonShape => buttonShape.value === 'rounded');

    const defaultProps: ButtonProps = {
        type: 'submit',
        label: globalSettings?.btnLabel?.text ?? 'Submit',
        color: 'blue',
        // @ts-ignore
        buttonShape: defaultButtonShape,
        icon: globalSettings?.icon?.value ? <FontAwesomeIcon icon={iconList[globalSettings.icon.value]} /> : undefined,
        isLoading: isFormSubmitting,
        disabled: isFormSubmitting,
        ...generalProps,
    };

    return (
        <div className={cn('w-full', jsxClass)}>
            <Form.Item help={isFormSubmitting && <span>Submitting your request...</span>}>
                <Button {...defaultProps} />
                <ButtonStyleSheet
                    jsxId={jsxId}
                    classPrefix={jsxClass}
                    globalSettings={globalSettings}
                    itemSettings={{}}
                />
            </Form.Item>
        </div>
    );
};

export default DefaultSubmitButton;
