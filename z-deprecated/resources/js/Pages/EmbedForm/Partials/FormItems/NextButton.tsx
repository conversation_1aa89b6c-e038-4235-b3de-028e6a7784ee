import { type JSX, useId } from 'react';

import { Form } from 'antd';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import usePagination from '@/hooks/EmbedForm/usePagination';
import { cn } from '@/lib/utils';
import { ButtonStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import { useStoreState } from '@/store/hooks';
import { ModifiedQuestion } from '@/types/question';
import Button from '@/UI-Kit/Button';
import { ButtonProps } from '@/UI-Kit/Button/types';
import { iconList } from '@/utils/dynamic-fontawesome-icons';
import getProps from '@/utils/EmbedForm/get-props';
import { buttonShapes } from '@/utils/QuestionBuilder/styles/button-props';

const NextButton = ({ formQuestion }: { formQuestion: ModifiedQuestion }): JSX.Element => {
    const form = Form.useFormInstance();
    const { goToNextPage } = usePagination(form);
    const jsxClass = `jsx-${formQuestion.id}`;
    const settings = useStoreState(state => state.embedFormModel.settings);
    const globalSettings = settings?.pageBreak?.nextButton;
    const itemSettings = formQuestion?.settings?.pageBreak?.nextButton;

    const globalProps = getProps(globalSettings?.buttonProps);
    const itemProps = getProps(itemSettings?.buttonProps);

    const [{ value: defaultButtonShape }] = buttonShapes.filter(buttonShape => buttonShape.value === 'rounded');

    const defaultProps: ButtonProps = {
        type: 'button',
        label: itemSettings?.btnLabel?.text ?? globalSettings?.btnLabel?.text ?? 'Next',
        color: 'blue',
        // @ts-ignore
        buttonShape: defaultButtonShape,
        icon: globalSettings?.icon?.value ? <FontAwesomeIcon icon={iconList[globalSettings.icon.value]} /> : undefined,
        onClick: goToNextPage,
        ...globalProps,
        ...itemProps,
    };

    return (
        <div className={cn('w-full', jsxClass)}>
            <Button {...defaultProps} />
            <ButtonStyleSheet
                jsxId={useId()}
                classPrefix={jsxClass}
                globalSettings={globalSettings}
                itemSettings={itemSettings}
            />
        </div>
    );
};

export default NextButton;
