import { type JSX, useId } from 'react';

import { Form, Input } from 'antd';
import { Rule } from 'rc-field-form/lib/interface';

import { cn } from '@/lib/utils';
import onFormItemChange from '@/Pages/EmbedForm/callbacks/onFormItemChange';
import { FormItemStyleSheet, InputStyleSheet, LabelStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import { useStoreState } from '@/store/hooks';
import { ModifiedQuestion } from '@/types/question';

const InputNumberFormItem = ({ formQuestion }: { formQuestion: ModifiedQuestion }): JSX.Element => {
    const rules: Rule[] = [];
    const jsxClass = `jsx-${formQuestion.id}`;
    const generalSettings = useStoreState(state => state.embedFormModel.settings);

    if (formQuestion.isRequired) {
        rules.push({ required: true, message: 'Please enter a number above' });
    }

    return (
        <div className={cn('w-full', jsxClass)}>
            <Form.Item
                name={formQuestion.id}
                label={<span dangerouslySetInnerHTML={{ __html: formQuestion.label as string }} />}
                rules={rules}
            >
                <Input type="number" style={{ width: '130px' }} onChange={() => onFormItemChange(formQuestion)} />
            </Form.Item>
            <FormItemStyleSheet
                generalSettings={generalSettings}
                itemSettings={formQuestion?.settings}
                classPrefix={jsxClass}
                jsxId={useId()}
            />
            <LabelStyleSheet settings={formQuestion?.settings} classPrefix={jsxClass} jsxId={useId()} />
            <InputStyleSheet settings={formQuestion?.settings} classPrefix={jsxClass} jsxId={useId()} />
        </div>
    );
};

export default InputNumberFormItem;
