import { type JSX, useId } from 'react';
import PhoneInput, { isPossiblePhoneNumber } from 'react-phone-number-input';

import { Form, Input } from 'antd';
import { CountryCode, E164Number } from 'libphonenumber-js';
import { Rule } from 'rc-field-form/lib/interface';

import { usePage } from '@inertiajs/react';

import '@/Pages/EmbedForm/css/phone-input.css';

import { cn } from '@/lib/utils';
import { FormItemStyleSheet, InputStyleSheet, LabelStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import { useStoreState } from '@/store/hooks';
import { ANY_TODO, PageProps } from '@/types/general';
import { ModifiedQuestion } from '@/types/question';

const InputFormItem = ({ formQuestion }: { formQuestion: ModifiedQuestion }): JSX.Element => {
    const rules: Rule[] = [];
    const jsxClass = `jsx-${formQuestion.id}`;
    const generalSettings = useStoreState(state => state.embedFormModel.settings);
    const inputType = formQuestion.settings?.input?.type?.value;
    const inputDefaultValue = formQuestion.settings?.input?.defaultValue;
    const { currentTenant } = usePage<PageProps>().props;
    const tenantCountryCode = currentTenant.server === 'ukSouth' ? 'GB' : 'US';
    const defaultCountryCode = formQuestion.settings?.defaultCountryCode?.value ?? tenantCountryCode;

    if (formQuestion.isRequired) {
        rules.push({ required: true, message: 'Please complete the question above' });
    }

    if (formQuestion.questionType.name === 'email' || inputType === 'email') {
        rules.push({ type: 'email', message: 'The input is not a valid email' });
    }

    if (inputType === 'url') {
        rules.push({ type: 'url', message: 'The input is not a valid url' });
    }

    if (formQuestion.questionType.name === 'phone') {
        const checkPhone = (_: ANY_TODO, value: string): Promise<void | string> => {
            if (!value) {
                return Promise.resolve();
            }

            if (isPossiblePhoneNumber(value)) {
                return Promise.resolve();
            }
            return Promise.reject(new Error('Please enter a valid phone number'));
        };

        rules.push({ validator: checkPhone });
    }

    return (
        <div className={cn('w-full', jsxClass)}>
            <Form.Item
                name={formQuestion.id}
                label={<span dangerouslySetInnerHTML={{ __html: formQuestion.label as string }} />}
                rules={rules}
                hidden={inputType === 'hidden'}
                {...(inputDefaultValue ? { initialValue: inputDefaultValue } : null)}
            >
                {formQuestion.questionType.name === 'phone' || inputType === 'phone' ? (
                    <PhoneInputCnt defaultCountryCode={defaultCountryCode} />
                ) : (
                    <>
                        {inputType === 'hidden' ? (
                            <Input type="hidden" />
                        ) : (
                            <Input
                                type={
                                    formQuestion.questionType.name === 'email' || inputType === 'email'
                                        ? 'email'
                                        : 'text'
                                }
                            />
                        )}
                    </>
                )}
            </Form.Item>

            {formQuestion.isConfirmed && (
                <Form.Item
                    name={`${formQuestion.id}--confirm`}
                    label={<span dangerouslySetInnerHTML={{ __html: formQuestion.settings.confirm.label as string }} />}
                    dependencies={[formQuestion.id]}
                    rules={[
                        {
                            required: true,
                            message: `Please confirm this field`,
                        },
                        ({ getFieldValue }) => ({
                            validator(_, value) {
                                if (!value || getFieldValue(formQuestion.id) === value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error(`This value does not match the value above it.`));
                            },
                        }),
                    ]}
                >
                    {formQuestion.questionType.name === 'phone' || inputType === 'phone' ? (
                        <PhoneInputCnt defaultCountryCode={defaultCountryCode} />
                    ) : (
                        <Input
                            type={
                                formQuestion.questionType.name === 'email' || inputType === 'email' ? 'email' : 'text'
                            }
                        />
                    )}
                </Form.Item>
            )}
            <FormItemStyleSheet
                generalSettings={generalSettings}
                itemSettings={formQuestion?.settings}
                classPrefix={jsxClass}
                jsxId={useId()}
            />
            <LabelStyleSheet settings={formQuestion?.settings} classPrefix={jsxClass} jsxId={useId()} />
            <InputStyleSheet settings={formQuestion?.settings} classPrefix={jsxClass} jsxId={useId()} />
        </div>
    );
};

export default InputFormItem;

const PhoneInputCnt = ({
    value: valueProp,
    onChange,
    defaultCountryCode,
}: {
    value?: string;
    onChange?: (value: E164Number | null) => void;
    defaultCountryCode: CountryCode;
}): JSX.Element => (
    <PhoneInput
        defaultCountry={defaultCountryCode}
        value={valueProp}
        onChange={onChange as (value?: string | undefined) => void}
    />
);
