import { useId } from 'react';

import { Form, Input } from 'antd';
import dayjs from 'dayjs';
import { Rule } from 'rc-field-form/lib/interface';

import { cn } from '@/lib/utils';
import { InputStyleSheet, LabelStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import { ModifiedQuestion } from '@/types/question';

const DobFormItem = ({ formQuestion }: { formQuestion: ModifiedQuestion }) => {
    const dateInputSettings: string = formQuestion.settings?.dateInputs ?? [];
    const minAgeSettings: number | null = formQuestion.settings?.minAge;
    const minYear = dayjs().subtract(110, 'year').year();
    const maxYear = minAgeSettings ? dayjs().subtract(minAgeSettings, 'year').year() : dayjs().year();

    const showMonth = ['showFullDate', 'showMonthDayDate', 'showMonthYearDate'].includes(dateInputSettings);
    const showDay = ['showFullDate', 'showMonthDayDate'].includes(dateInputSettings);
    const showYear = ['showFullDate', 'showYearOnly', 'showMonthYearDate'].includes(dateInputSettings);
    const showFirstSlash = showMonth && showDay;
    const showSecondSlash = showMonth && showYear;

    const onlyContainsDigits = (value: string) => /^\d+$/.test(value);

    const monthRules: Rule[] = [
        () => ({
            validator(_, value) {
                if (onlyContainsDigits(value) && value > 0 && value < 13) {
                    return Promise.resolve();
                }

                return Promise.reject(new Error('Please enter a whole number between 1 and 12 for the month'));
            },
        }),
    ];

    const dayRules: Rule[] = [
        () => ({
            validator(_, value) {
                if (onlyContainsDigits(value) && value > 0 && value < 32) {
                    return Promise.resolve();
                }
                return Promise.reject(new Error('Please enter a whole number between 1 and 31 for the day'));
            },
        }),
    ];

    const yearRules: Rule[] = [
        () => ({
            validator(_, value) {
                if (onlyContainsDigits(value) && value >= minYear && value <= maxYear) {
                    return Promise.resolve();
                }
                return Promise.reject(new Error('Please enter a valid value for the year'));
            },
        }),
    ];

    const jsxClass = `jsx-${formQuestion.id}`;

    if (formQuestion.isRequired) {
        if (showMonth) {
            monthRules.push({ required: true, message: 'Please enter a month' });
        }

        if (showDay) {
            dayRules.push({ required: true, message: 'Please enter a day' });
        }

        if (showYear) {
            yearRules.push({ required: true, message: 'Please enter a year' });
        }
    }

    return (
        <div className={cn('w-full', jsxClass)}>
            <Form.Item
                shouldUpdate={() => false}
                label={<span dangerouslySetInnerHTML={{ __html: formQuestion.label as string }} />}
                className="mb-5"
                required={formQuestion.isRequired as boolean}
            >
                <div className="flex items-center gap-x-3">
                    {showMonth && (
                        <div>
                            <span className="ml-1 block">Month</span>
                            <Form.Item name={[formQuestion.id, 'month']} rules={monthRules} noStyle>
                                <Input
                                    type="number"
                                    min={1}
                                    max={12}
                                    size="small"
                                    placeholder="MM"
                                    className="ui-input ui-input-compact w-20"
                                />
                            </Form.Item>
                        </div>
                    )}

                    {showFirstSlash && <span className="inline-block pt-5">/</span>}

                    {showDay && (
                        <div>
                            <span className="ml-1 block">Day</span>
                            <Form.Item name={[formQuestion.id, 'day']} rules={dayRules} noStyle>
                                <Input
                                    type="number"
                                    size="small"
                                    placeholder="DD"
                                    className="ui-input ui-input-compact w-20"
                                />
                            </Form.Item>
                        </div>
                    )}

                    {showSecondSlash && <span className="inline-block pt-5">/</span>}

                    {showYear && (
                        <div>
                            <span className="ml-1 block">Year</span>
                            <Form.Item name={[formQuestion.id, 'year']} rules={yearRules} noStyle>
                                <Input
                                    type="number"
                                    size="small"
                                    placeholder="YYYY"
                                    className="ui-input ui-input-compact w-24"
                                />
                            </Form.Item>
                        </div>
                    )}
                </div>
                <LabelStyleSheet settings={formQuestion?.settings} classPrefix={jsxClass} jsxId={useId()} />
                <InputStyleSheet settings={formQuestion?.settings} classPrefix={jsxClass} jsxId={useId()} />
            </Form.Item>
        </div>
    );
};

export default DobFormItem;
