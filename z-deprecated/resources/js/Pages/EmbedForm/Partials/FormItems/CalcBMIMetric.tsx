import { useEffect } from 'react';

import { Form, FormInstance, Input } from 'antd';
import { hasIn } from 'lodash';
import { Rule } from 'rc-field-form/lib/interface';

import { cn } from '@/lib/utils';
import { ModifiedQuestion } from '@/types/question';
import getStyles from '@/utils/EmbedForm/get-styles';

type Props = {
    calcRules: Rule[];
    formQuestion: ModifiedQuestion;
    rules: Rule[];
};

const computeBmiMetric = (kilograms: number, centimeters: number): number => {
    /* BMI Formula Reference
     * https://www.cdc.gov/nccdphp/dnpao/growthcharts/training/bmiage/page5_2.html
     */
    if (kilograms > 0 && centimeters > 0) {
        return (kilograms / centimeters / centimeters) * 10000;
    }

    return 0;
};

const updateBmiMetric = (
    formQuestion: ModifiedQuestion,
    getFieldValue: FormInstance['getFieldValue'],
    setFields: FormInstance['setFields']
) => {
    const measures = getFieldValue([formQuestion.id]);

    if (hasIn(measures, 'kilograms') && hasIn(measures, 'centimeters')) {
        const bmi = computeBmiMetric(measures.kilograms, measures.centimeters);

        setFields([{ name: [formQuestion.id, 'calculated'], value: bmi }]);
    }
};

const CalcBMIMetric = ({ calcRules, formQuestion, rules }: Props) => {
    const heightLabelStyle = getStyles(formQuestion.settings?.bmi?.heightLabel?.style);
    const weightLabelStyle = getStyles(formQuestion.settings?.bmi?.weightLabel?.style);

    const form = Form.useFormInstance();

    // If we are editing an existing form submission, we need to calculate our
    // initial BMI value based on the form's initialValues.
    useEffect(() => {
        const initialKilograms = form.getFieldValue([formQuestion.id, 'kilograms']);
        const initialCentimeters = form.getFieldValue([formQuestion.id, 'centimeters']);

        if (initialKilograms && initialCentimeters) {
            const initialCalculated = computeBmiMetric(initialKilograms, initialCentimeters);

            form.setFields([{ name: [formQuestion.id, 'calculated'], value: initialCalculated }]);
        }
    }, [form]);

    return (
        <>
            <Form.Item shouldUpdate={() => false} noStyle>
                {({ getFieldValue, setFields }) => (
                    <div role="row" className={cn('flex gap-x-3')}>
                        {!formQuestion.settings?.bmi?.heightPosition ||
                            (formQuestion.settings?.bmi?.heightPosition === 'right' && (
                                <div className="w-1/3">
                                    {formQuestion.settings?.bmi?.weightLabel?.label ? (
                                        <div className="ant-col ant-form-item-label">
                                            <label>
                                                <span
                                                    dangerouslySetInnerHTML={{
                                                        __html: formQuestion.settings.bmi.weightLabel.label as string,
                                                    }}
                                                />
                                                <style jsx={undefined}>{`
                                                {
                                                    ${weightLabelStyle.join('\n')}
                                                }
                                            `}</style>
                                            </label>
                                        </div>
                                    ) : null}
                                    <Form.Item name={[formQuestion.id, 'kilograms']} rules={rules}>
                                        <Input
                                            addonAfter="KGS"
                                            type="number"
                                            min={0}
                                            max={700}
                                            onChange={() => updateBmiMetric(formQuestion, getFieldValue, setFields)}
                                        />
                                    </Form.Item>
                                </div>
                            ))}
                        <div className="w-1/3">
                            {formQuestion.settings?.bmi?.heightLabel?.label ? (
                                <div className="ant-col ant-form-item-label">
                                    <label>
                                        <span
                                            dangerouslySetInnerHTML={{
                                                __html: formQuestion.settings.bmi.heightLabel.label as string,
                                            }}
                                        />
                                        <style jsx={undefined}>{`
                                            {
                                                ${heightLabelStyle.join('\n')}
                                            }
                                        `}</style>
                                    </label>
                                </div>
                            ) : null}
                            <Form.Item name={[formQuestion.id, 'centimeters']} rules={rules}>
                                <Input
                                    addonAfter="CM"
                                    type="number"
                                    min={0}
                                    max={275}
                                    onChange={() => updateBmiMetric(formQuestion, getFieldValue, setFields)}
                                />
                            </Form.Item>
                        </div>
                        {formQuestion.settings?.bmi?.heightPosition === 'left' && (
                            <div className="w-1/3">
                                {formQuestion.settings?.bmi?.weightLabel?.label ? (
                                    <div className="ant-col ant-form-item-label">
                                        <label>
                                            <span
                                                dangerouslySetInnerHTML={{
                                                    __html: formQuestion.settings.bmi.weightLabel.label as string,
                                                }}
                                            />
                                            <style jsx={undefined}>{`
                                                {
                                                    ${weightLabelStyle.join('\n')}
                                                }
                                            `}</style>
                                        </label>
                                    </div>
                                ) : null}
                                <Form.Item name={[formQuestion.id, 'kilograms']} rules={rules}>
                                    <Input
                                        addonAfter="KGS"
                                        type="number"
                                        min={0}
                                        max={700}
                                        onChange={() => updateBmiMetric(formQuestion, getFieldValue, setFields)}
                                    />
                                </Form.Item>
                            </div>
                        )}
                    </div>
                )}
            </Form.Item>

            <Form.Item name={[formQuestion.id, 'calculated']} rules={calcRules} hidden>
                <Input disabled />
            </Form.Item>
        </>
    );
};

export default CalcBMIMetric;
