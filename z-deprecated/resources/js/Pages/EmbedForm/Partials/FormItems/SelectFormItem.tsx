import { type JSX, useId } from 'react';

import { Form, Select, SelectProps } from 'antd';
import { Rule } from 'rc-field-form/lib/interface';

import { cn } from '@/lib/utils';
import onFormItemChange from '@/Pages/EmbedForm/callbacks/onFormItemChange';
import { FormItemStyleSheet, LabelStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import { useStoreState } from '@/store/hooks';
import { ANY_TODO } from '@/types/general';
import { ModifiedQuestion } from '@/types/question';

const SelectFormItem = ({ formQuestion }: { formQuestion: ModifiedQuestion }): JSX.Element => {
    const rules: Rule[] = [];
    const jsxClass = `jsx-${formQuestion.id}`;
    const generalSettings = useStoreState(state => state.embedFormModel.settings);

    const selectProps: SelectProps<ANY_TODO> = {
        showSearch: true,
        style: { width: '100%' },
        placeholder: 'Select Value(s)',
        onChange: () => onFormItemChange(formQuestion),
        options: formQuestion.radioCheckboxSelectOptions,
        optionFilterProp: 'label',
        filterOption: (input: ANY_TODO, option: ANY_TODO) =>
            option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0,
        getPopupContainer: triggerNode => triggerNode.parentElement,
        virtual: false,
    };

    const validationMessage =
        formQuestion.questionType.name === 'anyof'
            ? 'Please select one or more options above'
            : 'Please select an option above';

    if (formQuestion.questionType.name === 'anyof') {
        selectProps.mode = 'multiple';
    }

    if (formQuestion.isRequired) {
        rules.push({ required: true, message: validationMessage });
    }

    return (
        <div className={cn('w-full', jsxClass)}>
            <Form.Item
                name={formQuestion.id}
                label={<span dangerouslySetInnerHTML={{ __html: formQuestion.label as string }} />}
                rules={rules}
            >
                <Select {...selectProps} />
            </Form.Item>
            <FormItemStyleSheet
                generalSettings={generalSettings}
                itemSettings={formQuestion?.settings}
                classPrefix={jsxClass}
                jsxId={useId()}
            />
            <LabelStyleSheet settings={formQuestion?.settings} classPrefix={jsxClass} jsxId={useId()} />
        </div>
    );
};

export default SelectFormItem;
