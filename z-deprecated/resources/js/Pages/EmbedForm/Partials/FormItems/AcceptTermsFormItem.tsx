import { type JSX, useId } from 'react';

import { Form, Radio } from 'antd';
import edJsHTML from 'editorjs-html';
import { Rule } from 'rc-field-form/lib/interface';

import { cn } from '@/lib/utils';
import onFormItemChange from '@/Pages/EmbedForm/callbacks/onFormItemChange';
import { FormItemStyleSheet, LabelStyleSheet, RadioGroupStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import { useStoreState } from '@/store/hooks';
import { ModifiedQuestion } from '@/types/question';
import getStyles from '@/utils/EmbedForm/get-styles';

const AcceptTermsFormItem = ({ formQuestion }: { formQuestion: ModifiedQuestion }): JSX.Element => {
    const rules: Rule[] = [];
    const jsxClass = `jsx-${formQuestion.id}`;
    const userAgreementContainerStyles = getStyles(formQuestion?.settings?.userAgreement?.container?.style);
    const userAgreementContentStyles = getStyles(formQuestion?.settings?.userAgreement?.content?.style);
    const generalSettings = useStoreState(state => state.embedFormModel.settings);

    if (formQuestion.isRequired) {
        rules.push({ required: true, message: 'Accept the terms of conditions to continue' });
    }

    return (
        <div className={cn('w-full', jsxClass)}>
            <Form.Item
                shouldUpdate={() => false}
                style={!formQuestion?.settings?.userAgreement?.terms ? { marginBottom: 0 } : {}}
                required={formQuestion.isRequired as boolean}
                label={
                    formQuestion?.settings?.userAgreement ? (
                        <span dangerouslySetInnerHTML={{ __html: formQuestion.label as string }} />
                    ) : null
                }
            >
                <div
                    className={cn('flex', {
                        'flex-col':
                            !formQuestion?.settings?.userAgreement?.radioPosition ||
                            formQuestion?.settings?.userAgreement?.radioPosition === 'top',
                        'flex-col-reverse': formQuestion?.settings?.userAgreement?.radioPosition === 'bottom',
                    })}
                >
                    <Form.Item name={formQuestion.id} rules={rules} style={{ marginBottom: 0 }}>
                        <Radio.Group
                            className="block"
                            options={formQuestion.radioCheckboxSelectOptions}
                            onChange={() => onFormItemChange(formQuestion)}
                        />
                    </Form.Item>
                    {formQuestion?.settings?.userAgreement?.terms ? (
                        <>
                            <div
                                className="user-agreement-container overflow-y-scroll"
                                dangerouslySetInnerHTML={{
                                    __html: edJsHTML().parse(formQuestion?.settings?.userAgreement?.terms)[0],
                                }}
                            />
                            <style jsx={undefined}>{`
                                :global(.user-agreement-container) {
                                    ${userAgreementContainerStyles.join('\n')}
                                }

                                :global(.user-agreement-container p) {
                                    ${userAgreementContentStyles.join('\n')}
                                }
                            `}</style>
                        </>
                    ) : null}
                </div>
            </Form.Item>

            {!formQuestion?.settings?.userAgreement?.terms ? (
                <p className={formQuestion.isRequired ? 'ant-form-custom-required' : ''}>
                    <span dangerouslySetInnerHTML={{ __html: formQuestion.label as string }} /> Read the legal terms and
                    agreements by clicking{' '}
                    <a href="https://1nhealth.com/privacy-policy" target="_blank" rel="noreferrer">
                        Privacy Policy
                    </a>
                    .
                </p>
            ) : null}
            <FormItemStyleSheet
                generalSettings={generalSettings}
                itemSettings={formQuestion?.settings}
                classPrefix={jsxClass}
                jsxId={useId()}
            />
            <LabelStyleSheet settings={formQuestion?.settings} classPrefix={jsxClass} jsxId={useId()} />
            <RadioGroupStyleSheet settings={formQuestion?.settings} classPrefix={jsxClass} jsxId={useId()} />
        </div>
    );
};

export default AcceptTermsFormItem;
