import type { JSX } from 'react';

import edJsHTML from 'editorjs-html';

import { cn } from '@/lib/utils';
import { ModifiedQuestion } from '@/types/question';
import getStyles from '@/utils/EmbedForm/get-styles';

const FreeFormTextFormItem = ({ formQuestion }: { formQuestion: ModifiedQuestion }): JSX.Element => {
    const jsxClass = `jsx-${formQuestion.id}`;
    const freeFormTextContainerStyles = getStyles(formQuestion?.settings?.freeFormText?.container?.style);
    const freeFormTextContentStyles = getStyles(formQuestion?.settings?.freeFormText?.content?.style);
    const freeFormTextContent = formQuestion?.settings?.freeFormText?.text;

    return (
        <>
            <div
                className={cn('free-form-text-container mb-10', jsxClass)}
                dangerouslySetInnerHTML={{
                    __html: edJsHTML().parse(freeFormTextContent).join('\n <br>'),
                }}
            />

            <style jsx={undefined}>{`
                .free-form-text-container.${jsxClass} {
                    ${freeFormTextContainerStyles.join('\n')}
                }

                .free-form-text-container.${jsxClass} p {
                    ${freeFormTextContentStyles.join('\n')}
                }

                .free-form-text-container.${jsxClass} ol,
                .free-form-text-container.${jsxClass} ul {
                    padding-left: 40px;
                }

                .free-form-text-container.${jsxClass} ol {
                    list-style-type: decimal;
                }

                .free-form-text-container.${jsxClass} ul {
                    list-style-type: disc;
                }

                .free-form-text-container.${jsxClass} ol li,
                .free-form-text-container.${jsxClass} ul li {
                    margin-bottom: 5px;
                }
            `}</style>
        </>
    );
};

export default FreeFormTextFormItem;
