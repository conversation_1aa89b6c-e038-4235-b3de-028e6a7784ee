import { type JSX, useId } from 'react';

import { Form } from 'antd';
import { Rule } from 'rc-field-form/lib/interface';

import { cn } from '@/lib/utils';
import CalcBMIMetric from '@/Pages/EmbedForm/Partials/FormItems/CalcBMIMetric';
import CalcBMIUS from '@/Pages/EmbedForm/Partials/FormItems/CalcBMIUS';
import { FormItemStyleSheet, InputStyleSheet, LabelStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import { useStoreState } from '@/store/hooks';
import { ModifiedQuestion } from '@/types/question';

const CalcBMIFormItem = ({ formQuestion }: { formQuestion: ModifiedQuestion }): JSX.Element => {
    const min = 14;
    const max = 65;
    const rules: Rule[] = [];
    const calcRules: Rule[] = [{ type: 'number', min, max, message: 'Please enter a valid weight & height' }];
    const jsxClass = `jsx-${formQuestion.id}`;
    const generalSettings = useStoreState(state => state.embedFormModel.settings);

    if (formQuestion.isRequired) {
        rules.push({ required: true, message: 'Enter a valid value' });
        calcRules.push({ required: true, message: 'Enter a valid value' });
    }

    return (
        <div className={cn('w-full', jsxClass)}>
            <Form.Item shouldUpdate noStyle>
                {({ getFieldError }) => {
                    const calculatedError = getFieldError([formQuestion.id, 'calculated']);

                    return (
                        <Form.Item
                            shouldUpdate={() => false}
                            label={<span dangerouslySetInnerHTML={{ __html: formQuestion.label as string }} />}
                            style={{ marginBottom: 0 }}
                            required={formQuestion.isRequired as boolean}
                            validateStatus={calculatedError.length > 0 ? 'error' : undefined}
                            help={calculatedError.join('\n')}
                        >
                            {formQuestion?.settings.bmi.unit === 'us' ? (
                                <CalcBMIUS formQuestion={formQuestion} calcRules={calcRules} rules={rules} />
                            ) : (
                                <CalcBMIMetric formQuestion={formQuestion} calcRules={calcRules} rules={rules} />
                            )}
                        </Form.Item>
                    );
                }}
            </Form.Item>
            <FormItemStyleSheet
                generalSettings={generalSettings}
                itemSettings={formQuestion?.settings}
                classPrefix={jsxClass}
                jsxId={useId()}
            />
            <LabelStyleSheet settings={formQuestion?.settings} classPrefix={jsxClass} jsxId={useId()} />
            <InputStyleSheet settings={formQuestion?.settings} classPrefix={jsxClass} jsxId={useId()} />
        </div>
    );
};

export default CalcBMIFormItem;
