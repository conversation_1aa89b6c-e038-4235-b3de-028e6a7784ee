import { find, merge } from 'lodash';

import { cn } from '@/lib/utils';
import { useStoreState } from '@/store/hooks';
import { ANY_TODO, KeyValueGeneric } from '@/types/general';
import getStyles from '@/utils/EmbedForm/get-styles';
import { getRandomInt } from '@/utils/helpers';

const PageBreak = ({ children, formContainer, containerIndex, pageCount }: ANY_TODO) => {
    const pagingQuestion = find(
        formContainer,
        fc => fc.questionType.name === 'pageBreak' || fc.questionType.name === 'startPaging'
    );
    const currentPage = useStoreState(state => state.embedFormModel.currentPage);
    const settings = useStoreState(state => state.embedFormModel.settings);
    const generalContainerStyle = getStyles(settings?.pageBreak?.container?.style);
    const itemContainerStyle = getStyles(pagingQuestion?.settings?.pageBreak?.container?.style);
    const containerStyle = merge([], [...generalContainerStyle, ...itemContainerStyle]);
    const jsxClass = `jsx-page-break-${containerIndex}`;

    return (
        <div
            className={cn('page-break-container flex flex-wrap transition-all', jsxClass, {
                hidden: containerIndex + 1 !== currentPage,
            })}
        >
            {pagingQuestion?.settings?.pageBreak?.header || settings?.pageBreak?.header ? (
                <HeaderContainer
                    pagingSettings={pagingQuestion?.settings}
                    containerIndex={containerIndex}
                    pageCount={pageCount}
                />
            ) : null}
            {children}
            <style jsx={undefined}>{`
                .${jsxClass}.page-break-container {
                    ${containerStyle.join('\n')};
                }
            `}</style>
        </div>
    );
};

export default PageBreak;

const HeaderContainer = ({ pagingSettings, containerIndex, pageCount }: KeyValueGeneric) => {
    const settings = useStoreState(state => state.embedFormModel.settings);
    const generalHeaderStyle = getStyles(settings?.pageBreak?.headerContainer?.style);
    const itemHeaderStyle = getStyles(pagingSettings?.pageBreak?.headerContainer?.style);
    const headerStyle = merge([], [...generalHeaderStyle, ...itemHeaderStyle]);
    const jsxClass = `jsx-head-container-${containerIndex}`;

    return (
        <div className={cn('flex items-start justify-between', jsxClass)}>
            <HeaderTitle pagingSettings={pagingSettings} />
            <PageNumber pagingSettings={pagingSettings} containerIndex={containerIndex} pageCount={pageCount} />
            <style jsx={undefined}>{`
                .${jsxClass} {
                    ${headerStyle.join('\n')}
                }
            `}</style>
        </div>
    );
};

const HeaderTitle = ({ pagingSettings }: { pagingSettings: KeyValueGeneric }) => {
    const settings = useStoreState(state => state.embedFormModel.settings);
    const generalTitleStyle = getStyles(settings?.pageBreak?.title?.style);
    const itemTitleStyle = getStyles(pagingSettings?.pageBreak?.title?.style);
    const titleStyle = merge([], [...generalTitleStyle, ...itemTitleStyle]);
    const jsxClass = `jsx-head-title-${getRandomInt()}`;

    return (
        <h2 className={jsxClass}>
            {pagingSettings?.pageBreak?.header ?? settings?.pageBreak?.header}
            <style jsx={undefined}>{`
                h2.${jsxClass} {
                    ${titleStyle.join('\n')}
                }
            `}</style>
        </h2>
    );
};

const PageNumber = ({
    pagingSettings,
    containerIndex,
    pageCount,
}: {
    pagingSettings: KeyValueGeneric;
    containerIndex: number;
    pageCount: number;
}) => {
    const settings = useStoreState(state => state.embedFormModel.settings);
    const generalPagingStyle = getStyles(settings?.pageBreak?.paging?.style);
    const itemPagingStyle = getStyles(pagingSettings?.pageBreak?.paging?.style);
    const pagingStyle = merge([], [...generalPagingStyle, ...itemPagingStyle]);
    const jsxClass = `jsx-page-number-${getRandomInt()}`;

    return (
        <>
            {pagingSettings?.pageBreak?.showPaging?.value === '1' ||
            (settings?.pageBreak?.showPaging?.value === '1' && pagingSettings?.pageBreak?.showPaging?.value !== '0') ? (
                <div className={cn('justify-end')}>
                    <div className={jsxClass}>
                        {pagingSettings?.pageBreak?.paging?.title ?? settings?.pageBreak?.paging?.title ?? null}{' '}
                        {containerIndex + 1}/{pageCount}
                        <style jsx={undefined}>{`
                            .${jsxClass} {
                                ${pagingStyle.join('\n')}
                            }
                        `}</style>
                    </div>
                </div>
            ) : null}
        </>
    );
};
