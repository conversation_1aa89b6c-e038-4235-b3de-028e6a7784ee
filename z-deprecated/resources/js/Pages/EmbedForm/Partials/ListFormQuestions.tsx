import type { JSX } from 'react';

import AcceptTermsFormItem from '@/Pages/EmbedForm/Partials/FormItems/AcceptTermsFormItem';
import CalcBMIFormItem from '@/Pages/EmbedForm/Partials/FormItems/CalcBMIFormItem';
import CheckboxGroupFormItem from '@/Pages/EmbedForm/Partials/FormItems/CheckboxGroupFormItem';
import DobFormItem from '@/Pages/EmbedForm/Partials/FormItems/DobFormItem';
import FreeFormTextFormItem from '@/Pages/EmbedForm/Partials/FormItems/FreeFormTextFormItem';
import InputFormItem from '@/Pages/EmbedForm/Partials/FormItems/InputFormItem';
import InputNumberFormItem from '@/Pages/EmbedForm/Partials/FormItems/InputNumberFormItem';
import NextButton from '@/Pages/EmbedForm/Partials/FormItems/NextButton';
import RadioGroupFormItem from '@/Pages/EmbedForm/Partials/FormItems/RadioGroupFormItem';
import SelectFormItem from '@/Pages/EmbedForm/Partials/FormItems/SelectFormItem';
import SubmitButton from '@/Pages/EmbedForm/Partials/FormItems/SubmitButton';
import TextAreaFormItem from '@/Pages/EmbedForm/Partials/FormItems/TextAreaFormItem';
import { ModifiedQuestion } from '@/types/question';

const ListFormQuestions = ({ formQuestion }: { formQuestion: ModifiedQuestion }): JSX.Element | null => {
    if (['startPaging', 'endPaging', 'pageBreak'].includes(formQuestion.questionType.name)) {
        return null;
    }

    if (formQuestion.questionType.name === 'stext' || formQuestion.questionType.isPredefined) {
        return <InputFormItem formQuestion={formQuestion} />;
    }

    if (formQuestion.questionType.name === 'essay') {
        return <TextAreaFormItem formQuestion={formQuestion} />;
    }

    if (formQuestion.questionType.name === 'calcBMI') {
        return <CalcBMIFormItem formQuestion={formQuestion} />;
    }

    if (formQuestion.questionType.name === 'acceptTerms') {
        return <AcceptTermsFormItem formQuestion={formQuestion} />;
    }

    if (formQuestion.questionType.name === 'dob') {
        return <DobFormItem formQuestion={formQuestion} />;
    }

    if (formQuestion.questionType.name === 'freeFormText') {
        return <FreeFormTextFormItem formQuestion={formQuestion} />;
    }

    if (formQuestion.questionType.isAllowNumber) {
        return <InputNumberFormItem formQuestion={formQuestion} />;
    }

    if (formQuestion.questionType.isAllowOptions || formQuestion.questionType.isAllowSites) {
        const settings =
            formQuestion.questionType.name === 'anyof' ? formQuestion.settings.checkbox : formQuestion.settings.radio;

        switch (settings?.optionsType?.value) {
            case 'dropdown':
                return <SelectFormItem formQuestion={formQuestion} />;

            default:
                if (formQuestion.questionType.name === 'oneof' || formQuestion.questionType.isAllowSites) {
                    return (
                        <RadioGroupFormItem
                            formQuestion={formQuestion}
                            optionType={settings?.optionsType?.value === 'radioGroup' ? 'button' : 'default'}
                        />
                    );
                }

                return <CheckboxGroupFormItem formQuestion={formQuestion} />;
        }
    }

    if (formQuestion.questionType.name === 'nextButton') {
        return <NextButton formQuestion={formQuestion} />;
    }

    if (formQuestion.questionType.name === 'submitButton') {
        return <SubmitButton formQuestion={formQuestion} />;
    }

    return <div>{formQuestion.id}</div>;
};

export default ListFormQuestions;
