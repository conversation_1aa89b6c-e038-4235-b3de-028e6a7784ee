import React, { type JSX } from 'react';

import { FormInstance } from 'antd';

import ListFormQuestions from '@/Pages/EmbedForm/Partials/ListFormQuestions';
import { ModifiedQuestion } from '@/types/question';
import evaluateCondition from '@/utils/EmbedForm/evaluate-condition';

const listQuestions = (
    formQuestions: ModifiedQuestion[],
    form: FormInstance,
    formQuestionParent?: ModifiedQuestion
): JSX.Element => (
    <>
        {formQuestions?.map((formQuestion, questionIndex: number): JSX.Element => {
            let showQuestion = !formQuestion.condition;

            // show child question?
            if ('condition' in formQuestion && typeof formQuestion.condition !== 'undefined' && formQuestionParent) {
                showQuestion = evaluateCondition(
                    formQuestion,
                    formQuestionParent,
                    form.getFieldValue([formQuestionParent.id])
                );
            }

            return (
                // eslint-disable-next-line react/no-array-index-key
                <React.Fragment key={questionIndex}>
                    {showQuestion ? <ListFormQuestions formQuestion={formQuestion} /> : ''}
                    {showQuestion && 'children' in formQuestion && formQuestion.children?.length
                        ? listQuestions(formQuestion.children, form, formQuestion)
                        : ''}
                </React.Fragment>
            );
        }) ?? ''}
    </>
);

export default listQuestions;
