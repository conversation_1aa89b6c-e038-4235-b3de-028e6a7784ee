/* eslint-disable @typescript-eslint/no-unused-vars */ // This is temporary, until we figure if we need jsxId
import { merge } from 'lodash';

import { KeyValueGeneric } from '@/types/general';
import getStyles from '@/utils/EmbedForm/get-styles';

const GlobalStyleSheet = ({ generalSettings }: { generalSettings: KeyValueGeneric | undefined }) => {
    const generalStyle = getStyles(generalSettings?.general?.style);

    return (
        <style jsx={undefined}>{`
            html,
            body,
            div,
            span,
            applet,
            object,
            iframe,
            h1,
            h2,
            h3,
            h4,
            h5,
            h6,
            p,
            blockquote,
            pre,
            a,
            abbr,
            acronym,
            address,
            big,
            cite,
            code,
            del,
            dfn,
            em,
            img,
            ins,
            kbd,
            q,
            s,
            samp,
            small,
            strike,
            strong,
            sub,
            sup,
            tt,
            var,
            b,
            u,
            i,
            center,
            dl,
            dt,
            dd,
            ol,
            ul,
            li,
            fieldset,
            form,
            label,
            legend,
            table,
            caption,
            tbody,
            tfoot,
            thead,
            tr,
            th,
            td,
            article,
            aside,
            canvas,
            details,
            embed,
            figure,
            figcaption,
            footer,
            header,
            hgroup,
            menu,
            nav,
            output,
            ruby,
            section,
            summary,
            time,
            mark,
            audio,
            video {
                ${generalStyle.join('\n')}
            }

            .embed-form-cont #save-questions.ant-form {
                ${generalStyle.join('\n')}
            }

            /* Prevent mobile Safari from zooming in on form fields */
            select,
            textarea,
            input[type='text'],
            input[type='password'],
            input[type='datetime'],
            input[type='datetime-local'],
            input[type='date'],
            input[type='month'],
            input[type='time'],
            input[type='week'],
            input[type='number'],
            input[type='email'],
            input[type='url'],
            input[type='search'],
            input[type='tel'],
            input[type='color'] {
                font-size: 16px;
            }
        `}</style>
    );
};

export default GlobalStyleSheet;

export const FormGlobalStyleSheet = ({ generalSettings }: { generalSettings: KeyValueGeneric | undefined }) => {
    const generalStyle = getStyles(generalSettings?.general?.style);

    return (
        <style jsx={undefined} global={undefined}>{`
            .embed-form-cont #save-questions.ant-form div[class*='jsx-'],
            .embed-form-cont #save-questions.ant-form div[class*='jsx-'] .ant-form-item .ant-form-item-explain > div,
            .embed-form-cont #save-questions.ant-form div[class*='jsx-'] .ant-form-item .ant-form-item-row,
            .embed-form-cont
                #save-questions.ant-form
                div[class*='jsx-']
                .ant-form-item
                .ant-form-item-row
                .ant-form-item-label,
            .embed-form-cont
                #save-questions.ant-form
                div[class*='jsx-']
                ant-form-item
                .ant-form-item-row
                .ant-form-item-control,
            .embed-form-cont #save-questions.ant-form div[class*='jsx-'] .ant-form-item .ant-form-item-label > label,
            .embed-form-cont
                #save-questions.ant-form
                div[class*='jsx-'].ant-form-item
                .ant-form-item-label
                > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before,
            .embed-form-cont #save-questions.ant-form div[class*='jsx-'] .ant-form-item .ant-form-item-label > label p,
            .embed-form-cont #save-questions.ant-form div[class*='jsx-'] .ant-radio-group,
            .embed-form-cont
                #save-questions.ant-form
                div[class*='jsx-']
                .ant-radio-group
                input.ant-radio-input
                + .ant-radio-inner,
            .embed-form-cont
                #save-questions.ant-form
                div[class*='jsx-']
                .ant-radio-group
                .ant-radio-checked
                input.ant-radio-input
                + .ant-radio-inner,
            .embed-form-cont
                #save-questions.ant-form
                div[class*='jsx-']
                .ant-radio-group
                .ant-radio-wrapper
                span.ant-radio
                + *,
            .embed-form-cont #save-questions.ant-form div[class*='jsx-'] .ant-checkbox-group,
            .embed-form-cont #save-questions.ant-form div[class*='jsx-'] .ant-checkbox-group .ant-checkbox-wrapper,
            .embed-form-cont #save-questions.ant-form div[class*='jsx-'] .ant-checkbox-group .ant-checkbox-wrapper,
            .embed-form-cont
                #save-questions.ant-form
                div[class*='jsx-']
                .ant-checkbox-group
                .ant-checkbox-wrapper
                span.ant-checkbox
                + *,
            .embed-form-cont
                #save-questions.ant-form
                div[class*='jsx-']
                .ant-checkbox-group
                input.ant-checkbox-input
                + .ant-checkbox-inner,
            .embed-form-cont
                #save-questions.ant-form
                div[class*='jsx-']
                .ant-checkbox-group
                .ant-checkbox-checked
                input.ant-checkbox-input
                + .ant-checkbox-inner,
            .embed-form-cont #save-questions.ant-form div[class*='jsx-'] .ant-form-item .ant-form-item-control-input,
            .embed-form-cont
                #save-questions.ant-form
                div[class*='jsx-']
                .ant-form-item
                .ant-form-item-control-input
                .ant-input,
            .embed-form-cont
                #save-questions.ant-form
                div[class*='jsx-']
                .ant-form-item
                .ant-form-item-control-input
                .PhoneInputInput,
            .embed-form-cont
                #save-questions.ant-form
                div[class*='jsx-']
                .ant-input-group
                .ant-input-group-addon:last-child {
                ${generalStyle.join('\n')}
            }
        `}</style>
    );
};

export const ButtonStyleSheet = ({
    itemSettings,
    globalSettings,
    jsxId,
    classPrefix = '',
}: {
    itemSettings: KeyValueGeneric | undefined;
    globalSettings: KeyValueGeneric | undefined;
    jsxId: string | number;
    classPrefix?: string;
}) => {
    const className = classPrefix ? `.${classPrefix}` : '';
    const globalButtonStyle = getStyles(globalSettings?.button?.style);
    const itemButtonStyle = getStyles(itemSettings?.button?.style);
    const buttonStyle = merge([], [...globalButtonStyle, ...itemButtonStyle]);

    const globalLabelStyle = getStyles(globalSettings?.btnLabel?.style);
    const itemLabelStyle = getStyles(itemSettings?.btnLabel?.style);
    const buttonLabelStyle = merge([], [...globalLabelStyle, ...itemLabelStyle]);

    return (
        <style jsx={undefined}>{`
            #embed-form .embed-form-cont #save-questions.ant-form ${className} .btn {
                ${buttonStyle.join('\n')}
            }

            #embed-form .embed-form-cont #save-questions.ant-form ${className} .btn .btn-label,
            #embed-form .embed-form-cont #save-questions.ant-form ${className} .btn .btn-icon-left,
            #embed-form .embed-form-cont #save-questions.ant-form ${className} .btn .btn-icon-right {
                ${buttonLabelStyle.join('\n')}
            }

            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-form-item
                .ant-form-item-explain
                > div,
            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-form-item
                .ant-form-item-explain
                > div
                span {
                font-size: 0.85rem;
            }
        `}</style>
    );
};

export const FormItemStyleSheet = ({
    itemSettings,
    generalSettings,
    jsxId,
    classPrefix = '',
}: {
    itemSettings: KeyValueGeneric | undefined;
    generalSettings: KeyValueGeneric | undefined;
    jsxId: string | number;
    classPrefix?: string;
}) => {
    const className = classPrefix ? `.${classPrefix}` : '';
    const generalFormItemContainerStyle = getStyles(generalSettings?.formItem?.container?.style);
    const itemFormItemContainerStyle = getStyles(itemSettings?.formItem?.container?.style);
    const formItemContainerStyle = merge([], [...generalFormItemContainerStyle, ...itemFormItemContainerStyle]);

    const generalFormItemRowStyle = getStyles(generalSettings?.formItem?.row?.style);
    const itemFormItemRowStyle = getStyles(itemSettings?.formItem?.row?.style);
    const formItemRowStyle = merge([], [...generalFormItemRowStyle, ...itemFormItemRowStyle]);

    const generalFormItemLabelStyle = getStyles(generalSettings?.formItem?.label?.style);
    const itemFormItemLabelStyle = getStyles(itemSettings?.formItem?.label?.style);
    const formItemLabelStyle = merge([], [...generalFormItemLabelStyle, ...itemFormItemLabelStyle]);

    const generalFormItemControlStyle = getStyles(generalSettings?.formItem?.control?.style);
    const itemFormItemControlStyle = getStyles(itemSettings?.formItem?.control?.style);
    const formItemControlStyle = merge([], [...generalFormItemControlStyle, ...itemFormItemControlStyle]);

    return (
        <style jsx={undefined}>{`
            @media (min-width: 768px) {
                #embed-form .embed-form-cont #save-questions.ant-form ${className} {
                    ${formItemContainerStyle.join('\n')}
                }
            }

            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-form-item
                .ant-form-item-explain
                > div {
                font-size: 0.85rem;
                margin-bottom: 10px;
            }

            #embed-form .embed-form-cont #save-questions.ant-form ${className} .ant-form-item .ant-form-item-row {
                ${formItemRowStyle.join('\n')}
            }

            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-form-item
                .ant-form-item-row
                .ant-form-item-label {
                ${formItemLabelStyle.join('\n')}
            }

            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-form-item
                .ant-form-item-row
                .ant-form-item-control {
                ${formItemControlStyle.join('\n')}
            }
        `}</style>
    );
};

export const LabelStyleSheet = ({
    settings,
    jsxId,
    classPrefix = '',
}: {
    settings: KeyValueGeneric | undefined;
    jsxId: string | number;
    classPrefix?: string;
}) => {
    const labelStyles = settings?.label?.styles;
    const style = [];

    if (labelStyles) {
        if (labelStyles.fontSize) {
            style.push(`font-size: ${labelStyles.fontSize};`);
        }
        if (labelStyles.lineHeight) {
            style.push(`line-height: ${labelStyles.lineHeight};`);
        }
        if (labelStyles.fontWeight) {
            style.push(`font-weight: ${labelStyles.fontWeight};`);
        }
        if (labelStyles.textTransform) {
            style.push(`text-transform: ${labelStyles.textTransform};`);
        }
        if (labelStyles.textColor) {
            style.push(`color: ${labelStyles.textColor};`);
        }
    }

    return (
        <style jsx={undefined}>{`
            .${classPrefix} .ant-form-item .ant-form-item-label > label {
                ${style.join('\n')}
            }
        `}</style>
    );
};

export const LinkStyleSheet = ({
    settings,
    jsxId,
    classPrefix = '',
}: {
    settings: KeyValueGeneric | undefined;
    jsxId: string | number;
    classPrefix?: string;
}) => {
    const linkStyle = getStyles(settings?.form?.link?.style);
    const className = classPrefix ? `.${classPrefix}` : '';

    return (
        <style jsx={undefined}>{`
            #embed-form .embed-form-cont #save-questions.ant-form ${className} a {
                ${linkStyle.join('\n')}
            }
        `}</style>
    );
};

export const RadioGroupStyleSheet = ({
    settings,
    jsxId,
    classPrefix = '',
}: {
    settings: KeyValueGeneric | undefined;
    jsxId: string | number;
    classPrefix?: string;
}) => {
    const className = classPrefix ? `.${classPrefix}` : '';
    const radioGroupStyle = getStyles(settings?.radio?.container?.style);
    const radioButtonStyle = getStyles(settings?.radio?.button?.style);
    const radioCheckedStyle = getStyles(settings?.radio?.checked?.style);
    const radioLabelStyle = getStyles(settings?.radio?.label?.style);

    return (
        <style jsx={undefined}>{`
            @media (min-width: 768px) {
                #embed-form .embed-form-cont #save-questions.ant-form ${className} .ant-radio-group {
                    ${radioGroupStyle.join('\n')}
                }
            }

            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-radio-group
                input.ant-radio-input
                + .ant-radio-inner {
                ${radioButtonStyle.join('\n')}
            }

            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-radio-group
                .ant-radio-checked
                input.ant-radio-input
                + .ant-radio-inner {
                ${radioCheckedStyle.join('\n')}
            }

            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-radio-group
                .ant-radio-wrapper
                span.ant-radio
                + * {
                ${radioLabelStyle.join('\n')}
            }
        `}</style>
    );
};

export const CheckboxGroupStyleSheet = ({
    settings,
    jsxId,
    classPrefix = '',
}: {
    settings: KeyValueGeneric | undefined;
    jsxId: string | number;
    classPrefix?: string;
}) => {
    const className = classPrefix ? `.${classPrefix}` : '';
    const checkboxGroupStyle = getStyles(settings?.checkbox?.container?.style);
    const checkboxInputStyle = getStyles(settings?.checkbox?.input?.style);
    const checkboxCheckedStyle = getStyles(settings?.checkbox?.checked?.style);
    const checkboxLabelStyle = getStyles(settings?.checkbox?.label?.style);
    const checkboxOuterLabelStyle = [];

    if (
        checkboxGroupStyle.filter(element => element.includes('column')).length > 0 ||
        checkboxGroupStyle.length === 0
    ) {
        checkboxOuterLabelStyle.push('margin-inline-start: 0');
    } else if (checkboxGroupStyle.filter(element => element.includes('wrap')).length > 0) {
        checkboxOuterLabelStyle.push('margin-inline-start: 8px');
    }

    return (
        <style jsx={undefined}>{`
            @media (min-width: 768px) {
                #embed-form .embed-form-cont #save-questions.ant-form ${className} .ant-checkbox-group {
                    ${checkboxGroupStyle.join('\n')}
                }
            }

            @media (max-width: 767px) {
                #embed-form
                    .embed-form-cont
                    #save-questions.ant-form
                    ${className}
                    .ant-checkbox-group
                    .ant-checkbox-wrapper {
                    margin-inline-start: 0;
                }
            }

            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-checkbox-group
                .ant-checkbox-wrapper {
                ${checkboxOuterLabelStyle.join('\n')}
            }

            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-checkbox-group
                .ant-checkbox-wrapper
                span.ant-checkbox
                + * {
                ${checkboxLabelStyle.join('\n')}
            }

            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-checkbox-group
                input.ant-checkbox-input
                + .ant-checkbox-inner {
                ${checkboxInputStyle.join('\n')}
            }

            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-checkbox-group
                .ant-checkbox-checked
                input.ant-checkbox-input
                + .ant-checkbox-inner {
                ${checkboxCheckedStyle.join('\n')}
            }
        `}</style>
    );
};

export const InputStyleSheet = ({
    settings,
    jsxId,
    classPrefix = '',
}: {
    settings: KeyValueGeneric | undefined;
    jsxId: string | number;
    classPrefix?: string;
}) => {
    const className = classPrefix ? `.${classPrefix}` : '';
    const inputContainerStyle = getStyles(settings?.input?.container?.style);
    const inputControlStyle = getStyles(settings?.input?.control?.style);
    const inputAddonStyle = getStyles(settings?.input?.addon?.style);

    return (
        <style jsx={undefined}>{`
            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-form-item
                .ant-form-item-control-input {
                ${inputContainerStyle.join('\n')}
            }

            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-form-item
                .ant-form-item-control-input
                .ant-input,
            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-form-item
                .ant-form-item-control-input
                .PhoneInputInput {
                ${inputControlStyle.join('\n')}
            }

            #embed-form
                .embed-form-cont
                #save-questions.ant-form
                ${className}
                .ant-input-group
                .ant-input-group-addon:last-child {
                ${inputAddonStyle.join('\n')}
            }
        `}</style>
    );
};
