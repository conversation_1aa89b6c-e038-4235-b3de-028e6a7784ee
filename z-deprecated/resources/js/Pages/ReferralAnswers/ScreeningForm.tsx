import type { JSX } from 'react';

import { Form } from 'antd';

import { usePage } from '@inertiajs/react';

import '@/Pages/EmbedForm/css/embed-form.css';

import ConfirmDialog from '@/Components/Modals/ConfirmDialog';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import { FULLSTORY_MASK_CLASS } from '@/constants/fullstory';
import { useModalContext } from '@/Contexts/ModalContext';
import useSecondaryScreenerForm from '@/hooks/SecondaryScreener/useSecondaryScreenerForm';
import useSubmitSecondaryScreener from '@/hooks/SecondaryScreener/useSubmitSecondaryScreener';
import { cn } from '@/lib/utils';
import DefaultSubmitButton from '@/Pages/EmbedForm/Partials/FormItems/DefaultSubmitButton';
import FormStyles from '@/Pages/EmbedForm/Partials/FormStyles';
import listQuestions from '@/Pages/EmbedForm/Partials/listQuestions';
import PageBreak from '@/Pages/EmbedForm/Partials/PageBreak';
import { useStoreActions, useStoreState } from '@/store/hooks';
import { ANY_TODO, PageProps } from '@/types/general';
import { ReferralData } from '@/types/referral-data';
import Button from '@/UI-Kit/Button';
import { Alert, AlertDescription, AlertTitle } from '@/UI-Kit/Shadcn/alert';

const ScreeningForm = ({ isEdit = false, isDraft = false }: { isEdit?: boolean; isDraft?: boolean }) => {
    useSecondaryScreenerForm();

    const { formId, initialFormValues } = usePage<
        PageProps<{
            formId: string;
            initialFormValues: ANY_TODO;
            referral: ReferralData;
            campaignForm: ANY_TODO;
        }>
    >().props;
    const [form] = Form.useForm();
    const errorMessage = useStoreState(state => state.embedFormModel.errorMessage);
    const setErrorMessage = useStoreActions(actions => actions.embedFormModel.setErrorMessage);
    const setIsFormSubmitting = useStoreActions(actions => actions.embedFormModel.setIsFormSubmitting);
    const { submitSecondaryScreener } = useSubmitSecondaryScreener();
    const formContainers = useStoreState(state => state.embedFormModel.formContainers);
    const formQuestions = useStoreState(state => state.embedFormModel.formQuestions);
    const pageCount = useStoreState(state => state.embedFormModel.pageCount);
    const hasSubmitButton = formQuestions.filter(formQuestion => formQuestion.questionType.name === 'submitButton');
    const { setShowModal, setModalComponent } = useModalContext();

    /** Causes the form to re-render when it has children to show conditional questions */
    useStoreState(state => state.embedFormModel.formUpdated);

    const handleDraft = () => {
        setModalComponent(
            <ModalContentWrapper>
                <ConfirmDialog
                    content="Are you sure you want to save this as a draft? Qualification logic will not run when saved in draft mode."
                    dialogType="confirm"
                    endpoint={null}
                    iconProps={{ name: 'Save' }}
                    okText="Save Draft"
                    submitFunction={() => {
                        setIsFormSubmitting(true);
                        submitSecondaryScreener({ formValues: form.getFieldsValue(), formId, isEdit, isDraft: true });
                        setShowModal(false);
                    }}
                    title="Save as Draft"
                    color="green"
                />
            </ModalContentWrapper>
        );
        setShowModal(true);
    };

    return (
        <div id="embed-form" className={cn('relative flex h-full flex-col gap-y-6', FULLSTORY_MASK_CLASS)}>
            {isDraft && (
                <Alert variant="warning">
                    <AlertTitle>Draft Mode</AlertTitle>
                    <AlertDescription>
                        This form is currently in draft mode. When saved as a draft, the qualification rules will not be
                        applied to your answers.
                    </AlertDescription>
                </Alert>
            )}
            <div className="embed-form-cont h-full">
                <FormStyles />
                {errorMessage && (
                    <Alert variant="destructive">
                        <div className="flex items-center justify-between">
                            <div>
                                <AlertDescription>{errorMessage}</AlertDescription>
                            </div>
                        </div>
                    </Alert>
                )}
                <Form
                    form={form}
                    name="save-questions"
                    initialValues={initialFormValues}
                    layout="vertical"
                    onFinishFailed={() => {
                        if (errorMessage) {
                            setErrorMessage('');
                        }
                        setIsFormSubmitting(false);
                        setErrorMessage("You've missed one or more questions below. Please double check your answers.");
                    }}
                    onFinish={formData => {
                        setIsFormSubmitting(true);
                        submitSecondaryScreener({ formValues: formData, formId, isEdit });
                    }}
                    onValuesChange={() => {
                        if (errorMessage) {
                            setErrorMessage('');
                        }
                    }}
                >
                    {formContainers.length > 1 ? (
                        formContainers?.map(
                            (formContainer: ANY_TODO, containerIndex: number): JSX.Element => (
                                <PageBreak
                                    key={`page-break-${containerIndex.toString()}`}
                                    formContainer={formContainer}
                                    containerIndex={containerIndex}
                                    pageCount={pageCount}
                                >
                                    {listQuestions(formContainer, form)}
                                </PageBreak>
                            )
                        )
                    ) : (
                        <>
                            {formQuestions ? listQuestions(formQuestions, form) : null}
                            {hasSubmitButton.length === 0 && (
                                <div className="flex items-center gap-2">
                                    <Form.Item className="mb-0">
                                        <DefaultSubmitButton />
                                    </Form.Item>

                                    {(isDraft || !isEdit) && (
                                        <Form.Item className="mb-6">
                                            <Button
                                                type="button"
                                                label="Save as Draft"
                                                color="gray"
                                                buttonShape="rounded"
                                                onClick={handleDraft}
                                                buttonType="outline"
                                            />
                                        </Form.Item>
                                    )}
                                </div>
                            )}
                        </>
                    )}
                    {errorMessage && (
                        <Alert variant="destructive" className="mb-5">
                            <div className="flex items-center justify-between">
                                <div>
                                    <AlertDescription>
                                        Please scroll to the top of the survey and complete any questions you may have
                                        missed.
                                    </AlertDescription>
                                </div>
                            </div>
                        </Alert>
                    )}
                </Form>
            </div>
        </div>
    );
};

export default ScreeningForm;
