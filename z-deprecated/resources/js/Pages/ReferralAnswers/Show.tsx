import { useRoute } from 'ziggy-js';

import { Head, usePage } from '@inertiajs/react';

import { FULLSTORY_MASK_CLASS } from '@/constants/fullstory';
import MainLayout from '@/Layouts/MainLayout';
import MainContent from '@/Layouts/MainLayout/Partials/MainContent';
import CampaignFormTabMenu from '@/Pages/ReferralAnswers/Partials/CampaignFormTabMenu';
import ReferralAnswerMenu from '@/Pages/ReferralAnswers/Partials/ReferralAnswerMenu';
import ReferralAnswers from '@/Pages/ReferralAnswers/Partials/ReferralAnswers';
import PageActions from '@/Pages/Referrals/Partials/Manage/PageActions';
import PageBreadcrumbs from '@/Pages/Referrals/Partials/Manage/PageBreadcrumbs';
import { PageProps } from '@/types/general';
import { ReferralData } from '@/types/referral-data';
import { BreadcrumbItem, B<PERSON><PERSON>rumbLink, BreadcrumbPage, BreadcrumbSeparator } from '@/UI-Kit/Shadcn/breadcrumb';

const Show = () => {
    const route = useRoute();
    const { referral } = usePage<
        PageProps<{
            referral: ReferralData;
        }>
    >().props;

    return (
        <MainLayout
            header={<span className={FULLSTORY_MASK_CLASS}>{referral.full_name}</span>}
            headerActions={<PageActions />}
            useMainContentWrapper={false}
            stickyHeader={false}
            breadcrumbs={
                <PageBreadcrumbs>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                        <BreadcrumbLink
                            className={FULLSTORY_MASK_CLASS}
                            href={route('referrals.show', referral.uuid)}
                        >
                            {referral.full_name}
                        </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                        <BreadcrumbPage>Answers</BreadcrumbPage>
                    </BreadcrumbItem>
                </PageBreadcrumbs>
            }
        >
            <Head title={referral.full_name} />

            <ReferralAnswerMenu />

            <MainContent>
                <div className="grid grid-cols-1 gap-y-5">
                    <CampaignFormTabMenu />
                    <ReferralAnswers />
                </div>
            </MainContent>
        </MainLayout>
    );
};
export default Show;
