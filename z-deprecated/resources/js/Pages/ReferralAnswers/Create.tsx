import { useRoute } from 'ziggy-js';

import { Head, usePage } from '@inertiajs/react';

import ModalComponent from '@/Components/Modals/ModalComponent';
import { FULLSTORY_MASK_CLASS } from '@/constants/fullstory';
import useSecondaryScreenerForm from '@/hooks/SecondaryScreener/useSecondaryScreenerForm';
import MainLayout from '@/Layouts/MainLayout';
import MainContent from '@/Layouts/MainLayout/Partials/MainContent';
import CampaignFormTabMenu from '@/Pages/ReferralAnswers/Partials/CampaignFormTabMenu';
import ReferralAnswerMenu from '@/Pages/ReferralAnswers/Partials/ReferralAnswerMenu';
import ScreeningForm from '@/Pages/ReferralAnswers/ScreeningForm';
import PageActions from '@/Pages/Referrals/Partials/Manage/PageActions';
import PageBreadcrumbs from '@/Pages/Referrals/Partials/Manage/PageBreadcrumbs';
import { ANY_TODO, PageProps } from '@/types/general';
import { ReferralData } from '@/types/referral-data';
import Modal from '@/UI-Kit/Modal';
import { BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator } from '@/UI-Kit/Shadcn/breadcrumb';
import { Card, CardContent, CardHeader, CardTitle } from '@/UI-Kit/Shadcn/card';

const Create = () => {
    const route = useRoute();
    useSecondaryScreenerForm();

    const { referral, campaignForm } = usePage<
        PageProps<{
            referral: ReferralData;
            campaignForm: ANY_TODO;
        }>
    >().props;

    return (
        <MainLayout
            header={<span className={FULLSTORY_MASK_CLASS}>{referral.full_name}</span>}
            headerActions={<PageActions />}
            useMainContentWrapper={false}
            stickyHeader={false}
            breadcrumbs={
                <PageBreadcrumbs>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                        <BreadcrumbLink
                            className={FULLSTORY_MASK_CLASS}
                            href={route('referrals.show', referral.uuid)}
                        >
                            {referral.full_name}
                        </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                        <BreadcrumbPage>Screener</BreadcrumbPage>
                    </BreadcrumbItem>
                </PageBreadcrumbs>
            }
        >
            <Head title={referral.full_name} />

            <ReferralAnswerMenu />

            <MainContent>
                <div className="grid grid-cols-1 gap-y-5">
                    <CampaignFormTabMenu />
                    <Card>
                        <CardHeader>
                            <CardTitle>Screener: {campaignForm.campaign_name}</CardTitle>
                        </CardHeader>
                        <CardContent className="py-0">
                            <Modal>
                                <ScreeningForm />
                                <ModalComponent />
                            </Modal>
                        </CardContent>
                    </Card>
                </div>
            </MainContent>
        </MainLayout>
    );
};

export default Create;
