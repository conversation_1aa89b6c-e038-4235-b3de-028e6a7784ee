import { ReactNode } from 'react';

import { cn } from '@/lib/utils';
import { ANY_TODO } from '@/types/general';
import { RadioCheckboxSelectOption } from '@/types/question-options';

const Answer = ({ leadAnswer }: ANY_TODO) => {
    if (typeof leadAnswer.answer === 'undefined' || leadAnswer.answer === null) {
        return <span className="text-red-700">No answer given</span>;
    }

    if (typeof leadAnswer.answer === 'string') {
        return <StringAnswer leadAnswer={leadAnswer} />;
    }

    if (typeof leadAnswer.answer === 'number') {
        return <NumberAnswer leadAnswer={leadAnswer} />;
    }

    if (
        (leadAnswer.questionType.isAllowOptions || leadAnswer.questionType.isAllowSites) &&
        Array.isArray(leadAnswer.answer)
    ) {
        return <ArrayAnswer leadAnswer={leadAnswer} />;
    }

    if (leadAnswer.answer && typeof leadAnswer.answer === 'object') {
        return <ObjectAnswer leadAnswer={leadAnswer} />;
    }

    return null;
};

const StringAnswer = ({ leadAnswer }: ANY_TODO) => {
    const trimmedAnswer = leadAnswer.answer.trim();

    return <span className={cn({ 'text-red-700': !trimmedAnswer })}>{trimmedAnswer ?? `Answer was blank`}</span>;
};

const NumberAnswer = ({ leadAnswer }: ANY_TODO) => (
    <span className={cn({ 'text-red-700': !leadAnswer.answer })}>{leadAnswer.answer ?? `Answer was blank`}</span>
);

const ArrayAnswer = ({ leadAnswer }: ANY_TODO) => {
    const answerOptions = leadAnswer.answer as RadioCheckboxSelectOption[];

    return (
        <ul className="list-inside list-disc">
            {answerOptions.map(answerOption => (
                <li key={answerOption.value}>{answerOption.label}</li>
            ))}
        </ul>
    );
};

const ObjectAnswer = ({ leadAnswer }: ANY_TODO) => (
    <div>
        {Object.entries(leadAnswer.answer).map(([key, value]) => {
            const answerValue =
                value && typeof value === 'object'
                    ? Object.entries(value).map(([subKey, subValue]) => (
                          <div key={subKey}>{`${subKey}: ${subValue}`}</div>
                      ))
                    : (value as string | ReactNode);

            return (
                <li key={key} className="flex items-baseline gap-x-3">
                    <div className="font-bold">{key}:</div>
                    <div className="inline-block">{answerValue}</div>
                </li>
            );
        })}
    </div>
);

export default Answer;
