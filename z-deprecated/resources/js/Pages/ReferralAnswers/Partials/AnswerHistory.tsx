import dateTimeZoneFormatter from '@/Components/AgGrid/formatters/dateTimeZoneFormatter';
import Answer from '@/Pages/ReferralAnswers/Answer';
import { ANY_TODO } from '@/types/general';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';

const AnswerHistory = ({ leadAnswer }: ANY_TODO) => {
    if (typeof leadAnswer.activities === 'undefined' || leadAnswer.activities.length === 0) {
        return null;
    }

    const defaultKey = `${leadAnswer.uuid}-history`;

    return (
        <Accordion type="multiple" className="w-full border border-yellow-400" defaultValue={[defaultKey]}>
            <AccordionItem key={defaultKey} value={defaultKey} className="last:border-b-0">
                <AccordionTrigger
                    className="border-b border-yellow-400 bg-yellow-50 px-5 font-bold text-yellow-800"
                    iconPosition="left"
                >
                    Answer Changes
                </AccordionTrigger>

                <AccordionContent className="flex flex-col gap-y-4 p-4">
                    {leadAnswer.activities.map((activity: ANY_TODO) => {
                        const { new_answer: activityAnswer, createdAt } = activity;

                        return (
                            <div key={Math.random()}>
                                <div className="mb-2 font-semibold">{dateTimeZoneFormatter({ value: createdAt })}</div>
                                <Answer leadAnswer={{ ...leadAnswer, answer: activityAnswer }} />
                            </div>
                        );
                    })}
                </AccordionContent>
            </AccordionItem>
        </Accordion>
    );
};

export default AnswerHistory;
