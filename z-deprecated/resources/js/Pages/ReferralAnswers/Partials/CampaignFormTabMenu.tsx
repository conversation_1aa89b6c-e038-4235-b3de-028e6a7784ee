import { useRoute } from 'ziggy-js';

import { Link, usePage } from '@inertiajs/react';

import { cn } from '@/lib/utils';
import { ANY_TODO, PageProps } from '@/types/general';
import { ReferralData } from '@/types/referral-data';
import { Tabs, TabsList, TabsTrigger } from '@/UI-Kit/Shadcn/tabs';

const CampaignFormTabMenu = () => {
    const route = useRoute();
    const { referral, campaignForm, additionalCampaignForms } = usePage<
        PageProps<{
            additionalCampaignForms: ANY_TODO;
            campaignForm: ANY_TODO;
            referral: ReferralData;
        }>
    >().props;

    return (
        <Tabs tabType="pill" color="blue" defaultValue={campaignForm.uuid} className="grid w-full grid-cols-1 gap-y-5">
            <TabsList className="justify-start">
                <TabsTrigger
                    value={referral.campaign_form.uuid}
                    className="flex-col items-center text-center text-xs uppercase"
                    asChild
                >
                    <Link
                        className="no-anchor-style"
                        href={route('referral-answers.show', [referral.uuid, referral.campaign_form.uuid])}
                    >
                        <div
                            className={cn('w-full text-[10px] font-bold text-green-600', {
                                'text-red-500': !referral.preScreenerQualifiedStatus,
                            })}
                        >
                            {referral.preScreenerQualifiedStatus > 0 ? 'Qualified' : 'Disqualified'}
                        </div>
                        {referral.campaign_form.campaign_name}
                    </Link>
                </TabsTrigger>

                {additionalCampaignForms?.map((additionalCampaignForm: ANY_TODO) => {
                    let formStatus = 'Pending';

                    if (additionalCampaignForm.is_draft) {
                        formStatus = 'Draft';
                    } else if (additionalCampaignForm.hasAnswers) {
                        formStatus = additionalCampaignForm.qualifiedStatus > 0 ? 'Qualified' : 'Disqualified';
                    }

                    return (
                        <TabsTrigger
                            key={additionalCampaignForm.uuid}
                            value={additionalCampaignForm.uuid}
                            className="flex-col items-center text-center text-xs uppercase"
                            asChild
                        >
                            <Link
                                className="no-anchor-style"
                                href={route('referral-answers.show', [referral.uuid, additionalCampaignForm.uuid])}
                            >
                                <div
                                    className={cn('w-full text-[10px] font-bold text-green-600', {
                                        'text-red-500':
                                            additionalCampaignForm.qualifiedStatus === 0 &&
                                            additionalCampaignForm.hasAnswers > 0 &&
                                            !additionalCampaignForm.is_draft,
                                        'text-gray-600':
                                            additionalCampaignForm.hasAnswers === 0 || additionalCampaignForm.is_draft,
                                    })}
                                >
                                    {formStatus}
                                </div>
                                <div>{additionalCampaignForm.campaign_name}</div>
                            </Link>
                        </TabsTrigger>
                    );
                })}
            </TabsList>
        </Tabs>
    );
};

export default CampaignFormTabMenu;
