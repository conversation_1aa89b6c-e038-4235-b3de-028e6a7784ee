/* eslint-disable @typescript-eslint/no-unused-vars */
import { useState } from 'react';

import { Info } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { usePage } from '@inertiajs/react';

import { FULLSTORY_MASK_CLASS } from '@/constants/fullstory';
import { cn } from '@/lib/utils';
import AnswerContainer from '@/Pages/ReferralAnswers/Partials/AnswerContainer';
import { ANY_TODO, PageProps, UUID } from '@/types/general';
import { ReferralData } from '@/types/referral-data';
import Button from '@/UI-Kit/Button';
import { Alert, AlertDescription, AlertTitle } from '@/UI-Kit/Shadcn/alert';
import { Card, CardAction, CardContent, CardDescription, CardHeader, CardTitle } from '@/UI-Kit/Shadcn/card';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/UI-Kit/Shadcn/tooltip';
import { SingleComboSelectOption } from '@/UI-Kit/Shadcn/types/combobox-select';
import { canAccess } from '@/utils/auth';

const ReferralAnswers = () => {
    const route = useRoute();
    const [selectedQualificationBucket, setSelectedQualificationBucket] = useState<UUID | undefined>();
    const { auth, referral, leadAnswers, campaignForm, qualificationBuckets, isDraft } = usePage<
        PageProps<{
            campaignForm: ANY_TODO;
            leadAnswers: ANY_TODO;
            qualificationBuckets: ANY_TODO;
            referral: ReferralData;
            isAdditionalCampaignForm: boolean;
            isDraft: boolean;
        }>
    >().props;

    const formatOptionLabel = (option: ANY_TODO) => (
        <div>
            <span>{option.label}: </span>
            <span
                className={cn('font-semibold text-green-600', {
                    'text-red-500': option.qualifiedStatus === 0,
                })}
            >
                {option.qualifiedStatus === 0 ? 'Disqualified' : 'Qualified'}
            </span>
        </div>
    );

    return (
        <Card className="w-full">
            <CardHeader>
                <CardTitle>
                    <CardDescription>
                        The lead submitted the form <strong>{referral.version}</strong> times. See the previous answers
                        below.
                    </CardDescription>
                </CardTitle>
                <CardAction>
                    <div className={cn('flex flex-wrap items-center justify-between gap-4', FULLSTORY_MASK_CLASS)}>
                        <div className="flex items-center gap-x-3">
                            <div className="flex items-center gap-x-1">
                                <TooltipProvider key="disabled-bulk-actions">
                                    <Tooltip>
                                        <TooltipTrigger asChild>
                                            <Info size={20} />
                                        </TooltipTrigger>
                                        <TooltipContent className="w-56 text-center">
                                            Select a qualification bucket to view the qualification details for each of
                                            the answers.
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>
                                <ComboSelect
                                    placeholder="Qualification Bucket"
                                    options={qualificationBuckets}
                                    formatOptionLabel={formatOptionLabel}
                                    onChange={option => {
                                        setSelectedQualificationBucket((option as SingleComboSelectOption)?.value);
                                    }}
                                    usePortal
                                />
                            </div>

                            {/*   {((campaignForm.is_editable && canAccess(['editPreScreener'], auth.can)) || isDraft) && (
                                <Button
                                    color="green"
                                    size="sm"
                                    onClick={() =>
                                        router.visit(route('referral-answers.edit', [referral.uuid, campaignForm.uuid]))
                                    }
                                >
                                    Edit
                                </Button>
                            )} */}
                        </div>
                    </div>
                </CardAction>
            </CardHeader>
            <CardContent variant="fullPadding" className={cn('flex flex-col gap-y-6', FULLSTORY_MASK_CLASS)}>
                {isDraft && (
                    <Alert variant="warning">
                        <AlertTitle>Draft Mode</AlertTitle>
                        <AlertDescription>
                            This form is currently in draft mode. When saved as a draft, the qualification rules will
                            not be applied to your answers.
                        </AlertDescription>
                    </Alert>
                )}
                {leadAnswers.length > 0 ? (
                    <AnswerContainer
                        leadAnswers={leadAnswers}
                        selectedQualificationBucket={selectedQualificationBucket}
                    />
                ) : (
                    <span>There are no answers to display</span>
                )}
            </CardContent>
        </Card>
    );
};

export default ReferralAnswers;
