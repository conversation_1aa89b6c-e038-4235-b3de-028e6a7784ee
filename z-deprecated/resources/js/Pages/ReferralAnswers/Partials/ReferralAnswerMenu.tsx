import { useRoute } from 'ziggy-js';

import { Link, usePage } from '@inertiajs/react';

import { PageProps } from '@/types/general';
import { ReferralData } from '@/types/referral-data';
import { Tabs, TabsList, TabsTrigger } from '@/UI-Kit/Shadcn/tabs';
import { canAccess } from '@/utils/auth';

const ReferralAnswerMenu = () => {
    const route = useRoute();
    const { auth, referral } = usePage<PageProps<{ referral: ReferralData }>>().props;

    return (
        <Tabs tabType="full" color="green" defaultValue="forms" sticky>
            <TabsList>
                <TabsTrigger value="overview" asChild>
                    <Link
                        className="no-anchor-style"
                        href={route('referrals.show', { leadLimitByUser: referral.uuid, tab: 'overview' })}
                    >
                        Overview
                    </Link>
                </TabsTrigger>

                <TabsTrigger value="forms" asChild>
                    <Link
                        className="no-anchor-style"
                        href={route('referral-answers.show', [referral.uuid, referral.campaign_form.uuid])}
                    >
                        Forms
                    </Link>
                </TabsTrigger>

                <TabsTrigger value="marketing" asChild>
                    <Link
                        className="no-anchor-style"
                        href={route('referrals.show', { leadLimitByUser: referral.uuid, tab: 'marketing' })}
                    >
                        Marketing
                    </Link>
                </TabsTrigger>

                {canAccess(['viewReferralDocument'], auth.can) && (
                    <TabsTrigger value="referral-documents" asChild>
                        <Link
                            className="no-anchor-style"
                            href={route('referrals.show', {
                                leadLimitByUser: referral.uuid,
                                tab: 'referralDocuments',
                            })}
                        >
                            Documents
                        </Link>
                    </TabsTrigger>
                )}

                {canAccess(['viewReferralHistory'], auth.can) && (
                    <TabsTrigger value="history" asChild>
                        <Link
                            className="no-anchor-style"
                            href={route('referrals.show', { leadLimitByUser: referral.uuid, tab: 'history' })}
                        >
                            History
                        </Link>
                    </TabsTrigger>
                )}
            </TabsList>
        </Tabs>
    );
};

export default ReferralAnswerMenu;
