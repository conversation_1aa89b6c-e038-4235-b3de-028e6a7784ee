import { Fragment } from 'react';

import { isArray } from 'lodash';

import { cn } from '@/lib/utils';
import { ANY_TODO } from '@/types/general';
import { QualDetails } from '@/types/lead-qualification-details-data';
import { SituationCondition } from '@/types/situation-types';
import Badge from '@/UI-Kit/Badge';
import { SingleComboSelectOption } from '@/UI-Kit/Shadcn/types/combobox-select';
import { getRandomInt } from '@/utils/helpers';

const QualificationDetailsRows = ({ leadAnswerResults }: { leadAnswerResults: ANY_TODO }) => {
    const containerClass = 'col-span-1';
    const labelClass = 'text-sm mb-1 font-semibold text-gray-900';

    return (
        <>
            {leadAnswerResults.map((leadAnswerResult: ANY_TODO, index: ANY_TODO) => (
                <Fragment key={getRandomInt()}>
                    <dl className="mx-4 grid grid-cols-1 gap-x-8 gap-y-3 xl:grid-cols-[repeat(7,1fr)_50px_repeat(4,1fr)]">
                        <div className={cn(containerClass, 'xl:col-span-2')}>
                            <dt className={cn(labelClass, { 'lg:hidden': index !== 0 })}>Operator</dt>
                            <dd>{leadAnswerResult.operator.label}</dd>
                        </div>
                        <div className={cn(containerClass, 'xl:col-span-5')}>
                            <dt className={cn(labelClass, { 'lg:hidden': index !== 0 })}>Condition Answer(s)</dt>
                            <dd>
                                <FormattedAnswerOrCondition answerOrCondition={leadAnswerResult.conditions} />
                            </dd>
                        </div>
                        <div className={cn(containerClass, 'xl:self-start')}>
                            <dt className={cn(labelClass, { 'lg:hidden': index !== 0 })}>Correct</dt>
                            <dd>
                                <Badge
                                    bgColor={leadAnswerResult.isCorrect ? 'green' : 'red'}
                                    textColor={leadAnswerResult.isCorrect ? 'green' : 'red'}
                                >
                                    {leadAnswerResult.isCorrect ? 'Yes' : 'No'}
                                </Badge>
                            </dd>
                        </div>
                    </dl>
                    <div className="relative mx-4 last:hidden">
                        <div aria-hidden="true" className="absolute inset-0 flex items-center">
                            <div className="w-full border-t border-gray-200" />
                        </div>
                        <div className="relative flex justify-start">
                            <span className="text-semibold bg-white pr-2 text-sm text-gray-800">
                                {leadAnswerResult.groupOperator}
                            </span>
                        </div>
                    </div>
                </Fragment>
            ))}
        </>
    );
};

const FormattedAnswerOrCondition = ({
    answerOrCondition,
}: {
    answerOrCondition: QualDetails['referralAnswer'] | SituationCondition['conditions'];
}) => {
    if (isArray(answerOrCondition)) {
        return (
            <div className="grid grid-cols-1 gap-y-2">
                {answerOrCondition?.map((referralAnswer: SingleComboSelectOption) => (
                    <Badge key={referralAnswer.value} className="w-fit">
                        {referralAnswer.label}
                    </Badge>
                ))}
            </div>
        );
    }

    if (typeof answerOrCondition === 'object') {
        return (
            <div className="grid grid-cols-1 gap-y-2">
                {Object.entries(answerOrCondition).map(([key, value]) => (
                    <Badge key={key} className="w-fit">{`${key}: ${value}`}</Badge>
                ))}
            </div>
        );
    }

    return <Badge>{String(answerOrCondition)}</Badge>;
};

export default QualificationDetailsRows;
