import { cn } from '@/lib/utils';
import Answer from '@/Pages/ReferralAnswers/Answer';
import AnswerHistory from '@/Pages/ReferralAnswers/Partials/AnswerHistory';
import QualificationDetails from '@/Pages/ReferralAnswers/Partials/QualificationDetails';
import { ANY_TODO } from '@/types/general';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';

const AnswerContainer = ({ leadAnswers, selectedQualificationBucket }: ANY_TODO) => {
    const defaultKeys = leadAnswers?.length ? leadAnswers.map((answer: ANY_TODO) => answer.uuid) : [];

    return (
        <Accordion type="multiple" className={cn('w-full border border-gray-200')} defaultValue={defaultKeys}>
            {leadAnswers.map((leadAnswer: ANY_TODO) => (
                <AccordionItem key={leadAnswer.uuid} value={leadAnswer.uuid} className="last:border-b-0">
                    <AccordionTrigger
                        className="border-b border-gray-200 bg-gray-50 px-5 font-bold"
                        iconPosition="left"
                    >
                        <div dangerouslySetInnerHTML={{ __html: leadAnswer.questionLabel }} />
                    </AccordionTrigger>

                    <AccordionContent className="flex flex-col gap-y-4 p-4">
                        <Answer leadAnswer={leadAnswer} />
                        <QualificationDetails
                            leadAnswerResults={leadAnswer.leadAnswerResults}
                            selectedQualificationBucket={selectedQualificationBucket}
                        />
                        <AnswerHistory leadAnswer={leadAnswer} />

                        {leadAnswer.children.length > 0 && <AnswerContainer leadAnswers={leadAnswer.children} />}
                    </AccordionContent>
                </AccordionItem>
            ))}
        </Accordion>
    );
};

export default AnswerContainer;
