import QualificationDetailsRows from '@/Pages/ScreenerResponses/components/QualificationDetailsRows';
import { ANY_TODO } from '@/types/general';

const QualificationDetails = ({ leadAnswerResults, selectedQualificationBucket }: ANY_TODO) => {
    const filteredLeadAnswerResults = (leadAnswerResults ?? []).filter(
        (result: ANY_TODO) => result.situationUUID === selectedQualificationBucket
    );

    if (!selectedQualificationBucket || filteredLeadAnswerResults.length === 0) {
        return null;
    }

    return (
        <div className="grid-col-1 grid gap-y-3 border border-gray-200 pb-3">
            <div className="border-b border-gray-200 bg-blue-50 p-2.5 font-semibold">Qualification Details</div>
            <QualificationDetailsRows leadAnswerResults={filteredLeadAnswerResults} />
        </div>
    );
};

export default QualificationDetails;
