import { ReactNode } from 'react';

import { FlaskRound } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { Head, usePage } from '@inertiajs/react';

import TableAndPageActionsDropdownTrigger from '@/Components/CommonButtons/TableAndPageActionsDropdownTrigger';
import ConditionalWrapper from '@/Components/ConditionalWrapper';
import ModalComponent from '@/Components/Modals/ModalComponent';
import QuestionBuilderFormProvider from '@/Contexts/QuestionBuilderFormContext';
import MainLayout from '@/Layouts/MainLayout';
import MainContent from '@/Layouts/MainLayout/Partials/MainContent';
import CampaignFormEditMenu from '@/Pages/CampaignForms/Partials/CampaignFormEditMenu';
import QuestionBuilderForm from '@/Pages/QuestionBuilder/Partials/QuestionBuilderForm';
import { PageProps } from '@/types/general';
import { QuestionBuilderPageProps } from '@/types/question-builder';
import Modal from '@/UI-Kit/Modal';
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from '@/UI-Kit/Shadcn/breadcrumb';
import { DropdownMenu, DropdownMenuContent, DropdownMenuLinkItem } from '@/UI-Kit/Shadcn/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/UI-Kit/Shadcn/tooltip';
import { canAccess } from '@/utils/auth';

const Edit = () => {
    const { entity } = usePage<PageProps<QuestionBuilderPageProps>>().props;

    return (
        <MainLayout
            useMainContentWrapper={false}
            stickyHeader={false}
            header={<span>{entity.campaignName}</span>}
            headerActions={<PageActions entity={entity} />}
            breadcrumbs={<PageBreadcrumbs entity={entity} />}
        >
            <Head title={`Edit - ${entity.campaignName}`} />
            <CampaignFormEditMenu campaignFormUUID={entity.uuid} initialSelectedValue="questionBuilder" />
            <MainContent>
                <QuestionBuilderFormProvider>
                    <Modal>
                        <QuestionBuilderForm />
                        <ModalComponent />
                    </Modal>
                </QuestionBuilderFormProvider>
            </MainContent>
        </MainLayout>
    );
};

const getDisabledTestFormTooltip = (children: ReactNode) => (
    <TooltipProvider key="disabled-test-form-action">
        <Tooltip>
            <TooltipTrigger asChild>
                <div>{children}</div>
            </TooltipTrigger>
            <TooltipContent className="text-center" side="left">
                <div>
                    <div>This form is configured</div>
                    <div>for internal use only</div>
                </div>
            </TooltipContent>
        </Tooltip>
    </TooltipProvider>
);

const PageActions = ({ entity }: QuestionBuilderPageProps) => {
    const route = useRoute();
    const { auth } = usePage<PageProps>().props;

    return (
        <>
            {canAccess(['testCampaignForm'], auth.can) && (
                <ConditionalWrapper
                    key="test-form-wrapper"
                    condition={entity.showInternal}
                    wrapper={getDisabledTestFormTooltip}
                >
                    <DropdownMenu modal={false}>
                        <TableAndPageActionsDropdownTrigger
                            dropdownLabel="Page Actions"
                            buttonProps={{ buttonType: 'outline', color: 'gray' }}
                        />

                        <DropdownMenuContent align="end">
                            <DropdownMenuLinkItem
                                href={route('test-forms', { form_public_id: entity.formPublic, test: 'true' })}
                                disabled={entity.showInternal}
                                anchorProps={{ target: '_blank' }}
                                isExternalLink
                            >
                                <FlaskRound className="h-4 w-4!" />
                                Test Form
                            </DropdownMenuLinkItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </ConditionalWrapper>
            )}
        </>
    );
};

const PageBreadcrumbs = ({ entity }: QuestionBuilderPageProps) => {
    const route = useRoute();

    return (
        <Breadcrumb>
            <BreadcrumbList>
                <BreadcrumbItem>
                    <BreadcrumbLink href={route('studies.index')}>Study Hub</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                    <BreadcrumbLink href={route('studies.show', entity.studyUUID)}>{entity.studyName}</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                    <BreadcrumbLink href={route('campaign-forms.index')}>Campaign Forms</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                    <BreadcrumbLink href={route('campaign-forms.show', entity.uuid)}>
                        {entity.campaignName}
                    </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                    <BreadcrumbPage>Form Builder</BreadcrumbPage>
                </BreadcrumbItem>
            </BreadcrumbList>
        </Breadcrumb>
    );
};

export default Edit;
