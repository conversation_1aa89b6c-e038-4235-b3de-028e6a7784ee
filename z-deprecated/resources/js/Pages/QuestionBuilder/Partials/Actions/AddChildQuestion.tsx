import { light } from '@fortawesome/fontawesome-svg-core/import.macro';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { useStoreActions } from '@/store/hooks';
import { ModifiedQuestion } from '@/types/question';
import Button from '@/UI-Kit/Button';

const AddChildQuestion = ({ question }: { question: ModifiedQuestion }) => {
    const setAddQuestionSettings = useStoreActions(actions => actions.questionBuilderModel.setAddQuestionSettings);

    return (
        <Button
            type="button"
            label=""
            color="gray"
            buttonShape="circle"
            size="sm"
            icon={<FontAwesomeIcon icon={light('plus')} />}
            onClick={() => {
                setAddQuestionSettings({ addAfterQuestion: question, parentQuestion: question, isChild: true });
            }}
        />
    );
};

export default AddChildQuestion;
