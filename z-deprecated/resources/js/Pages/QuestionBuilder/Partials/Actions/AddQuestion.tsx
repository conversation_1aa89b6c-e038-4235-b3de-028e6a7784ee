import { icon } from '@fortawesome/fontawesome-svg-core/import.macro';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { cn } from '@/lib/utils';
import { useStoreActions } from '@/store/hooks';
import { AddQuestionSettings } from '@/store/types/QuestionBuilderModel';
import Button from '@/UI-Kit/Button';

const AddQuestion = ({
    question,
    isStatic = false,
}: {
    question: AddQuestionSettings['addAfterQuestion'];
    isStatic?: boolean;
}) => {
    const setAddQuestionSettings = useStoreActions(actions => actions.questionBuilderModel.setAddQuestionSettings);

    return (
        <div
            className={cn('absolute -bottom-4 flex w-full justify-center', {
                'transition-height opacity-0 delay-150 ease-in-out group-hover:opacity-100': !isStatic,
            })}
        >
            <div className="relative flex justify-center">
                <Button
                    type="button"
                    label=""
                    color="green"
                    buttonShape="circle"
                    size="mini"
                    icon={<FontAwesomeIcon icon={icon({ name: 'plus', style: 'light' })} />}
                    className="ring-gray-200!"
                    onClick={() => {
                        setAddQuestionSettings({ addAfterQuestion: question, parentQuestion: null, isChild: false });
                    }}
                />
            </div>
        </div>
    );
};

export default AddQuestion;
