import { regular } from '@fortawesome/fontawesome-svg-core/import.macro';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import Button from '@/UI-Kit/Button';
import { ButtonProps } from '@/UI-Kit/Button/types';

const Edit = (props: Partial<ButtonProps>) => (
    <Button
        type="button"
        color="gray"
        label=""
        buttonShape="circle"
        icon={<FontAwesomeIcon icon={regular('pen')} />}
        {...props}
    />
);

export default Edit;
