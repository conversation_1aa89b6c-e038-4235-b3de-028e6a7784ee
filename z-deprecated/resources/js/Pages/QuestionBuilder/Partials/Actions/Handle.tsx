import { forwardRef } from 'react';

import { solid } from '@fortawesome/fontawesome-svg-core/import.macro';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { cn } from '@/lib/utils';
import Button from '@/UI-Kit/Button';
import { ButtonProps } from '@/UI-Kit/Button/types';

const Handle = forwardRef<HTMLButtonElement, Partial<ButtonProps>>((props, ref) => (
    <Button
        type="button"
        ref={ref}
        label=""
        color="gray"
        buttonShape="circle"
        icon={<FontAwesomeIcon icon={solid('grip-vertical')} />}
        className={cn('cursor-move', props.className)}
        {...props}
    />
));

Handle.displayName = 'Handle';

export default Handle;
