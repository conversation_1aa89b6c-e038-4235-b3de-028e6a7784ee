import { merge } from 'lodash';

import FontColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontColor';
import TextDecoration from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/TextDecoration';
import StyleCountAccordionHeader from '@/Pages/QuestionBuilder/Partials/Sidebar/StyleCountAccordionHeader';
import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';
import getStyles from '@/utils/EmbedForm/get-styles';

const LinkAccordion = ({ namePath, label, settings }: FormSettingFieldProps) => {
    const styleCounts = {
        link: getStyles(settings?.form?.link?.style).length,
    };

    return (
        <AccordionItem value="link-styles">
            <AccordionTrigger
                className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                iconPosition="left"
            >
                <StyleCountAccordionHeader header="Link Styles" styleCount={styleCounts?.link as number} />
            </AccordionTrigger>

            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                <FontColor namePath={merge([], namePath)} label={`${label} Link Color`} />
                <TextDecoration namePath={merge([], namePath)} label={`${label} Text Decoration`} />
            </AccordionContent>
        </AccordionItem>
    );
};

export default LinkAccordion;
