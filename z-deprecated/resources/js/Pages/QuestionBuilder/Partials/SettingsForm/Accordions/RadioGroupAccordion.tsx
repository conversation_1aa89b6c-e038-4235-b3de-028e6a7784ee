import { merge, sum } from 'lodash';

import { cn } from '@/lib/utils';
import BackgroundColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BackgroundColor';
import Border from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Border';
import BorderColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BorderColor';
import BorderStyle from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BorderStyle';
import DisplayFlex from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/DisplayFlex';
import FontColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontColor';
import FontSize from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontSize';
import FontWeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontWeight';
import Height from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Height';
import LineHeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/LineHeight';
import TextTransform from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/TextTransform';
import Width from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Width';
import AdvancedAccordionHeader from '@/Pages/QuestionBuilder/Partials/Sidebar/AdvancedAccordionHeader';
import StyleCountAccordionHeader from '@/Pages/QuestionBuilder/Partials/Sidebar/StyleCountAccordionHeader';
import { useStoreState } from '@/store/hooks';
import { KeyValueGeneric } from '@/types/general';
import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';
import getStyles from '@/utils/EmbedForm/get-styles';

const RadioGroupAccordion = ({
    namePath,
    settings,
}: {
    namePath: FormSettingFieldProps['namePath'];
    settings: KeyValueGeneric;
}) => {
    const showAdvancedStyles = useStoreState(state => state.questionBuilderModel.showAdvancedStyles);
    const styleCounts = {
        button: getStyles(settings?.radio?.button?.style).length,
        checked: getStyles(settings?.radio?.checked?.style).length,
        container: getStyles(settings?.radio?.container?.style).length,
        label: getStyles(settings?.radio?.label?.style).length,
    };

    return (
        <div className={cn({ hidden: !showAdvancedStyles })}>
            <AccordionItem value="radio-styles">
                <AccordionTrigger
                    className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold [&_h3]:mb-0"
                    iconPosition="left"
                >
                    <AdvancedAccordionHeader
                        header="Radio Styles"
                        styleCount={sum(Object.values(styleCounts as KeyValueGeneric))}
                    />
                </AccordionTrigger>

                <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                    <Accordion
                        defaultValue={['radio-general']}
                        type="multiple"
                        className={cn('w-full border border-b-0 border-gray-100 [&_h3]:mb-0')}
                    >
                        <AccordionItem value="radio-general">
                            <AccordionTrigger
                                className="sticky top-[58px] z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader
                                    header="General"
                                    styleCount={styleCounts?.container as number}
                                />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <DisplayFlex namePath={merge([], [...namePath, 'container'])} label="Position" />
                            </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value="radio-button">
                            <AccordionTrigger
                                className="sticky top-[58px] z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader header="Button" styleCount={styleCounts?.button as number} />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <Width namePath={merge([], [...namePath, 'button'])} label="" />
                                <Height namePath={merge([], [...namePath, 'button'])} label="" />
                                <Border namePath={merge([], [...namePath, 'button'])} label="Border" />
                                <BorderStyle namePath={merge([], [...namePath, 'button'])} label="Border Style" />
                                <BorderColor namePath={merge([], [...namePath, 'button'])} label="Border Color" />
                                <BackgroundColor
                                    namePath={merge([], [...namePath, 'button'])}
                                    label="Background Color"
                                />
                            </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value="radio-checked">
                            <AccordionTrigger
                                className="sticky top-[58px] z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader
                                    header="Checked" // eslint-disable-next-line no-unsafe-optional-chaining
                                    styleCount={(styleCounts?.checked + styleCounts?.label) as number}
                                />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <BackgroundColor namePath={merge([], [...namePath, 'checked'])} label="Checked Color" />
                                <BorderColor
                                    namePath={merge([], [...namePath, 'checked'])}
                                    label="Checked Border Color"
                                />
                                <FontSize namePath={merge([], [...namePath, 'label'])} label="Label Font Size" />
                                <LineHeight namePath={merge([], [...namePath, 'label'])} label="Label Line Height" />
                                <FontWeight namePath={merge([], [...namePath, 'label'])} label="Label Font Weight" />
                                <TextTransform
                                    namePath={merge([], [...namePath, 'label'])}
                                    label="Label Font Transform"
                                />
                                <FontColor namePath={merge([], [...namePath, 'label'])} label="Label Font Color" />
                            </AccordionContent>
                        </AccordionItem>
                    </Accordion>
                </AccordionContent>
            </AccordionItem>
        </div>
    );
};

export default RadioGroupAccordion;
