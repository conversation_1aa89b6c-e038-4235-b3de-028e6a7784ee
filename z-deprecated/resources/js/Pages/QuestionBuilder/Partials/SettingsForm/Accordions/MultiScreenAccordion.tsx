import { sum } from 'lodash';

import { cn } from '@/lib/utils';
import BackgroundColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BackgroundColor';
import Border from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Border';
import BorderColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BorderColor';
import BorderStyle from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BorderStyle';
import FontColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontColor';
import FontSize from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontSize';
import FontWeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontWeight';
import LineHeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/LineHeight';
import Margin from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Margin';
import Padding from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Padding';
import PlainText from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/PlainText';
import ShowPageNumber from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/ShowPageNumber';
import TextTransform from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/TextTransform';
import Width from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Width';
import AdvancedAccordionHeader from '@/Pages/QuestionBuilder/Partials/Sidebar/AdvancedAccordionHeader';
import StyleCountAccordionHeader from '@/Pages/QuestionBuilder/Partials/Sidebar/StyleCountAccordionHeader';
import { useStoreState } from '@/store/hooks';
import { KeyValueGeneric } from '@/types/general';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';
import getStyles from '@/utils/EmbedForm/get-styles';

const MultiScreenAccordion = ({ settings }: { settings: KeyValueGeneric }) => {
    const showAdvancedStyles = useStoreState(state => state.questionBuilderModel.showAdvancedStyles);
    const styleCounts = {
        container: getStyles(settings?.pageBreak?.container?.style).length,
        headerContainer: getStyles(settings?.pageBreak?.headerContainer?.style).length,
        paging: getStyles(settings?.pageBreak?.paging?.style).length,
        title: getStyles(settings?.pageBreak?.title?.style).length,
    };

    return (
        <div className={cn({ hidden: !showAdvancedStyles })}>
            <AccordionItem value="multi-screen">
                <AccordionTrigger
                    className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                    iconPosition="left"
                >
                    <AdvancedAccordionHeader
                        header="Multi-Screen"
                        styleCount={sum(Object.values(styleCounts as KeyValueGeneric))}
                    />
                </AccordionTrigger>

                <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                    <Accordion
                        defaultValue={['multi-screen-container']}
                        type="multiple"
                        className={cn('w-full border border-b-0 border-gray-100 [&_h3]:mb-0')}
                    >
                        <AccordionItem value="multi-screen-container">
                            <AccordionTrigger
                                className="sticky top-[58px] z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader
                                    header="Container"
                                    styleCount={styleCounts?.container as number}
                                />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <Width namePath={['settings', 'pageBreak', 'container']} label="" />
                                <Margin namePath={['settings', 'pageBreak', 'container']} label="" />
                                <Padding namePath={['settings', 'pageBreak', 'container']} label="" />
                                <BackgroundColor namePath={['settings', 'pageBreak', 'container']} label="Background" />
                                <Border namePath={['settings', 'pageBreak', 'container']} label="Border" />
                                <BorderStyle namePath={['settings', 'pageBreak', 'container']} label="Border Style" />
                                <BorderColor namePath={['settings', 'pageBreak', 'container']} label="Border Color" />
                            </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value="multi-screen-title">
                            <AccordionTrigger
                                className="sticky top-[58px] z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader header="Title" styleCount={styleCounts?.title as number} />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <PlainText
                                    namePath={['settings', 'pageBreak', 'header']}
                                    label="Title"
                                    placeholder="Enter header title"
                                />
                                <FontSize namePath={['settings', 'pageBreak', 'title']} label="Font Size" />
                                <LineHeight namePath={['settings', 'pageBreak', 'title']} label="Line Height" />
                                <FontWeight namePath={['settings', 'pageBreak', 'title']} label="Font Weight" />
                                <FontColor namePath={['settings', 'pageBreak', 'title']} label="Font Color" />
                                <TextTransform namePath={['settings', 'pageBreak', 'title']} label="Title Transform" />
                            </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value="multi-screen-title-container">
                            <AccordionTrigger
                                className="sticky top-[58px] z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader
                                    header="Title Container"
                                    styleCount={styleCounts?.headerContainer as number}
                                />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <Width namePath={['settings', 'pageBreak', 'headerContainer']} label="" />
                                <Margin namePath={['settings', 'pageBreak', 'headerContainer']} label="" />
                                <Padding namePath={['settings', 'pageBreak', 'headerContainer']} label="" />
                                <Border namePath={['settings', 'pageBreak', 'headerContainer']} label="Border" />
                                <BorderStyle
                                    namePath={['settings', 'pageBreak', 'headerContainer']}
                                    label="Border Style"
                                />
                                <BorderColor
                                    namePath={['settings', 'pageBreak', 'headerContainer']}
                                    label="Border Color"
                                />
                            </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value="multi-screen-paging">
                            <AccordionTrigger
                                className="sticky top-[58px] z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader header="Paging" styleCount={styleCounts?.paging as number} />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <ShowPageNumber namePath={['settings', 'pageBreak']} label="Show Page Number" />
                                <PlainText
                                    namePath={['settings', 'pageBreak', 'paging', 'title']}
                                    label="Page Number Title"
                                    placeholder="Enter page number title"
                                />
                                <FontSize
                                    namePath={['settings', 'pageBreak', 'paging']}
                                    label="Page Number Font Size"
                                />
                                <LineHeight
                                    namePath={['settings', 'pageBreak', 'paging']}
                                    label="Page Number Line Height"
                                />
                                <FontWeight
                                    namePath={['settings', 'pageBreak', 'paging']}
                                    label="Page Number Font Weight"
                                />
                                <FontColor
                                    namePath={['settings', 'pageBreak', 'paging']}
                                    label="Page Number Font Color"
                                />
                                <TextTransform
                                    namePath={['settings', 'pageBreak', 'paging']}
                                    label="Paging Title Transform"
                                />
                            </AccordionContent>
                        </AccordionItem>
                    </Accordion>
                </AccordionContent>
            </AccordionItem>
        </div>
    );
};

export default MultiScreenAccordion;
