import { merge } from 'lodash';

import FontColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontColor';
import FontSize from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontSize';
import FontWeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontWeight';
import LineHeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/LineHeight';
import TextTransform from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/TextTransform';
import StyleCountAccordionHeader from '@/Pages/QuestionBuilder/Partials/Sidebar/StyleCountAccordionHeader';
import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';
import getStyles from '@/utils/EmbedForm/get-styles';

const LabelAccordion = ({ namePath, label, settings }: FormSettingFieldProps) => {
    const styleCounts = {
        label: getStyles(settings?.form?.label?.style).length,
    };

    return (
        <AccordionItem value="label-styles">
            <AccordionTrigger
                className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold [&_h3]:mt-0"
                iconPosition="left"
            >
                <StyleCountAccordionHeader header="Label Styles" styleCount={styleCounts?.label as number} />
            </AccordionTrigger>

            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                <FontSize namePath={merge([], namePath)} label={`${label} Font Size`} />
                <LineHeight namePath={merge([], namePath)} label={`${label} Line Height`} />
                <FontWeight namePath={merge([], namePath)} label={`${label} Font Weight`} />
                <FontColor namePath={merge([], namePath)} label={`${label} Font Color`} />
                <TextTransform namePath={merge([], [...namePath])} label="Text Transform" />
            </AccordionContent>
        </AccordionItem>
    );
};

export default LabelAccordion;
