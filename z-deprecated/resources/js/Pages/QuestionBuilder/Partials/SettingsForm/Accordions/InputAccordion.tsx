import { merge, sum } from 'lodash';

import { cn } from '@/lib/utils';
import BackgroundColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BackgroundColor';
import Border from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Border';
import BorderColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BorderColor';
import BorderRadius from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BorderRadius';
import BorderStyle from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BorderStyle';
import FontColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontColor';
import FontSize from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontSize';
import FontWeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontWeight';
import Height from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Height';
import LineHeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/LineHeight';
import Margin from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Margin';
import Padding from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Padding';
import TextTransform from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/TextTransform';
import Width from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Width';
import AdvancedAccordionHeader from '@/Pages/QuestionBuilder/Partials/Sidebar/AdvancedAccordionHeader';
import StyleCountAccordionHeader from '@/Pages/QuestionBuilder/Partials/Sidebar/StyleCountAccordionHeader';
import { useStoreState } from '@/store/hooks';
import { KeyValueGeneric } from '@/types/general';
import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';
import getStyles from '@/utils/EmbedForm/get-styles';

const InputGroupAccordion = ({
    namePath,
    settings,
}: {
    namePath: FormSettingFieldProps['namePath'];
    settings: KeyValueGeneric;
}) => {
    const showAdvancedStyles = useStoreState(state => state.questionBuilderModel.showAdvancedStyles);
    const styleCounts = {
        addon: getStyles(settings?.input?.addon?.style).length,
        container: getStyles(settings?.input?.container?.style).length,
        control: getStyles(settings?.input?.control?.style).length,
    };

    return (
        <div className={cn({ hidden: !showAdvancedStyles })}>
            <AccordionItem value="input-styles">
                <AccordionTrigger
                    className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                    iconPosition="left"
                >
                    <AdvancedAccordionHeader
                        header="Input Styles"
                        styleCount={sum(Object.values(styleCounts as KeyValueGeneric))}
                    />
                </AccordionTrigger>

                <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                    <Accordion
                        defaultValue={['input-wrapper']}
                        type="multiple"
                        className={cn('w-full border border-b-0 border-gray-100 [&_h3]:mb-0')}
                    >
                        <AccordionItem value="input-wrapper">
                            <AccordionTrigger
                                className="sticky top-[58px] z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader
                                    header="Input Wrapper"
                                    styleCount={styleCounts?.container as number}
                                />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <Width namePath={merge([], [...namePath, 'container'])} label="Wrapper" />
                                <Height namePath={merge([], [...namePath, 'container'])} label="Wrapper" />
                                <Margin namePath={merge([], [...namePath, 'container'])} label="Wrapper" />
                                <Padding namePath={merge([], [...namePath, 'container'])} label="Wrapper" />
                            </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value="input-field">
                            <AccordionTrigger
                                className="sticky top-[58px] z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader
                                    header="Field"
                                    styleCount={styleCounts?.container as number}
                                />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <Padding namePath={merge([], [...namePath, 'control'])} label="" />
                                <BorderRadius namePath={merge([], [...namePath, 'control'])} label="Border Radius" />
                                <Border namePath={merge([], [...namePath, 'control'])} label="Border" />
                                <BorderStyle namePath={merge([], [...namePath, 'control'])} label="Border Style" />
                                <BorderColor namePath={merge([], [...namePath, 'control'])} label="Border Color" />
                                <BackgroundColor
                                    namePath={merge([], [...namePath, 'control'])}
                                    label="Background Color"
                                />
                                <FontSize namePath={merge([], [...namePath, 'control'])} label="Font Size" />
                                <LineHeight namePath={merge([], [...namePath, 'control'])} label="Line Height" />
                                <FontWeight namePath={merge([], [...namePath, 'control'])} label="Font Weight" />
                                <TextTransform namePath={merge([], [...namePath, 'control'])} label="Font Transform" />
                                <FontColor namePath={merge([], [...namePath, 'control'])} label="Font Color" />
                            </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value="input-field">
                            <AccordionTrigger
                                className="sticky top-[58px] z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader
                                    header="Input Add On"
                                    styleCount={styleCounts?.addon as number}
                                />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <BorderRadius namePath={merge([], [...namePath, 'addon'])} label="Border Radius" />
                                <Border namePath={merge([], [...namePath, 'addon'])} label="Border" />
                                <BorderStyle namePath={merge([], [...namePath, 'addon'])} label="Border Style" />
                                <BorderColor namePath={merge([], [...namePath, 'addon'])} label="Border Color" />
                                <BackgroundColor
                                    namePath={merge([], [...namePath, 'addon'])}
                                    label="Background Color"
                                />
                                <FontSize namePath={merge([], [...namePath, 'addon'])} label="Font Size" />
                                <LineHeight namePath={merge([], [...namePath, 'addon'])} label="Line Height" />
                                <FontWeight namePath={merge([], [...namePath, 'addon'])} label="Font Weight" />
                                <TextTransform namePath={merge([], [...namePath, 'addon'])} label="Font Transform" />
                                <FontColor namePath={merge([], [...namePath, 'addon'])} label="Font Color" />
                            </AccordionContent>
                        </AccordionItem>
                    </Accordion>
                </AccordionContent>
            </AccordionItem>
        </div>
    );
};

export default InputGroupAccordion;
