import { merge, sum } from 'lodash';

import { cn } from '@/lib/utils';
import BackgroundColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BackgroundColor';
import Border from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Border';
import BorderColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BorderColor';
import BorderRadius from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BorderRadius';
import BorderStyle from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BorderStyle';
import Height from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Height';
import Margin from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Margin';
import Padding from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Padding';
import Width from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Width';
import AdvancedAccordionHeader from '@/Pages/QuestionBuilder/Partials/Sidebar/AdvancedAccordionHeader';
import StyleCountAccordionHeader from '@/Pages/QuestionBuilder/Partials/Sidebar/StyleCountAccordionHeader';
import { useStoreState } from '@/store/hooks';
import { KeyValueGeneric } from '@/types/general';
import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';
import getStyles from '@/utils/EmbedForm/get-styles';

const FormItemAccordion = ({
    namePath,
    settings,
}: {
    namePath: FormSettingFieldProps['namePath'];
    settings: KeyValueGeneric;
}) => {
    const showAdvancedStyles = useStoreState(state => state.questionBuilderModel.showAdvancedStyles);
    const styleCounts = {
        container: getStyles(settings?.formItem?.container?.style).length,
        control: getStyles(settings?.formItem?.control?.style).length,
        label: getStyles(settings?.formItem?.label?.style).length,
        row: getStyles(settings?.formItem?.row?.style).length,
    };

    return (
        <div className={cn({ hidden: !showAdvancedStyles })}>
            <AccordionItem value="form-item-styles">
                <AccordionTrigger
                    className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                    iconPosition="left"
                >
                    <AdvancedAccordionHeader
                        header="Form Item Styles"
                        styleCount={sum(Object.values(styleCounts as KeyValueGeneric))}
                    />
                </AccordionTrigger>

                <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                    <Accordion
                        defaultValue={['form-item-wrapper']}
                        type="multiple"
                        className={cn('w-full border border-b-0 border-gray-100 [&_h3]:mb-0')}
                    >
                        <AccordionItem value="form-item-wrapper">
                            <AccordionTrigger
                                className="sticky top-[58px] z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader
                                    header="Wrapper"
                                    styleCount={styleCounts?.container as number}
                                />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <Width namePath={merge([], [...namePath, 'container'])} label="" />
                                <Height namePath={merge([], [...namePath, 'container'])} label="" />
                                <Margin namePath={merge([], [...namePath, 'container'])} label="" />
                                <Padding namePath={merge([], [...namePath, 'container'])} label="" />
                                <BorderRadius namePath={merge([], [...namePath, 'container'])} label="Border Radius" />
                                <Border namePath={merge([], [...namePath, 'container'])} label="Border" />
                                <BorderStyle namePath={merge([], [...namePath, 'container'])} label="Border Style" />
                                <BorderColor namePath={merge([], [...namePath, 'container'])} label="Border Color" />
                                <BackgroundColor
                                    namePath={merge([], [...namePath, 'container'])}
                                    label="Background Color"
                                />
                            </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value="form-item-row">
                            <AccordionTrigger
                                className="sticky top-[58px] z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader header="Row" styleCount={styleCounts?.row as number} />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <Width namePath={merge([], [...namePath, 'row'])} label="" />
                                <Height namePath={merge([], [...namePath, 'row'])} label="" />
                                <Margin namePath={merge([], [...namePath, 'row'])} label="" />
                                <Padding namePath={merge([], [...namePath, 'row'])} label="" />
                                <BorderRadius namePath={merge([], [...namePath, 'row'])} label="Border Radius" />
                                <Border namePath={merge([], [...namePath, 'row'])} label="Border" />
                                <BorderStyle namePath={merge([], [...namePath, 'row'])} label="Border Style" />
                                <BorderColor namePath={merge([], [...namePath, 'row'])} label="Border Color" />
                                <BackgroundColor namePath={merge([], [...namePath, 'row'])} label="Background Color" />
                            </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value="form-item-label">
                            <AccordionTrigger
                                className="sticky top-[58px] z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader header="Label" styleCount={styleCounts?.label as number} />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <Width namePath={merge([], [...namePath, 'label'])} label="" />
                                <Height namePath={merge([], [...namePath, 'label'])} label="" />
                                <Margin namePath={merge([], [...namePath, 'label'])} label="" />
                                <Padding namePath={merge([], [...namePath, 'label'])} label="" />
                                <BorderRadius namePath={merge([], [...namePath, 'label'])} label="Border Radius" />
                                <Border namePath={merge([], [...namePath, 'label'])} label="Border" />
                                <BorderStyle namePath={merge([], [...namePath, 'label'])} label="Border Style" />
                                <BorderColor namePath={merge([], [...namePath, 'label'])} label="Border Color" />
                                <BackgroundColor
                                    namePath={merge([], [...namePath, 'label'])}
                                    label="Background Color"
                                />
                            </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value="form-item-container">
                            <AccordionTrigger
                                className="sticky top-[58px] z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader
                                    header="Container"
                                    styleCount={styleCounts?.control as number}
                                />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <Width namePath={merge([], [...namePath, 'control'])} label="" />
                                <Height namePath={merge([], [...namePath, 'control'])} label="" />
                                <Margin namePath={merge([], [...namePath, 'control'])} label="" />
                                <Padding namePath={merge([], [...namePath, 'control'])} label="" />
                                <BorderRadius namePath={merge([], [...namePath, 'control'])} label="Border Radius" />
                                <Border namePath={merge([], [...namePath, 'control'])} label="Border" />
                                <BorderStyle namePath={merge([], [...namePath, 'control'])} label="Border Style" />
                                <BorderColor namePath={merge([], [...namePath, 'control'])} label="Border Color" />
                                <BackgroundColor
                                    namePath={merge([], [...namePath, 'control'])}
                                    label="Background Color"
                                />
                            </AccordionContent>
                        </AccordionItem>
                    </Accordion>
                </AccordionContent>
            </AccordionItem>
        </div>
    );
};

export default FormItemAccordion;
