import { Form, Input } from 'antd';
import { Rule, RuleObject, StoreValue } from 'rc-field-form/lib/interface';

import { usePage } from '@inertiajs/react';

const ThankYouUrlDisqualified = () => {
    const { entity } = usePage<any>().props;
    const httpsUrlValidator = async (_: RuleObject, value: StoreValue) => {
        if (!value) {
            return;
        }
        const urlPattern = /^(https:\/\/)([\w-]+\.)+[\w-]+(\/[\w-./?=&]*)*\/?$/;

        if (!urlPattern.test(value)) {
            throw new Error('Please enter a valid URL starting with https://');
        }
    };

    const thankYouUrlRules: Rule[] = [{ validator: httpsUrlValidator }];

    if (!entity?.showInternal) {
        thankYouUrlRules.push({ required: true, message: 'Please enter thank you disqualified URL' });
    }

    return (
        <>
            <Form.Item
                name="thankYouUrlDisqualifiedValue"
                label="Default Disqualified URL"
                rules={thankYouUrlRules}
                validateFirst
            >
                <Input placeholder="Disqualified URL (e.g., https://example.com)" size="small" />
            </Form.Item>

            <Form.Item name="thankYouUrlDisqualified" hidden>
                <Input />
            </Form.Item>
        </>
    );
};

export default ThankYouUrlDisqualified;
