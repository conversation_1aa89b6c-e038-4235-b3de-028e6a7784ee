import { useEffect, useState } from 'react';

import { Form } from 'antd';

import { LanguageService } from '@/Components/Languages/services/languageService';
import { LanguageCode } from '@/Components/Languages/types/languages';
import ConfirmDialog from '@/Components/Modals/ConfirmDialog';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import { useModalContext } from '@/Contexts/ModalContext';
import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import { useStoreActions, useStoreState } from '@/store/hooks';
import { ModifiedQuestion } from '@/types/question';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import { SingleComboSelectOption } from '@/UI-Kit/Shadcn/types/combobox-select';
import buildTree from '@/utils/QuestionBuilder/build-tree';
import convertQuestionTreeToProps from '@/utils/QuestionBuilder/convert-question-tree-to-props';
import flattenTree from '@/utils/QuestionBuilder/flatten-tree';

const SelectDisplayLanguage = ({ noStyle = false }: { noStyle?: boolean }) => {
    const { questionBuilderForm, primaryForm } = useQuestionBuilderFormContext();
    const isFormDirty = useStoreState(state => state.appSettingsModel.isFormDirty);
    const languageCode = useStoreState(state => state.questionBuilderModel.languageCode);
    const editIndex = useStoreState(state => state.questionBuilderModel.editIndex);
    const setLanguageCode = useStoreActions(actions => actions.questionBuilderModel.setLanguageCode);
    const { setShowModal, setModalComponent } = useModalContext();
    const [selectedLanguage, setSelectedLanguage] = useState<SingleComboSelectOption>();

    useEffect(() => {
        const selectedLang = LanguageService.getLanguageObject(languageCode as LanguageCode);
        setSelectedLanguage(selectedLang ? { label: selectedLang.label, value: selectedLang.value } : undefined);
    }, [languageCode]);

    const updateLanguageText = (language: LanguageCode): void => {
        if (questionBuilderForm) {
            const cloneFlattenedQuestions: ModifiedQuestion[] = questionBuilderForm.getFieldsValue().questions ?? [];

            if (cloneFlattenedQuestions.length > 0) {
                const newFlattenedQuestions = buildTree([...cloneFlattenedQuestions]).map(
                    (question: ModifiedQuestion) => convertQuestionTreeToProps(question, language)
                );

                questionBuilderForm.setFieldsValue({ questions: [...flattenTree(newFlattenedQuestions)] });
            }
        }

        if (primaryForm) {
            const { thankYouUrlQualified, thankYouUrlDisqualified } = primaryForm.getFieldsValue();
            const thankYouUrlQualifiedValue = thankYouUrlQualified?.[language] ?? thankYouUrlQualified?.en ?? '';
            const thankYouUrlDisqualifiedValue =
                thankYouUrlDisqualified?.[language] ?? thankYouUrlDisqualified?.en ?? '';

            primaryForm.setFieldsValue({ thankYouUrlQualifiedValue, thankYouUrlDisqualifiedValue });
        }
    };

    const handleSelectLanguage = (data: SingleComboSelectOption | null): void => {
        if (isFormDirty && selectedLanguage) {
            setModalComponent(
                <ModalContentWrapper>
                    <ConfirmDialog
                        cancelText="Go Back"
                        color="amber"
                        content={
                            <span>
                                You have unsaved changes for the <strong>{selectedLanguage.label}</strong> language. To
                                switch to <strong>{data?.label}</strong>, please save your{' '}
                                <strong>{selectedLanguage.label}</strong> changes first.
                            </span>
                        }
                        dialogType="confirm"
                        endpoint={null}
                        title="Please Save Your Changes"
                        okText="Ok"
                    />
                </ModalContentWrapper>
            );
            setShowModal(true);

            primaryForm?.setFields([{ name: ['doNotSubmit', 'selectLanguage'], value: selectedLanguage }]);
        } else if (data && data.value) {
            setLanguageCode(data.value);
            updateLanguageText(data.value as LanguageCode);
        }
    };

    return (
        <div className="">
            <Form.Item name={['doNotSubmit', 'selectLanguage']} label="Show Language" noStyle={noStyle}>
                <ComboSelect
                    isClearable={false}
                    options={LanguageService.getLanguageList()}
                    isDisabled={editIndex !== null}
                    onChange={value => {
                        handleSelectLanguage((value as SingleComboSelectOption) || null);
                    }}
                    isCompact
                />
            </Form.Item>
        </div>
    );
};

export default SelectDisplayLanguage;
