import { FormInstance } from 'antd';
import { Copy, Ellipsis } from 'lucide-react';

import ConfirmDialog from '@/Components/Modals/ConfirmDialog';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import { useModalContext } from '@/Contexts/ModalContext';
import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import useCopQuestionBuilderToCampaignForm from '@/hooks/useCopyQuestionBuilderToCampaignForm';
import CopyQuestionBuilderToAnotherCampaignForm from '@/Pages/QuestionBuilder/Partials/CopyQuestionBuilderToAnotherCampaignForm';
import { Button } from '@/UI-Kit/Shadcn/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/UI-Kit/Shadcn/dropdown-menu';

const SavePrimaryFormButton = () => {
    const { setShowModal, setModalComponent } = useModalContext();
    const { primaryForm } = useQuestionBuilderFormContext() as {
        primaryForm: FormInstance;
    };

    const { copQuestionBuilderToCampaignForm } = useCopQuestionBuilderToCampaignForm();

    return (
        <div className="inline-flex gap-x-2 rounded-md shadow-xs">
            <Button
                key="save-primary-form"
                data-ondata="save-primary-form"
                type="button"
                size="sm"
                variant="success"
                onClick={() => primaryForm.submit()}
            >
                Save Questions
            </Button>
            <DropdownMenu modal={false}>
                <DropdownMenuTrigger asChild>
                    <Button
                        type="button"
                        variant="ghost"
                        // className="h-[30px] items-center justify-center rounded-l-none! px-2 focus:ring-0 focus:ring-offset-0"
                        size="icon"
                    >
                        <Ellipsis />
                    </Button>
                </DropdownMenuTrigger>

                <DropdownMenuContent align="end">
                    <DropdownMenuItem
                        onClick={() => {
                            setModalComponent(
                                <ModalContentWrapper size="lg">
                                    <ConfirmDialog
                                        content={<CopyQuestionBuilderToAnotherCampaignForm />}
                                        dialogType="form"
                                        endpoint=""
                                        formProps={{
                                            onFinish: formData => copQuestionBuilderToCampaignForm(formData),
                                        }}
                                        iconProps={{ name: 'Copy' }}
                                        okText="Copy Campaign Form"
                                        title="Copy to Another Campaign Form"
                                    />
                                </ModalContentWrapper>
                            );
                            setShowModal(true);
                        }}
                    >
                        <Copy className="h-4 w-4!" />
                        Copy to Another Form
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    );
};

export default SavePrimaryFormButton;
