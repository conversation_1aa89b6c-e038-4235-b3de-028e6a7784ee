import { Form } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import borderStyles from '@/utils/QuestionBuilder/styles/border-styles';

const BorderStyle = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'style', 'borderStyle'])} label={label}>
        <ComboSelect options={[...borderStyles]} isCompact />
    </Form.Item>
);

export default BorderStyle;
