import { Form } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import { iconPositions } from '@/utils/QuestionBuilder/styles/button-props';

const IconPosition = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'buttonProps', 'iconPosition'])} label={label}>
        <ComboSelect options={[...iconPositions]} isCompact />
    </Form.Item>
);

export default IconPosition;
