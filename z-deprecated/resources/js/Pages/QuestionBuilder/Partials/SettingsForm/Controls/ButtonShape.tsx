import { Form } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import { buttonShapes } from '@/utils/QuestionBuilder/styles/button-props';

const ButtonShapes = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'buttonProps', 'buttonShape'])} label={label}>
        <ComboSelect options={[...buttonShapes]} isCompact />
    </Form.Item>
);

export default ButtonShapes;
