import { Form } from 'antd';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import googleFonts from '@/utils/QuestionBuilder/styles/google-fonts';

// TODO: Implement this after deployment - not needed for Fractyl
const GoogleFont = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={[...namePath, 'googleFont']} label={label}>
        <ComboSelect
            isClearable={false}
            options={googleFonts.map(font => ({
                value: font.name,
                label: font.name,
            }))}
            isCompact
        />
    </Form.Item>
);

export default GoogleFont;
