import { Form } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import fontSizes from '@/utils/QuestionBuilder/styles/font-sizes';

const FontSize = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'style', 'font-size'])} label={label}>
        <ComboSelect
            options={fontSizes.map(size => ({
                value: `${size}px`,
                label: `${size}px`,
            }))}
            isCompact
        />
    </Form.Item>
);

export default FontSize;
