import { Form } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';

const DisplayFlex = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'style', 'displayFlex'])} label={label}>
        <ComboSelect
            options={[
                {
                    value: 'display: flex; flex-wrap: wrap',
                    label: 'Side By Side',
                },
                {
                    value: 'display: flex; flex-direction: column',
                    label: 'One Per Row',
                },
            ]}
            isCompact
        />
    </Form.Item>
);

export default DisplayFlex;
