import { Form } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import { buttonTypes } from '@/utils/QuestionBuilder/styles/button-props';

const ButtonTypes = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'buttonProps', 'buttonType'])} label={label}>
        <ComboSelect options={[...buttonTypes]} isCompact />
    </Form.Item>
);

export default ButtonTypes;
