import { Form } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import fontWeights from '@/utils/QuestionBuilder/styles/font-weights';

const BorderStyle = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'style', 'fontWeight'])} label={label}>
        <ComboSelect options={[...fontWeights]} isCompact />
    </Form.Item>
);

export default BorderStyle;
