import { Form } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';

const ShowPageNumber = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'showPaging'])} label={label}>
        <ComboSelect
            options={[
                {
                    value: '1',
                    label: 'Yes',
                },
                {
                    value: '0',
                    label: 'No',
                },
            ]}
            isClearable
            isCompact
        />
    </Form.Item>
);

export default ShowPageNumber;
