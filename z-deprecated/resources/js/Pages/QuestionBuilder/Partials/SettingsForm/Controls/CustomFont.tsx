import { Form, Input } from 'antd';

import { FormSettingFieldProps } from '@/types/question-builder-settings';

const CustomFont = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item
        name={[...namePath, 'customFont', 'link']}
        label={label}
        rules={[{ type: 'url', message: 'Please enter a url' }]}
    >
        <Input placeholder="Link to custom font" size="small" allowClear />
    </Form.Item>
);

export default CustomFont;
