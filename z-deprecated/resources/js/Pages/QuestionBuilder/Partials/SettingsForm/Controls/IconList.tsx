import { Form } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import { getIconOptions } from '@/utils/dynamic-fontawesome-icons';

const IconList = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'icon'])} label={label}>
        <ComboSelect options={[...getIconOptions()]} isCompact />
    </Form.Item>
);

export default IconList;
