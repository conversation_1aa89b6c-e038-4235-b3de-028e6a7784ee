import { Form } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import { blockTypes } from '@/utils/QuestionBuilder/styles/button-props';

const ButtonBlockType = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'buttonProps', 'blockType'])} label={label}>
        <ComboSelect options={[...blockTypes]} isCompact />
    </Form.Item>
);

export default ButtonBlockType;
