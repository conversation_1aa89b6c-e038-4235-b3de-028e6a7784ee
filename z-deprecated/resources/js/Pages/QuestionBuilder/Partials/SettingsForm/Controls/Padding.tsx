import { Form, Input, Space } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';

const Padding = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item label={`${label ? `${label} ` : label}Padding (top / right / bottom / left)`}>
        <Space.Compact block>
            <Form.Item name={merge([], [...namePath, 'style', 'padding-top'])} noStyle>
                <Input placeholder="top" />
            </Form.Item>
            <Form.Item name={merge([], [...namePath, 'style', 'padding-right'])} noStyle>
                <Input placeholder="right" />
            </Form.Item>
            <Form.Item name={merge([], [...namePath, 'style', 'padding-bottom'])} noStyle>
                <Input placeholder="bottom" />
            </Form.Item>
            <Form.Item name={merge([], [...namePath, 'style', 'padding-left'])} noStyle>
                <Input placeholder="left" />
            </Form.Item>
        </Space.Compact>
    </Form.Item>
);

export default Padding;
