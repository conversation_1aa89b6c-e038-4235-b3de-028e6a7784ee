import { useCallback, useEffect, useState } from 'react';

import { Form, FormInstance } from 'antd';
import { debounce, merge } from 'lodash';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import { ComboSelectOption } from '@/UI-Kit/Shadcn/types/combobox-select';
import AcceptTermsOptionTypes from '@/utils/QuestionBuilder/styles/accept-terms-choice-option-types';
import MultipleChoiceOptionTypes from '@/utils/QuestionBuilder/styles/multiple-choice-option-types';
import SingleChoiceOptionTypes from '@/utils/QuestionBuilder/styles/single-choice-option-types';

const OptionsType = ({ index, questionTypeName }: { index: number; questionTypeName: string }) => {
    const namePathMap: Record<string, string> = {
        oneof: 'radio',
        oneofsite: 'radio',
        anyof: 'checkbox',
        acceptTerms: 'userAgreement',
    };

    const namePath = ['settings', namePathMap[questionTypeName] ?? 'radio', 'optionsType'];
    const { questionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
    };
    const [optionsType, setOptionsType] = useState([{}]);

    useEffect(() => {
        if (questionTypeName === 'oneof' || questionTypeName === 'oneofsite') {
            setOptionsType(SingleChoiceOptionTypes);
        } else if (questionTypeName === 'anyof') {
            setOptionsType(MultipleChoiceOptionTypes);
        } else if (questionTypeName === 'acceptTerms') {
            setOptionsType(AcceptTermsOptionTypes);
        }
    }, [questionTypeName]);

    const onInputTypeChange = useCallback(
        debounce(data => {
            questionBuilderForm.setFieldValue(['questions', index, ...namePath], data);
        }, 100),
        [index]
    );

    return (
        <Form.Item
            name={merge([], [...namePath])}
            label={<span className="text-xs uppercase tracking-tight">Options Type</span>}
        >
            <ComboSelect
                isClearable={false}
                options={[...optionsType] as ComboSelectOption[]}
                onChange={data => onInputTypeChange(data)}
                isCompact
            />
        </Form.Item>
    );
};

export default OptionsType;
