import { Form, Input } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';

const FontFamily = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'style', 'font-family'])} label={label}>
        <Input placeholder="Name of custom font" size="small" allowClear />
    </Form.Item>
);

export default FontFamily;
