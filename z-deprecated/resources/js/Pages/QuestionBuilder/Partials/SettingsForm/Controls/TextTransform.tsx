import { Form } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import textTransform from '@/utils/QuestionBuilder/styles/text-transform';

const TextTransform = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'style', 'textTransform'])} label={label}>
        <ComboSelect options={[...textTransform]} isCompact />
    </Form.Item>
);

export default TextTransform;
