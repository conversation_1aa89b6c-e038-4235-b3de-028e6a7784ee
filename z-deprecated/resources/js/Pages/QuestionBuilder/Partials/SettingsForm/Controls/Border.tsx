import { Form } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import borders from '@/utils/QuestionBuilder/styles/borders';

const Border = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'style', 'border'])} label={label}>
        <ComboSelect options={[...borders()]} isCompact />
    </Form.Item>
);

export default Border;
