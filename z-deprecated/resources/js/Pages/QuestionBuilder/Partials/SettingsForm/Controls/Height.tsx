import { Form, Input, Space } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';

const Height = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item label={`${label ? `${label} ` : label}Height (height / min / max)`}>
        <Space.Compact block>
            <Form.Item name={merge([], [...namePath, 'style', 'height'])} noStyle>
                <Input placeholder="height" />
            </Form.Item>
            <Form.Item name={merge([], [...namePath, 'style', 'min-height'])} noStyle>
                <Input placeholder="min height" />
            </Form.Item>
            <Form.Item name={merge([], [...namePath, 'style', 'max-height'])} noStyle>
                <Input placeholder="max height" />
            </Form.Item>
        </Space.Compact>
    </Form.Item>
);

export default Height;
