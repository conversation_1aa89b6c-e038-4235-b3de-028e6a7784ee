import { Form, Input } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';

const FontColor = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item
        name={merge([], [...namePath, 'style', 'color'])}
        label={label}
        rules={[
            {
                type: 'string',
                message: 'Please enter a valid color',
                len: 7,
                pattern: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
            },
        ]}
    >
        <Input placeholder="#000000" size="small" allowClear />
    </Form.Item>
);

export default FontColor;
