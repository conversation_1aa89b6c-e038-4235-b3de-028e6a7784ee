import { Form } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import borderRadius from '@/utils/QuestionBuilder/styles/border-radius';

const BorderRadius = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'style', 'borderRadius'])} label={label}>
        <ComboSelect options={[...borderRadius]} isCompact />
    </Form.Item>
);

export default BorderRadius;
