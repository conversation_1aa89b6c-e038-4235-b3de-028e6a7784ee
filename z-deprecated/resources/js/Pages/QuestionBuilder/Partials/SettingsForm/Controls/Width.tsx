import { Form, Input, Space } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';

const Width = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item label={`${label ? `${label} ` : label}Width (width / min / max)`}>
        <Space.Compact block>
            <Form.Item name={merge([], [...namePath, 'style', 'width'])} noStyle>
                <Input placeholder="width" />
            </Form.Item>
            <Form.Item name={merge([], [...namePath, 'style', 'min-width'])} noStyle>
                <Input placeholder="min width" />
            </Form.Item>
            <Form.Item name={merge([], [...namePath, 'style', 'max-width'])} noStyle>
                <Input placeholder="max width" />
            </Form.Item>
        </Space.Compact>
    </Form.Item>
);

export default Width;
