import { Form, Input, Space } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';

const Margin = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item label={`${label ? `${label} ` : label}Margin (top / right / bottom / left)`}>
        <Space.Compact block>
            <Form.Item name={merge([], [...namePath, 'style', 'margin-top'])} noStyle>
                <Input placeholder="top" />
            </Form.Item>
            <Form.Item name={merge([], [...namePath, 'style', 'margin-right'])} noStyle>
                <Input placeholder="right" />
            </Form.Item>
            <Form.Item name={merge([], [...namePath, 'style', 'margin-bottom'])} noStyle>
                <Input placeholder="bottom" />
            </Form.Item>
            <Form.Item name={merge([], [...namePath, 'style', 'margin-left'])} noStyle>
                <Input placeholder="left" />
            </Form.Item>
        </Space.Compact>
    </Form.Item>
);

export default Margin;
