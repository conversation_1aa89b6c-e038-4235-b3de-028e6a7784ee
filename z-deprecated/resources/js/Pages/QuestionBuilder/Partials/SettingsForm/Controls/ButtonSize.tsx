import { Form } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import { buttonSizes } from '@/utils/QuestionBuilder/styles/button-props';

const ButtonSizes = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'buttonProps', 'size'])} label={label}>
        <ComboSelect options={[...buttonSizes]} isCompact />
    </Form.Item>
);

export default ButtonSizes;
