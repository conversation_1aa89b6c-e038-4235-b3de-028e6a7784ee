import { Form } from 'antd';
import { merge } from 'lodash';

import { FormSettingFieldProps } from '@/types/question-builder-settings';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import textDecoration from '@/utils/QuestionBuilder/styles/text-decoration';

const TextDecoration = ({ namePath, label }: FormSettingFieldProps) => (
    <Form.Item name={merge([], [...namePath, 'style', 'textDecoration'])} label={label}>
        <ComboSelect options={[...textDecoration]} isCompact />
    </Form.Item>
);

export default TextDecoration;
