import { Form, Input } from 'antd';
import { merge } from 'lodash';
import { Rule } from 'rc-field-form/lib/interface';

import { FormSettingFieldProps } from '@/types/question-builder-settings';

const PlainText = ({ namePath, label, isRequired = false, onChange, placeholder }: FormSettingFieldProps) => {
    const rules: Rule[] = [];

    if (isRequired) {
        rules.push({ required: true, message: 'Please enter a value' });
    }

    return (
        <Form.Item name={merge([], [...namePath])} label={label} rules={rules}>
            <Input placeholder={placeholder} onChange={onChange} size="small" allowClear={rules.length === 0} />
        </Form.Item>
    );
};

export default PlainText;
