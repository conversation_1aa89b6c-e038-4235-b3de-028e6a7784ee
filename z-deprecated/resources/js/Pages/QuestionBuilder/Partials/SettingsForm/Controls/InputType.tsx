import { useCallback } from 'react';

import { Form, FormInstance } from 'antd';
import { debounce, merge } from 'lodash';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import inputTypes from '@/utils/QuestionBuilder/styles/input-types';

const InputType = ({ index }: { index: number }) => {
    const namePath = ['settings', 'input', 'type'];
    const { questionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
    };

    const onInputTypeChange = useCallback(
        debounce(data => {
            questionBuilderForm.setFieldValue(['questions', index, ...namePath], data);
        }, 100),
        [index]
    );

    return (
        <Form.Item name={merge([], [...namePath])} label="Input Type">
            <ComboSelect
                isClearable={false}
                options={[...inputTypes]}
                onChange={data => onInputTypeChange(data)}
                isCompact
            />
        </Form.Item>
    );
};

export default InputType;
