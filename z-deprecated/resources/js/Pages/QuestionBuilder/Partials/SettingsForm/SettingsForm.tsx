import { useMemo } from 'react';

import { Form, FormInstance, Input } from 'antd';

import { usePage } from '@inertiajs/react';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import { cn } from '@/lib/utils';
import CalendlyEventType from '@/Pages/QuestionBuilder/Partials/SettingsForm/CalendlyEventType';
import SavePrimaryFormButton from '@/Pages/QuestionBuilder/Partials/SettingsForm/SavePrimaryFormButton';
import SelectDisplayLanguage from '@/Pages/QuestionBuilder/Partials/SettingsForm/SelectDisplayLanguage';
import GeneralCheckboxGroupStyles from '@/Pages/QuestionBuilder/Partials/SettingsForm/StyleGroups/GeneralCheckboxGroupStyles';
import GeneralFormItemStyles from '@/Pages/QuestionBuilder/Partials/SettingsForm/StyleGroups/GeneralFormItemStyles';
import GeneralInputStyles from '@/Pages/QuestionBuilder/Partials/SettingsForm/StyleGroups/GeneralInputStyles';
import GeneralLabelStyles from '@/Pages/QuestionBuilder/Partials/SettingsForm/StyleGroups/GeneralLabelStyles';
import GeneralLinkStyles from '@/Pages/QuestionBuilder/Partials/SettingsForm/StyleGroups/GeneralLinkStyles';
import GeneralMultiScreenStyles from '@/Pages/QuestionBuilder/Partials/SettingsForm/StyleGroups/GeneralMultiScreenStyles';
import GeneralNextButtonStyles from '@/Pages/QuestionBuilder/Partials/SettingsForm/StyleGroups/GeneralNextButtonStyles';
import GeneralRadioGroupStyles from '@/Pages/QuestionBuilder/Partials/SettingsForm/StyleGroups/GeneralRadioGroupStyles';
import GeneralStyles from '@/Pages/QuestionBuilder/Partials/SettingsForm/StyleGroups/GeneralStyles';
import GeneralSubmitButtonStyles from '@/Pages/QuestionBuilder/Partials/SettingsForm/StyleGroups/GeneralSubmitButtonStyles';
import ThankYouUrlDisqualified from '@/Pages/QuestionBuilder/Partials/SettingsForm/ThankYouUrlDisqualified';
import ThankYouUrlQualified from '@/Pages/QuestionBuilder/Partials/SettingsForm/ThankYouUrlQualified';
import CardWrapper from '@/Pages/QuestionBuilder/Partials/Sidebar/CardWrapper';
import FormSettingsTitle from '@/Pages/QuestionBuilder/Partials/Sidebar/FormSettingsTitle';
import TransitionWrapper from '@/Pages/QuestionBuilder/Partials/Sidebar/TransitionWrapper';
import { useStoreActions, useStoreState } from '@/store/hooks';
import { PageProps } from '@/types/general';
import { QuestionBuilderPageProps } from '@/types/question-builder';
import Alert from '@/UI-Kit/Alert';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';

const SettingsForm = () => {
    const { entity } = usePage<PageProps<QuestionBuilderPageProps>>().props;
    const { calendarEngineIntegrationEnabled, studyUUID } = entity;
    const { initialFormValues } = useQuestionBuilderFormContext();
    const editIndex = useStoreState(state => state.questionBuilderModel.editIndex) as number;
    const isFormDirty = useStoreState(state => state.appSettingsModel.isFormDirty);
    const { addAfterQuestion } = useStoreState(state => state.questionBuilderModel.addQuestionSettings);
    const setIsFormDirty = useStoreActions(actions => actions.appSettingsModel.setIsFormDirty);
    const showSidebar = useMemo(
        () => editIndex === null && addAfterQuestion === null,
        [editIndex, addAfterQuestion?.id]
    );
    const { primaryForm } = useQuestionBuilderFormContext() as {
        primaryForm: FormInstance;
    };

    return (
        <TransitionWrapper showSidebar={showSidebar}>
            <div>
                {initialFormValues ? (
                    <Form
                        key="primary-form-builder"
                        name="primaryFormBuilder"
                        layout="vertical"
                        form={primaryForm}
                        initialValues={{ ...initialFormValues }}
                        onValuesChange={() => {
                            if (!isFormDirty) {
                                setIsFormDirty(true);
                            }
                        }}
                    >
                        {isFormDirty && (
                            <Alert
                                type="error"
                                className="sticky top-0 z-10 rounded-none rounded-t-[8px] pt-2 pb-2"
                                description={
                                    <span className="text-sm">
                                        You have unsaved changes. Make sure to click the <strong>Save Questions</strong>{' '}
                                        button above.
                                    </span>
                                }
                            />
                        )}
                        <CardWrapper
                            cardTitle={<FormSettingsTitle />}
                            cardActions={[
                                <div key="close-add-question-button" className="flex items-center">
                                    <SavePrimaryFormButton />
                                </div>,
                            ]}
                        >
                            <Accordion
                                type="multiple"
                                className={cn('w-full border border-b-0 border-gray-100 [&_h3]:mb-0')}
                                defaultValue={['configuration']}
                            >
                                <AccordionItem value="configuration">
                                    <AccordionTrigger
                                        className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                        iconPosition="left"
                                    >
                                        Configuration
                                    </AccordionTrigger>

                                    <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                        <SelectDisplayLanguage />
                                        <ThankYouUrlQualified />
                                        <ThankYouUrlDisqualified />
                                        {calendarEngineIntegrationEnabled ? (
                                            <CalendlyEventType studyUUID={studyUUID} />
                                        ) : (
                                            <Alert
                                                className="mb-5"
                                                type="info"
                                                showIcon
                                                description={
                                                    <span className="text-sm">
                                                        Configure the calendar engine integration to select a scheduling
                                                        widget.
                                                    </span>
                                                }
                                            />
                                        )}
                                    </AccordionContent>
                                </AccordionItem>
                                <GeneralStyles />
                                <GeneralFormItemStyles />
                                <GeneralLabelStyles />
                                <GeneralLinkStyles />
                                <GeneralRadioGroupStyles />
                                <GeneralCheckboxGroupStyles />
                                <GeneralInputStyles />
                                <GeneralMultiScreenStyles />
                                <GeneralNextButtonStyles />
                                <GeneralSubmitButtonStyles />
                                <Form.Item name="uuid" hidden>
                                    <Input />
                                </Form.Item>
                            </Accordion>
                        </CardWrapper>
                    </Form>
                ) : null}
            </div>
        </TransitionWrapper>
    );
};

export default SettingsForm;
