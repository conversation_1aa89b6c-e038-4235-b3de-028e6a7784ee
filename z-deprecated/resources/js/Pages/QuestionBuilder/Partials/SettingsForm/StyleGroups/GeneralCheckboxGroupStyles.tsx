import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import CheckboxGroupAccordion from '@/Pages/QuestionBuilder/Partials/SettingsForm/Accordions/CheckboxGroupAccordion';

const GeneralCheckboxGroupStyles = () => {
    const namePath = ['settings', 'checkbox'];
    const { primaryForm } = useQuestionBuilderFormContext();
    const globalSettings = primaryForm?.getFieldValue('settings');

    return <CheckboxGroupAccordion namePath={namePath} settings={globalSettings} />;
};

export default GeneralCheckboxGroupStyles;
