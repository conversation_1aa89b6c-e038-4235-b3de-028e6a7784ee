import { FormInstance } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import RadioGroupAccordion from '@/Pages/QuestionBuilder/Partials/SettingsForm/Accordions/RadioGroupAccordion';

const GeneralRadioGroupStyles = () => {
    const namePath = ['settings', 'radio'];
    const { primaryForm } = useQuestionBuilderFormContext() as { primaryForm: FormInstance };
    const globalSettings = primaryForm.getFieldValue('settings');

    return <RadioGroupAccordion namePath={namePath} settings={globalSettings} />;
};

export default GeneralRadioGroupStyles;
