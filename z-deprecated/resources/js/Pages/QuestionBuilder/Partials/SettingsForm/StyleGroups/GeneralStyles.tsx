import CustomFont from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/CustomFont';
import FontColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontColor';
import FontFamily from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontFamily';
import FontSize from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontSize';
import FontWeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontWeight';
import LineHeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/LineHeight';
import TextTransform from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/TextTransform';
import { AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';

const GeneralStyle = () => (
    <AccordionItem value="general-styles">
        <AccordionTrigger
            className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
            iconPosition="left"
        >
            General Styles
        </AccordionTrigger>

        <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
            {/* <GoogleFont
            namePath={['settings', 'general', 'googleFont']}
            label="Google Font"
        /> */}
            <CustomFont namePath={['settings', 'general']} label="Custom Font Link" />
            <FontFamily namePath={['settings', 'general']} label="Font Family" />
            <FontSize namePath={['settings', 'general']} label="Font Size" />
            <LineHeight namePath={['settings', 'general']} label="Line Height" />
            <FontColor namePath={['settings', 'general']} label="Font Color" />
            <FontWeight namePath={['settings', 'general']} label="Font Weight" />
            <TextTransform namePath={['settings', 'general']} label="Text Transform" />
        </AccordionContent>
    </AccordionItem>
);

export default GeneralStyle;
