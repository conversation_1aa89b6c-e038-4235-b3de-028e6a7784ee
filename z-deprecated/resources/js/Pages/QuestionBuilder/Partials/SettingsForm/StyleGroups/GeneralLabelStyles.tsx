import { FormInstance } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import LabelAccordion from '@/Pages/QuestionBuilder/Partials/SettingsForm/Accordions/LabelAccordion';

const GeneralRadioGroupStyles = () => {
    const namePath = ['settings', 'form', 'label'];
    const { primaryForm } = useQuestionBuilderFormContext() as { primaryForm: FormInstance };
    const globalSettings = primaryForm.getFieldValue('settings');

    return <LabelAccordion namePath={namePath} label="" settings={globalSettings} />;
};

export default GeneralRadioGroupStyles;
