import { FormInstance } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import InputAccordion from '@/Pages/QuestionBuilder/Partials/SettingsForm/Accordions/InputAccordion';

const GeneralRadioGroupStyles = () => {
    const namePath = ['settings', 'input'];
    const { primaryForm } = useQuestionBuilderFormContext() as { primaryForm: FormInstance };
    const globalSettings = primaryForm.getFieldValue('settings');

    return <InputAccordion namePath={namePath} settings={globalSettings} />;
};

export default GeneralRadioGroupStyles;
