import { FormInstance } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import ButtonStyles from '@/Pages/QuestionBuilder/Partials/SettingsForm/StyleGroups/ButtonStyles';

const GeneralNextButtonStyles = () => {
    const { primaryForm } = useQuestionBuilderFormContext() as { primaryForm: FormInstance };
    const globalSettings = primaryForm.getFieldValue('settings');

    return (
        <ButtonStyles
            accordionName="Next Button"
            namePath={['settings', 'pageBreak', 'nextButton']}
            settings={globalSettings?.pageBreak?.nextButton}
            isAdvanced
        />
    );
};

export default GeneralNextButtonStyles;
