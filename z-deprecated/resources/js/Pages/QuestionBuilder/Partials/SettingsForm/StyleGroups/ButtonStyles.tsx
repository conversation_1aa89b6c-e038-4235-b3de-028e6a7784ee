import { isEmpty, merge, sum } from 'lodash';

import { cn } from '@/lib/utils';
import BackgroundColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BackgroundColor';
import Border from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Border';
import BorderColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BorderColor';
import BorderStyle from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BorderStyle';
import ButtonBlockType from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/ButtonBlockType';
import ButtonShape from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/ButtonShape';
import ButtonSize from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/ButtonSize';
import ButtonType from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/ButtonType';
import FontColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontColor';
import FontSize from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontSize';
import FontWeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontWeight';
import IconList from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/IconList';
import IconPosition from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/IconPosition';
import LineHeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/LineHeight';
import Margin from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Margin';
import Padding from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Padding';
import PlainText from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/PlainText';
import TextTransform from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/TextTransform';
import AdvancedAccordionHeader from '@/Pages/QuestionBuilder/Partials/Sidebar/AdvancedAccordionHeader';
import StyleCountAccordionHeader from '@/Pages/QuestionBuilder/Partials/Sidebar/StyleCountAccordionHeader';
import { useStoreState } from '@/store/hooks';
import { KeyValueGeneric } from '@/types/general';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';
import getProps from '@/utils/EmbedForm/get-props';
import getStyles from '@/utils/EmbedForm/get-styles';

const ButtonStyles = ({
    accordionName,
    namePath,
    isAdvanced = false,
    settings,
}: {
    accordionName: string;
    namePath: (string | number)[];
    isAdvanced?: boolean;
    settings: KeyValueGeneric;
}) => {
    const showAdvancedStyles = useStoreState(state => state.questionBuilderModel.showAdvancedStyles);
    const buttonPropsCount = Object.keys(getProps(settings?.buttonProps)).length;
    const iconCount = !isEmpty(settings?.icon) ? 1 : 0;
    const styleCounts = {
        btnLabel: getStyles(settings?.btnLabel?.style).length,
        button: getStyles(settings?.button?.style).length,
        buttonProps: buttonPropsCount + iconCount,
        label: getStyles(settings?.label?.style).length,
    };
    const alignTop = accordionName.includes('Next') ? 'top-[58px]' : 'top-[42px]';

    return (
        <div className={cn({ hidden: !showAdvancedStyles && isAdvanced })}>
            <AccordionItem value="button-styles">
                <AccordionTrigger
                    className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                    iconPosition="left"
                >
                    {isAdvanced ? (
                        <AdvancedAccordionHeader
                            header={accordionName}
                            styleCount={sum(Object.values(styleCounts as KeyValueGeneric))}
                        />
                    ) : (
                        <StyleCountAccordionHeader
                            header={accordionName}
                            styleCount={sum(Object.values(styleCounts as KeyValueGeneric))}
                        />
                    )}
                </AccordionTrigger>

                <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                    <Accordion
                        defaultValue={['button-styles-general']}
                        type="multiple"
                        className={cn('w-full border border-b-0 border-gray-100 [&_h3]:mb-0')}
                    >
                        <AccordionItem value="button-styles-general">
                            <AccordionTrigger
                                className={cn(
                                    'sticky z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold',
                                    alignTop
                                )}
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader
                                    header="General"
                                    styleCount={styleCounts?.buttonProps as number}
                                />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <PlainText
                                    namePath={merge([], [...namePath, 'btnLabel', 'text'])}
                                    label="Button Label"
                                />
                                <ButtonType namePath={merge([], [...namePath])} label="Button Type" />
                                <ButtonBlockType namePath={merge([], [...namePath])} label="Block Type" />
                                <ButtonShape namePath={merge([], [...namePath])} label="Button Shape" />
                                <ButtonSize namePath={merge([], [...namePath])} label="Button Size" />
                                <IconList namePath={merge([], [...namePath])} label="Icon" />
                                <IconPosition namePath={merge([], [...namePath])} label="Icon Position" />
                            </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value="button-styles-styles">
                            <AccordionTrigger
                                className={cn(
                                    'sticky z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold',
                                    alignTop
                                )}
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader header="Styles" styleCount={styleCounts?.button as number} />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <Margin namePath={merge([], [...namePath, 'button'])} label="" />
                                <Padding namePath={merge([], [...namePath, 'button'])} label="" />
                                <Border namePath={merge([], [...namePath, 'button'])} label="Border" />
                                <BorderStyle namePath={merge([], [...namePath, 'button'])} label="Border Style" />
                                <BorderColor namePath={merge([], [...namePath, 'button'])} label="Border Color" />
                                <BackgroundColor namePath={merge([], [...namePath, 'button'])} label="Background" />
                            </AccordionContent>
                        </AccordionItem>

                        <AccordionItem value="button-styles-font-style">
                            <AccordionTrigger
                                className={cn(
                                    'sticky z-9 border-b border-gray-200 bg-gray-50 px-5 font-semibold',
                                    alignTop
                                )}
                                iconPosition="left"
                            >
                                <StyleCountAccordionHeader
                                    header="Font Style"
                                    styleCount={styleCounts?.btnLabel as number}
                                />
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <FontSize namePath={merge([], [...namePath, 'btnLabel'])} label="Font Size" />
                                <LineHeight namePath={merge([], [...namePath, 'btnLabel'])} label="Line Height" />
                                <FontWeight namePath={merge([], [...namePath, 'btnLabel'])} label="Font Weight" />
                                <FontColor namePath={merge([], [...namePath, 'btnLabel'])} label="Font Color" />
                                <TextTransform
                                    namePath={merge([], [...namePath, 'btnLabel'])}
                                    label="Title Transform"
                                />
                            </AccordionContent>
                        </AccordionItem>
                    </Accordion>
                </AccordionContent>
            </AccordionItem>
        </div>
    );
};

export default ButtonStyles;
