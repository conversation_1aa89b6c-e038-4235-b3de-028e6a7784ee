import { FormInstance } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import LinkAccordion from '@/Pages/QuestionBuilder/Partials/SettingsForm/Accordions/LinkAccordion';

const GeneralLinkStyles = () => {
    const namePath = ['settings', 'form', 'link'];
    const { primaryForm } = useQuestionBuilderFormContext() as { primaryForm: FormInstance };
    const globalSettings = primaryForm.getFieldValue('settings');

    return <LinkAccordion namePath={namePath} label="" settings={globalSettings} />;
};

export default GeneralLinkStyles;
