import { FormInstance } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import MultiScreenAccordion from '@/Pages/QuestionBuilder/Partials/SettingsForm/Accordions/MultiScreenAccordion';

const GeneralMultiScreenStyles = () => {
    const { primaryForm } = useQuestionBuilderFormContext() as { primaryForm: FormInstance };
    const globalSettings = primaryForm.getFieldValue('settings');

    return <MultiScreenAccordion settings={globalSettings} />;
};

export default GeneralMultiScreenStyles;
