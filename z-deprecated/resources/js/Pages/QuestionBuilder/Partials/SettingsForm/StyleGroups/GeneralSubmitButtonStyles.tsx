import { FormInstance } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import ButtonStyles from '@/Pages/QuestionBuilder/Partials/SettingsForm/StyleGroups/ButtonStyles';

const GeneraSubmitButtonStyles = () => {
    const { primaryForm } = useQuestionBuilderFormContext() as { primaryForm: FormInstance };
    const globalSettings = primaryForm.getFieldValue('settings');

    return (
        <ButtonStyles
            accordionName="Submit Button"
            namePath={['settings', 'form', 'submitButton']}
            settings={globalSettings?.form?.submitButton}
        />
    );
};

export default GeneraSubmitButtonStyles;
