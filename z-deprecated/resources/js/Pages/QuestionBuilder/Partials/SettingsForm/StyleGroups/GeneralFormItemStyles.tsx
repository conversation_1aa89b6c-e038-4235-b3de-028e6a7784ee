import { FormInstance } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import FormItemAccordion from '@/Pages/QuestionBuilder/Partials/SettingsForm/Accordions/FormItemAccordion';

const GeneralFormItemStyles = () => {
    const namePath = ['settings', 'formItem'];
    const { primaryForm } = useQuestionBuilderFormContext() as { primaryForm: FormInstance };
    const globalSettings = primaryForm.getFieldValue('settings');

    return <FormItemAccordion namePath={namePath} settings={globalSettings} />;
};

export default GeneralFormItemStyles;
