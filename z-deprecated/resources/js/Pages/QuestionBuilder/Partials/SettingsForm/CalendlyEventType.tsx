import { Form } from 'antd';
import { useRoute } from 'ziggy-js';

import { UUID } from '@/types/general';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';

type CalendlyEventTypeProps = {
    studyUUID: UUID;
};

const CalendlyEventType = ({ studyUUID }: CalendlyEventTypeProps) => {
    const route = useRoute();

    return (
        <Form.Item name="calendlyEventType" label="Calendly Self-Scheduling Widget">
            <ComboSelect
                async
                endpoint={route('calendly-event-types-for-select', { study: studyUUID })}
                placeholder="Utilize a Calendly Scheduling Widget"
                isClearable
            />
        </Form.Item>
    );
};

export default CalendlyEventType;
