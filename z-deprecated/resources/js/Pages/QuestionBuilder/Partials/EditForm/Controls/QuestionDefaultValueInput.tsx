import { useCallback } from 'react';

import { Form, FormInstance, Input } from 'antd';
import { debounce } from 'lodash';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';

const QuestionDefaultValueInput = ({ index }: { index: number }) => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
    };

    const namePath = ['settings', 'input', 'defaultValue'];

    const onTextInputChange = useCallback(
        debounce(e => {
            questionBuilderForm.setFieldValue(['questions', index, ...namePath], e.target.value);
        }, 200),
        [index]
    );

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId } = prevValues;
                const { id: curId } = curValues;

                return prevId !== curId;
            }}
            noStyle
        >
            <div className="w-full">
                <Form.Item
                    label={<span className="text-xs uppercase tracking-tight">Field Default Value</span>}
                    name={[...namePath]}
                >
                    <Input.TextArea onChange={onTextInputChange} />
                </Form.Item>
            </div>
        </Form.Item>
    );
};

export default QuestionDefaultValueInput;
