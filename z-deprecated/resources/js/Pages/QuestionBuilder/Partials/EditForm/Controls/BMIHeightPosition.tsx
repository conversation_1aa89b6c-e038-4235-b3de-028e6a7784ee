import { Form, FormInstance, Radio, RadioChangeEvent } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';

const BMIHeightPosition = ({ index }: { index: number }) => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as { questionBuilderForm: FormInstance };

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId } = prevValues;
                const { id: curId } = curValues;

                return prevId !== curId;
            }}
            noStyle
        >
            {() => {
                const onChange = (event: RadioChangeEvent) => {
                    questionBuilderForm.setFields([
                        {
                            name: ['questions', index, 'settings', 'bmi', 'heightPosition'],
                            value: event.target.value,
                        },
                    ]);
                };

                return (
                    <div className="bmi_select w-full">
                        <Form.Item
                            name={['settings', 'bmi', 'heightPosition']}
                            label="Show height on the:"
                            rules={[{ required: true, message: 'Please select a height position' }]}
                        >
                            <Radio.Group onChange={onChange}>
                                <Radio value="left">
                                    <span className="font-semibold">Left</span>
                                </Radio>
                                <Radio value="right">
                                    <span className="font-semibold">Right</span>
                                </Radio>
                            </Radio.Group>
                        </Form.Item>
                    </div>
                );
            }}
        </Form.Item>
    );
};

export default BMIHeightPosition;
