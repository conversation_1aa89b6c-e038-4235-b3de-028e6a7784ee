import { ReactElement } from 'react';

import { Form, FormInstance, Input } from 'antd';
import { clone } from 'lodash';

import { regular } from '@fortawesome/fontawesome-svg-core/import.macro';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import useDeleteOption from '@/hooks/QuestionBuilder/useDeleteOption';
import useOptionTextChange from '@/hooks/QuestionBuilder/useOptionTextChange';
import AddOption from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/AddOption';
import { QuestionOption } from '@/types/question-options';
import { Card, CardContent, CardFooter } from '@/UI-Kit/Shadcn/card';
import { getRandomInt } from '@/utils/helpers';

const SingleMultiSelectOptions = ({ index }: { index: number }): ReactElement<any> | null => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
    };
    const { onOptionTextChange } = useOptionTextChange({ index });
    const { deleteOption } = useDeleteOption();
    const { id } = questionBuilderForm.getFieldValue(['questions', index, 'id']);

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId, optionLabels: prevOptionLabels } = prevValues;
                const { id: curId, optionLabels: curOptionLabels } = curValues;

                return prevId !== curId || prevOptionLabels.length !== curOptionLabels.length;
            }}
            noStyle
        >
            {({ getFieldValue }) => {
                const options = getFieldValue(['options']);

                return (
                    <Card className="options border-none">
                        <CardContent variant="noPadding" className="bg-white">
                            <ul className="grid grid-cols-1 gap-3 xl:grid-cols-2">
                                {options.map((option: QuestionOption, optionIndex: number) => {
                                    const optionLabelNamePath: (number | string)[] = clone(['optionLabels']);

                                    optionLabelNamePath.splice(optionLabelNamePath.length, 0);
                                    optionLabelNamePath.push(optionIndex);

                                    return (
                                        <li
                                            key={`option-list-${getRandomInt(736728)}`}
                                            className="flex flex-row items-center gap-x-1"
                                        >
                                            <span className="w-1/12">{`${optionIndex + 1}.`}</span>
                                            <Form.Item
                                                name={optionLabelNamePath}
                                                rules={[{ required: true, message: 'Please enter an option' }]}
                                                style={{ marginBottom: 0 }}
                                                className="w-10/12"
                                            >
                                                <Input
                                                    placeholder={`Option ${optionIndex + 1}`}
                                                    allowClear
                                                    size="small"
                                                    style={{
                                                        paddingTop: '4px',
                                                        paddingBottom: '3px',
                                                    }}
                                                    onChange={e => onOptionTextChange(e, option.value)}
                                                />
                                            </Form.Item>
                                            <div className="delete w-1/12">
                                                {options.length > 1 && (
                                                    <FontAwesomeIcon
                                                        icon={regular('times')}
                                                        className="cursor-pointer text-sm text-red-600"
                                                        onClick={(): void =>
                                                            deleteOption(option.value, ['questions', index])
                                                        }
                                                    />
                                                )}
                                            </div>
                                        </li>
                                    );
                                })}
                            </ul>
                        </CardContent>

                        <CardFooter border="none" size="none" className="pt-4">
                            <AddOption key={`options-add-option-button-${id}`} />
                        </CardFooter>
                    </Card>
                );
            }}
        </Form.Item>
    );
};

export default SingleMultiSelectOptions;
