import { Form, FormInstance, Radio, RadioChangeEvent } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';

const EncryptCheckbox = ({ index }: { index: number }) => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
    };

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId } = prevValues;
                const { id: curId } = curValues;

                return prevId !== curId;
            }}
            noStyle
        >
            <div className="flex w-full flex-row md:w-1/2">
                <Form.Item
                    name="isEncrypted"
                    label={<span className="text-xs uppercase tracking-tight">Encrypt Answer</span>}
                >
                    <Radio.Group
                        onChange={(e: RadioChangeEvent) => {
                            questionBuilderForm.setFieldValue(['questions', index, 'isEncrypted'], e.target.value);
                        }}
                    >
                        <Radio value="1">
                            <span className="text-xs font-semibold">Yes</span>
                        </Radio>
                        <Radio value="0">
                            <span className="text-xs font-semibold">No</span>
                        </Radio>
                    </Radio.Group>
                </Form.Item>
            </div>
        </Form.Item>
    );
};

export default EncryptCheckbox;
