import { Form, FormInstance, Radio, RadioChangeEvent } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';

const BMIOptionSelect = ({ index }: { index: number }) => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as { questionBuilderForm: FormInstance };

    const onChange = (event: RadioChangeEvent) => {
        const {
            target: { value },
        } = event;

        questionBuilderForm.setFields([{ name: ['questions', index, 'settings', 'bmi', 'unit'], value }]);
    };

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId } = prevValues;
                const { id: curId } = curValues;

                return prevId !== curId;
            }}
            noStyle
        >
            {() => (
                <div className="bmi_select w-full">
                    <Form.Item
                        name={['settings', 'bmi', 'unit']}
                        label="Metric System"
                        rules={[{ required: true, message: 'Please select a measurement type' }]}
                    >
                        <Radio.Group onChange={onChange}>
                            <Radio value="us">
                                <span className="font-semibold">US</span>
                            </Radio>
                            <Radio value="metric">
                                <span className="font-semibold">Metric</span>
                            </Radio>
                        </Radio.Group>
                    </Form.Item>
                </div>
            )}
        </Form.Item>
    );
};

export default BMIOptionSelect;
