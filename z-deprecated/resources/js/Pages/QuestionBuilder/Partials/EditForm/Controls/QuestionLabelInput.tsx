import { useCallback } from 'react';

import { Form, FormInstance, Input } from 'antd';
import { debounce } from 'lodash';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';

const QuestionLabelInput = ({ index }: { index: number }) => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
    };

    const onTextInputChange = useCallback(
        debounce(e => {
            questionBuilderForm.setFieldValue(['questions', index, 'questionLabel'], e.target.value);
        }, 200),
        [index]
    );

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId } = prevValues;
                const { id: curId } = curValues;

                return prevId !== curId;
            }}
            noStyle
        >
            <div className="w-full">
                <Form.Item
                    label={<span className="text-xs uppercase tracking-tight">Field Label</span>}
                    name="questionLabel"
                    rules={[{ required: true, message: 'Please enter a question label' }]}
                >
                    <Input.TextArea onChange={onTextInputChange} />
                </Form.Item>
            </div>
        </Form.Item>
    );
};

export default QuestionLabelInput;
