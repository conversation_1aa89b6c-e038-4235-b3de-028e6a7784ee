import React from 'react';

import useAddOption from '@/hooks/QuestionBuilder/useAddOption';
import { useStoreState } from '@/store/hooks';
import Button from '@/UI-Kit/Button';

const AddOption = () => {
    const languageCode = useStoreState(state => state.questionBuilderModel.languageCode);
    const editIndex = useStoreState(state => state.questionBuilderModel.editIndex) as number;
    const { addOption } = useAddOption();
    const addOptionProps = {
        className: 'self-end',
        disabled: false,
        onClick: (e: React.MouseEvent<HTMLElement>): void => addOption(e, editIndex),
    };

    if (languageCode !== 'en') {
        addOptionProps.className = 'btn-outline-default-sm disabled:opacity-50 cursor-not-allowed';
        addOptionProps.disabled = true;
    }

    return <Button label="Add Option" color="green" size="mini" {...addOptionProps} />;
};

export default AddOption;
