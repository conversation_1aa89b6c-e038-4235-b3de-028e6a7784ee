import { Form, Input } from 'antd';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const HiddenFields = () => (
    <>
        <Form.Item noStyle name="children">
            <Input type="hidden" />
        </Form.Item>
        <Form.Item noStyle name="condition">
            <Input type="hidden" />
        </Form.Item>
        <Form.Item noStyle name="conditionSource">
            <Input type="hidden" />
        </Form.Item>
        <Form.Item noStyle name="conditionOptions">
            <Input type="hidden" />
        </Form.Item>
        <Form.Item noStyle name="conditionValue">
            <Input type="hidden" />
        </Form.Item>
        <Form.Item noStyle name="hidden">
            <Input type="hidden" />
        </Form.Item>
        <Form.Item noStyle name="id">
            <Input type="hidden" />
        </Form.Item>
        <Form.Item noStyle name="isPredefined">
            <Input type="hidden" />
        </Form.Item>
        <Form.Item noStyle name="json">
            <Input type="hidden" />
        </Form.Item>
        <Form.Item noStyle name="level">
            <Input type="hidden" />
        </Form.Item>
        <Form.Item noStyle name="options">
            <Input type="hidden" />
        </Form.Item>
        <Form.Item noStyle name="optionLabels">
            <Input type="hidden" />
        </Form.Item>
        <Form.Item noStyle name="parentId">
            <Input type="hidden" />
        </Form.Item>
        <Form.Item noStyle name="question">
            <Input type="hidden" />
        </Form.Item>
        <Form.Item noStyle name="questionType">
            <Input type="hidden" />
        </Form.Item>
        <Form.Item noStyle name="uuid">
            <Input type="hidden" />
        </Form.Item>
    </>
);

export default HiddenFields;
