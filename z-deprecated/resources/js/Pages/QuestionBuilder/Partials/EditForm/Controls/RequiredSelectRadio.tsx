import { useEffect } from 'react';

import { Form, FormInstance, Radio, RadioChangeEvent } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';

const RequiredSelectRadio = ({ index }: { index: number }) => {
    const { questionBuilderForm, editQuestionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
        editQuestionBuilderForm: FormInstance;
    };

    const inputType = Form.useWatch(['settings', 'input', 'type'], editQuestionBuilderForm);

    useEffect(() => {
        if (inputType?.value === 'hidden') {
            editQuestionBuilderForm.setFieldValue('isRequired', '0');

            questionBuilderForm.setFieldValue(['questions', index, 'isRequired'], 0);
        }
    }, [inputType?.value]);

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId } = prevValues;
                const { id: curId } = curValues;

                return prevId !== curId;
            }}
            noStyle
        >
            {({ getFieldValue }) => {
                const questionType = getFieldValue('questionType');

                return (
                    <div className="flex w-full flex-row md:w-1/2">
                        <Form.Item
                            name="isRequired"
                            label={<span className="text-xs uppercase tracking-tight">Answer Required</span>}
                            required
                        >
                            <Radio.Group
                                onChange={(e: RadioChangeEvent) => {
                                    questionBuilderForm.setFieldValue(
                                        ['questions', index, 'isRequired'],
                                        e.target.value
                                    );
                                }}
                            >
                                <Radio
                                    value="1"
                                    disabled={!questionType?.isAllowRequired || inputType?.value === 'hidden'}
                                >
                                    <span className="text-xs font-semibold">Yes</span>
                                </Radio>
                                <Radio value="0" disabled={inputType?.value === 'hidden'}>
                                    <span className="text-xs font-semibold">No</span>
                                </Radio>
                            </Radio.Group>
                        </Form.Item>
                    </div>
                );
            }}
        </Form.Item>
    );
};

export default RequiredSelectRadio;
