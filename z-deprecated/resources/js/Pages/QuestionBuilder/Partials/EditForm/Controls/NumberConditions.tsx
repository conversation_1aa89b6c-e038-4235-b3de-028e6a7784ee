import { ReactElement, useCallback } from 'react';

import { Form, FormInstance, InputNumber } from 'antd';
import { debounce, findIndex } from 'lodash';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import useOnConditionsChange from '@/hooks/QuestionBuilder/useOnConditionsChange';
import { useStoreState } from '@/store/hooks';
import { ANY_TODO } from '@/types/general';
import { ConvertedQuestion } from '@/types/question';
import { Card, CardContent, CardHeader, CardTitle } from '@/UI-Kit/Shadcn/card';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import { numberOperators } from '@/utils/number-operators';

const NumberConditions = ({ index }: { index: number }): ReactElement<any> | null => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
    };
    const languageCode = useStoreState(state => state.questionBuilderModel.languageCode);
    const { onConditionsChange } = useOnConditionsChange();

    const onTextInputChange = useCallback(
        debounce(
            (data: ConvertedQuestion['conditionValue'], type: string, parentIndex) =>
                onConditionsChange(data, type, index, parentIndex),
            500
        ),
        [index]
    );

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId, parentId: prevParentId } = prevValues;
                const { id: curId, parentId: curParentId } = curValues;

                return prevId !== curId || prevParentId !== curParentId;
            }}
            noStyle
        >
            {({ getFieldValue }) => {
                const parentId = getFieldValue('parentId');
                const parentIndex = findIndex(questionBuilderForm.getFieldsValue().questions, { id: parentId });

                return (
                    <Card className="condition mt-6">
                        <CardHeader>
                            <CardTitle>
                                <div className="text-sm font-semibold">Only show if parent answer is:</div>
                            </CardTitle>
                        </CardHeader>

                        <CardContent className="p-3">
                            <div className="flex w-full items-center">
                                <div className="mr-3 w-6/12">
                                    <Form.Item
                                        name="conditionOptions"
                                        rules={[{ required: true, message: 'Please select an operator' }]}
                                        style={{ marginBottom: 0 }}
                                    >
                                        <ComboSelect
                                            placeholder="Select Operator"
                                            isClearable={false}
                                            isDisabled={languageCode !== 'en'}
                                            options={Object.keys(numberOperators).map((key: string) => ({
                                                value: numberOperators[key].operator,
                                                label: numberOperators[key].label,
                                            }))}
                                            onChange={data => onConditionsChange(data, 'select', index, parentIndex)}
                                            isCompact
                                        />
                                    </Form.Item>
                                </div>
                                <div className="w-6/12">
                                    <Form.Item
                                        name="conditionValue"
                                        rules={[{ required: true, message: 'Please a condition value' }]}
                                        style={{ marginBottom: 0 }}
                                    >
                                        <InputNumber
                                            min={0}
                                            disabled={languageCode !== 'en'}
                                            onChange={(value: ANY_TODO) => {
                                                onTextInputChange(value, 'number-input', parentIndex);
                                            }}
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                );
            }}
        </Form.Item>
    );
};

export default NumberConditions;
