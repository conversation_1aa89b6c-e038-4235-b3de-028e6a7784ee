import { ReactElement, useCallback } from 'react';

import { Form, FormInstance, Input } from 'antd';
import { debounce, findIndex, merge } from 'lodash';
import clone from 'lodash/clone';
import isEmpty from 'lodash/isEmpty';
import md5 from 'md5';

import { regular } from '@fortawesome/fontawesome-svg-core/import.macro';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import useDeleteOption from '@/hooks/QuestionBuilder/useDeleteOption';
import useOptionTextChange from '@/hooks/QuestionBuilder/useOptionTextChange';
import AddOption from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/AddOption';
import { useStoreState } from '@/store/hooks';
import { QuestionOption } from '@/types/question-options';
import { ReactSelectOptionType } from '@/types/react-select';
import { SiteDataList } from '@/types/site-data';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/UI-Kit/Shadcn/card';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import { getRandomInt } from '@/utils/helpers';

const SiteSelectOptions = ({ index }: { index: number }): ReactElement<any> | null => {
    const sites: SiteDataList[] = useStoreState(state => state.questionBuilderModel.sites);
    const { deleteOption } = useDeleteOption();
    const { questionBuilderForm, editQuestionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
        editQuestionBuilderForm: FormInstance;
    };
    const { onOptionTextChange } = useOptionTextChange({ index });
    const { id } = questionBuilderForm.getFieldValue(['questions', index, 'id']);

    const onSiteOptionChange = useCallback(
        debounce((data, optionValue) => {
            const options = questionBuilderForm.getFieldValue(['questions', index, 'options']);
            const optionIndex = findIndex(options, { value: optionValue });

            questionBuilderForm.setFieldValue(['questions', index, 'options', optionIndex], data);
            questionBuilderForm.setFieldValue(['questions', index, 'optionLabels', optionIndex], data.label);

            editQuestionBuilderForm.setFieldValue(['optionLabels', optionIndex], data.label);
            editQuestionBuilderForm.setFieldValue(['options', optionIndex], data);
        }, 100),
        [index]
    );

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId, optionLabels: prevOptionLabels, options: prevOptions } = prevValues;
                const { id: curId, optionLabels: curOptionLabels, options: curOptions } = curValues;

                const prevOptionValues = md5(JSON.stringify(prevOptions.map((option: QuestionOption) => option.value)));
                const curOptionValues = md5(JSON.stringify(curOptions.map((option: QuestionOption) => option.value)));

                return (
                    prevId !== curId ||
                    prevOptions.length !== curOptions.length ||
                    prevOptionLabels.length !== curOptionLabels.length ||
                    prevOptionValues !== curOptionValues
                );
            }}
            noStyle
        >
            {({ getFieldValue }) => {
                const options = getFieldValue('options');
                const cardActions = [];

                if (!isEmpty(options) && !isEmpty(sites)) {
                    const unusedOptions = sites.filter(
                        site => options.findIndex((option: QuestionOption) => option.value === site.site_public) < 0
                    );

                    if (!isEmpty(unusedOptions)) {
                        cardActions.push(<AddOption key={`options-add-option-button-${id}`} />);
                    }
                }

                if (options.length === 0) {
                    return null;
                }

                return (
                    <Card className="options border-none">
                        <CardHeader className="bg-white! p-0" border="none">
                            <CardTitle>
                                <span className="option-label text-xs font-semibold tracking-tight uppercase">
                                    Site Options
                                </span>
                            </CardTitle>
                        </CardHeader>

                        <CardContent variant="noPadding" className="bg-white pt-4">
                            <ul>
                                {options.map((option: ReactSelectOptionType, optionIndex: number) => {
                                    const optionLabelNamePath: (number | string)[] = clone(['optionLabels']);
                                    const reactSelectOption = merge({}, option);

                                    const selectOptions = sites
                                        .filter(site => {
                                            if (reactSelectOption.value === site.site_public) {
                                                reactSelectOption.label = site.name;
                                            }

                                            return (
                                                site.site_public === option.value ||
                                                options
                                                    .filter(
                                                        (siteOption: ReactSelectOptionType) =>
                                                            siteOption.value !== option.value
                                                    ) // Skip current option.
                                                    .findIndex(
                                                        (siteOption: ReactSelectOptionType) =>
                                                            siteOption.value === site.site_public
                                                    ) < 0
                                            ); // Filter out options that have been selected by other options}
                                        })
                                        .map(site => ({
                                            label: site.name,
                                            value: site.site_public,
                                        }));

                                    optionLabelNamePath.splice(optionLabelNamePath.length, 0);
                                    optionLabelNamePath.push(optionIndex);

                                    return (
                                        <li
                                            key={`site-option-${getRandomInt(77378)}`}
                                            className="site-options mb-2 grid grid-cols-[20px_repeat(10,1fr)_20px] gap-3"
                                        >
                                            <span className="flex items-center justify-center">{`${
                                                optionIndex + 1
                                            }.`}</span>

                                            <ComboSelect
                                                className="col-span-5"
                                                placeholder="Select Site"
                                                isClearable={false}
                                                value={reactSelectOption}
                                                options={selectOptions}
                                                onChange={data => onSiteOptionChange(data, option.value)}
                                                isCompact
                                            />

                                            <Form.Item
                                                name={optionLabelNamePath}
                                                rules={[{ required: true, message: 'Please enter an option label' }]}
                                                style={{ marginBottom: 0 }}
                                                className="col-span-5"
                                            >
                                                <Input
                                                    placeholder={`Option ${optionIndex + 1}`}
                                                    allowClear
                                                    onChange={e => onOptionTextChange(e, option.value)}
                                                />
                                            </Form.Item>

                                            <div className="delete flex items-center justify-center">
                                                {optionIndex > 0 && (
                                                    <FontAwesomeIcon
                                                        icon={regular('times')}
                                                        className="cursor-pointer text-sm text-red-600"
                                                        onClick={(): void =>
                                                            deleteOption(option.value, ['questions', index])
                                                        }
                                                    />
                                                )}
                                            </div>
                                        </li>
                                    );
                                })}
                            </ul>
                        </CardContent>

                        <CardFooter border="none" className="pb-0">
                            {cardActions}
                        </CardFooter>
                    </Card>
                );
            }}
        </Form.Item>
    );
};

export default SiteSelectOptions;
