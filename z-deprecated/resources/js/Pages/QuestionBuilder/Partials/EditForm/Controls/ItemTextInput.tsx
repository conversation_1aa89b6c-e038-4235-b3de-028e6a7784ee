import { ChangeEvent } from 'react';

import { FormInstance } from 'antd';
import { merge } from 'lodash';
import { Rule } from 'rc-field-form/lib/interface';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import PlainText from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/PlainText';
import { FormSettingFieldProps } from '@/types/question-builder-settings';

const ItemTextInput = ({
    namePath,
    label,
    isRequired = false,
    placeholder,
    index,
}: FormSettingFieldProps & { index: number }) => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as { questionBuilderForm: FormInstance };
    const rules: Rule[] = [];

    if (isRequired) {
        rules.push({ required: true, message: 'Please enter a value' });
    }

    const onChange = (event: ChangeEvent<HTMLInputElement>) => {
        questionBuilderForm.setFields([
            {
                name: merge([], ['questions', index], [...namePath]),
                value: event.target.value,
            },
        ]);
    };

    return (
        <PlainText
            namePath={namePath}
            label={label}
            isRequired={isRequired}
            placeholder={placeholder}
            onChange={onChange}
        />
    );
};

export default ItemTextInput;
