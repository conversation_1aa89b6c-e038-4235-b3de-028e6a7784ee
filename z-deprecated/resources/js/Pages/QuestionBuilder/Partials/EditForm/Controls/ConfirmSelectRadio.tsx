import { useState } from 'react';

import { Form, FormInstance, Radio, RadioChangeEvent } from 'antd';

import ConfirmQuestionLabelInput from './ConfirmQuestionLabelInput';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';

const ConfirmSelectRadio = ({ index }: { index: number }) => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as { questionBuilderForm: FormInstance };
    const [showConfirmQuestionLabel, setShowConfirmQuestionLabel] = useState(
        questionBuilderForm.getFieldValue(['questions', index, 'isConfirmed']) === '1'
    );

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId } = prevValues;
                const { id: curId } = curValues;

                return prevId !== curId;
            }}
            noStyle
        >
            <div className="flex w-full flex-row md:w-1/2">
                <Form.Item name="isConfirmed" label="Confirm This Field" required>
                    <Radio.Group
                        onChange={(e: RadioChangeEvent) => {
                            questionBuilderForm.setFieldValue(['questions', index, 'isConfirmed'], e.target.value);

                            setShowConfirmQuestionLabel(e.target.value === '1');
                        }}
                    >
                        <Radio value="1">
                            <span className="text-xs font-semibold">Yes</span>
                        </Radio>
                        <Radio value="0">
                            <span className="text-xs font-semibold">No</span>
                        </Radio>
                    </Radio.Group>
                </Form.Item>
            </div>

            {showConfirmQuestionLabel && <ConfirmQuestionLabelInput index={index} />}
        </Form.Item>
    );
};

export default ConfirmSelectRadio;
