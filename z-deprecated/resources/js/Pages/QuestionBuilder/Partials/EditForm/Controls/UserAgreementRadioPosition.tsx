import { Form, FormInstance, Radio, RadioChangeEvent } from 'antd';
import { merge } from 'lodash';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';

const UserAgreementRadioPosition = ({ index }: { index: number }) => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as { questionBuilderForm: FormInstance };
    const namePath = ['settings', 'userAgreement', 'radioPosition'];

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId } = prevValues;
                const { id: curId } = curValues;

                return prevId !== curId;
            }}
            noStyle
        >
            {() => {
                const onChange = (event: RadioChangeEvent) => {
                    questionBuilderForm.setFields([
                        {
                            name: merge([], ['questions', index, ...namePath]),
                            value: event.target.value,
                        },
                    ]);
                };

                return (
                    <div className="w-full">
                        <Form.Item
                            name={merge([], [...namePath])}
                            label="Show radio button at the:"
                            rules={[{ required: true, message: 'Please select a radio position' }]}
                        >
                            <Radio.Group onChange={onChange}>
                                <Radio value="top">
                                    <span className="font-semibold">Top</span>
                                </Radio>
                                <Radio value="bottom">
                                    <span className="font-semibold">Bottom</span>
                                </Radio>
                            </Radio.Group>
                        </Form.Item>
                    </div>
                );
            }}
        </Form.Item>
    );
};

export default UserAgreementRadioPosition;
