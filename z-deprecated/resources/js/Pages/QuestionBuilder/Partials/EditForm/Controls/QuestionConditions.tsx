import { ReactElement } from 'react';

import { Checkbox, Form, FormInstance } from 'antd';
import { findIndex } from 'lodash';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import useOnConditionsChange from '@/hooks/QuestionBuilder/useOnConditionsChange';
import { useStoreState } from '@/store/hooks';
import { ANY_TODO } from '@/types/general';
import { Card, CardContent, CardHeader, CardTitle } from '@/UI-Kit/Shadcn/card';

const QuestionConditions = ({ index }: { index: number }): ReactElement<any> | null => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
    };
    const languageCode = useStoreState(state => state.questionBuilderModel.languageCode);
    const { onConditionsChange } = useOnConditionsChange();

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId, parentId: prevParentId } = prevValues;
                const { id: curId, parentId: curParentId } = curValues;

                return prevId !== curId || prevParentId !== curParentId;
            }}
            noStyle
        >
            {({ getFieldValue }) => {
                const parentId = getFieldValue('parentId');
                const parentIndex = findIndex(questionBuilderForm.getFieldsValue().questions, { id: parentId });
                const parentQuestion = questionBuilderForm.getFieldValue(['questions', parentIndex]);
                const allowRender = parentQuestion?.optionLabels?.length > 0;

                return (
                    <>
                        {allowRender ? (
                            <Card className="condition">
                                <CardHeader className="px-3">
                                    <CardTitle>
                                        <span className="text-sm font-semibold">{`Only show if parent answer is ${
                                            parentQuestion.questionType.isAllowOptions ? ' any of' : ''
                                        }:`}</span>
                                    </CardTitle>
                                </CardHeader>

                                <CardContent className="p-3">
                                    <Form.Item
                                        name="conditionOptions"
                                        rules={[{ required: true, message: 'Please select at least one condition' }]}
                                        style={{ marginBottom: 0 }}
                                    >
                                        <Checkbox.Group
                                            className="grid grid-cols-1 justify-items-stretch gap-2 lg:grid-cols-2"
                                            options={parentQuestion?.options}
                                            disabled={languageCode !== 'en'}
                                            onChange={(checkedValue: ANY_TODO) =>
                                                onConditionsChange(checkedValue, 'checkbox-group', index, parentIndex)
                                            }
                                        />
                                    </Form.Item>
                                </CardContent>
                            </Card>
                        ) : (
                            <p>The parent does not have any options yet</p>
                        )}
                    </>
                );
            }}
        </Form.Item>
    );
};

export default QuestionConditions;
