/* eslint-disable @typescript-eslint/no-unused-vars */
import { useCallback, useEffect, useRef } from 'react';

import { Form, FormInstance } from 'antd';
import { debounce, merge } from 'lodash';

import Delimiter from '@editorjs/delimiter';
import EditorJS from '@editorjs/editorjs';
import Header from '@editorjs/header';
import List from '@editorjs/list';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import { ANY_TODO } from '@/types/general';

export const EDITOR_JS_TOOLS = {
    list: List,
    header: Header,
    delimiter: Delimiter,
};

const FreeFormTextEditor = ({ index }: { index: number }) => {
    const namePath = ['settings', 'freeFormText', 'text'];

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId } = prevValues;
                const { id: curId } = curValues;

                return prevId !== curId;
            }}
            noStyle
        >
            <div className="w-full">
                <Form.Item
                    label="Free Form Text Editor"
                    name={merge([], [...namePath])}
                    rules={[{ required: true, message: 'Please enter some content' }]}
                >
                    <InputEditor index={index} namePath={namePath} editorblock="free-form-text-editor" />
                </Form.Item>
            </div>
        </Form.Item>
    );
};

const InputEditor = ({ index, value, onChange, namePath, editorblock }: ANY_TODO) => {
    const editorRef = useRef<ANY_TODO>(undefined);
    const { questionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
    };

    const onTextInputChange = useCallback(
        debounce(newValue => {
            questionBuilderForm.setFieldValue(merge([], ['questions', index, ...namePath]), newValue);
        }, 200),
        [index]
    );

    useEffect(() => {
        // Initialize editorjs if we don't have a reference
        if (!editorRef.current) {
            editorRef.current = new EditorJS({
                holder: editorblock,
                tools: EDITOR_JS_TOOLS,
                data: value,
                minHeight: 200,
                async onChange(api, event) {
                    const editorData = await api.saver.save();

                    onChange(editorData);
                    onTextInputChange(editorData);
                },
            });
        }

        // Add a return function to handle cleanup
        return () => {
            if (editorRef.current && editorRef.current.destroy) {
                editorRef.current.destroy();
            }
        };
    }, []);

    return <div className="mb-10 rounded border border-gray-100 p-3 shadow-inner" id={editorblock} />;
};

export default FreeFormTextEditor;
