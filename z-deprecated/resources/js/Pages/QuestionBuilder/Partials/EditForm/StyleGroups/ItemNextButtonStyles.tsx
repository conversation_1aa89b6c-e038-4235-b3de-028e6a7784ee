import { Form } from 'antd';

import ButtonStyles from '@/Pages/QuestionBuilder/Partials/SettingsForm/StyleGroups/ButtonStyles';

const ItemNextButtonStyles = () => (
    <Form.Item
        shouldUpdate={(prevValues, curValues) => {
            const { id: prevId, parentId: prevParentId } = prevValues;
            const { id: curId, parentId: curParentId } = curValues;

            return prevId !== curId || prevParentId !== curParentId;
        }}
        noStyle
    >
        {({ getFieldValue }) => {
            const questionType = getFieldValue('questionType');
            const itemSettings = getFieldValue('settings');

            if (!['nextButton'].includes(questionType?.name)) {
                return null;
            }

            return (
                <ButtonStyles
                    accordionName="Next Button"
                    namePath={['settings', 'pageBreak', 'nextButton']}
                    settings={itemSettings?.pageBreak?.nextButton}
                />
            );
        }}
    </Form.Item>
);

export default ItemNextButtonStyles;
