import { Form } from 'antd';

import ButtonStyles from '@/Pages/QuestionBuilder/Partials/SettingsForm/StyleGroups/ButtonStyles';

const ItemSubmitButtonStyles = () => (
    <Form.Item
        shouldUpdate={(prevValues, curValues) => {
            const { id: prevId, parentId: prevParentId } = prevValues;
            const { id: curId, parentId: curParentId } = curValues;

            return prevId !== curId || prevParentId !== curParentId;
        }}
        noStyle
    >
        {({ getFieldValue }) => {
            const questionType = getFieldValue('questionType');
            const itemSettings = getFieldValue('settings');

            if (!['submitButton'].includes(questionType?.name)) {
                return null;
            }

            return (
                <ButtonStyles
                    accordionName="Submit Button"
                    namePath={['settings', 'form', 'submitButton']}
                    settings={itemSettings?.form?.submitButton}
                />
            );
        }}
    </Form.Item>
);

export default ItemSubmitButtonStyles;
