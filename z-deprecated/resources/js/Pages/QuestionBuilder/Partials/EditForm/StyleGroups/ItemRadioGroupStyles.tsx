import { Form } from 'antd';

import RadioGroupAccordion from '@/Pages/QuestionBuilder/Partials/SettingsForm/Accordions/RadioGroupAccordion';

const ItemRadioGroupStyles = () => {
    const namePath = ['settings', 'radio'];

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId, parentId: prevParentId } = prevValues;
                const { id: curId, parentId: curParentId } = curValues;

                return prevId !== curId || prevParentId !== curParentId;
            }}
            noStyle
        >
            {({ getFieldValue }) => {
                const questionType = getFieldValue('questionType');
                const itemSettings = getFieldValue('settings');

                if (!['oneof', 'acceptTerms'].includes(questionType?.name) && !questionType?.isAllowSites) {
                    return null;
                }

                return <RadioGroupAccordion namePath={namePath} settings={itemSettings} />;
            }}
        </Form.Item>
    );
};

export default ItemRadioGroupStyles;
