import { Form } from 'antd';

import FormItemAccordion from '@/Pages/QuestionBuilder/Partials/SettingsForm/Accordions/FormItemAccordion';

const FormItemStyles = () => {
    const namePath = ['settings', 'formItem'];

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId, parentId: prevParentId } = prevValues;
                const { id: curId, parentId: curParentId } = curValues;

                return prevId !== curId || prevParentId !== curParentId;
            }}
            noStyle
        >
            {({ getFieldValue }) => {
                const questionType = getFieldValue('questionType');
                const itemSettings = getFieldValue('settings');

                if (questionType?.isLayout) {
                    return null;
                }

                return <FormItemAccordion namePath={namePath} settings={itemSettings} />;
            }}
        </Form.Item>
    );
};

export default FormItemStyles;
