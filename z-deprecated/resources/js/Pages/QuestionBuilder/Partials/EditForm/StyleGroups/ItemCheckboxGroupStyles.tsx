import { Form } from 'antd';

import CheckboxGroupAccordion from '@/Pages/QuestionBuilder/Partials/SettingsForm/Accordions/CheckboxGroupAccordion';

const ItemCheckboxGroupStyles = () => {
    const namePath = ['settings', 'checkbox'];

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId, parentId: prevParentId } = prevValues;
                const { id: curId, parentId: curParentId } = curValues;

                return prevId !== curId || prevParentId !== curParentId;
            }}
            noStyle
        >
            {({ getFieldValue }) => {
                const questionType = getFieldValue('questionType');
                const itemSettings = getFieldValue('settings');

                if (!['anyof'].includes(questionType?.name)) {
                    return null;
                }

                return <CheckboxGroupAccordion namePath={namePath} settings={itemSettings} />;
            }}
        </Form.Item>
    );
};

export default ItemCheckboxGroupStyles;
