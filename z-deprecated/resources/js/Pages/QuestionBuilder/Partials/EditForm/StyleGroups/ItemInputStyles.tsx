import { Form } from 'antd';

import InputAccordion from '@/Pages/QuestionBuilder/Partials/SettingsForm/Accordions/InputAccordion';

const ItemInputStyles = () => {
    const namePath = ['settings', 'input'];

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId, parentId: prevParentId } = prevValues;
                const { id: curId, parentId: curParentId } = curValues;

                return prevId !== curId || prevParentId !== curParentId;
            }}
            noStyle
        >
            {({ getFieldValue }) => {
                const questionType = getFieldValue('questionType');
                const itemSettings = getFieldValue('settings');

                if (
                    ['anyof', 'oneof', 'oneofsite', 'acceptTerms'].includes(questionType?.name) ||
                    questionType?.isLayout
                ) {
                    return null;
                }

                return <InputAccordion namePath={namePath} settings={itemSettings} />;
            }}
        </Form.Item>
    );
};

export default ItemInputStyles;
