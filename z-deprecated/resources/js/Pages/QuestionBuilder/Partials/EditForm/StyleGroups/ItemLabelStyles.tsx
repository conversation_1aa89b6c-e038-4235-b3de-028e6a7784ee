import { Form } from 'antd';

import LabelAccordion from '@/Pages/QuestionBuilder/Partials/SettingsForm/Accordions/LabelAccordion';

const ItemLabelStyles = () => {
    const namePath = ['settings', 'form', 'label'];

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId, parentId: prevParentId } = prevValues;
                const { id: curId, parentId: curParentId } = curValues;

                return prevId !== curId || prevParentId !== curParentId;
            }}
            noStyle
        >
            {({ getFieldValue }) => {
                const questionType = getFieldValue('questionType');
                const itemSettings = getFieldValue('settings');

                if (['submitButton', 'nextButton'].includes(questionType?.name) || questionType?.isLayout) {
                    return null;
                }

                return <LabelAccordion namePath={namePath} label="" settings={itemSettings} />;
            }}
        </Form.Item>
    );
};

export default ItemLabelStyles;
