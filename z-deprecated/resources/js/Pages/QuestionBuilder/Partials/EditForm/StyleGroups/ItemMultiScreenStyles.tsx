import { Form } from 'antd';

import MultiScreenAccordion from '@/Pages/QuestionBuilder/Partials/SettingsForm/Accordions/MultiScreenAccordion';

const ItemMultiScreenStyles = () => (
    <Form.Item
        shouldUpdate={(prevValues, curValues) => {
            const { id: prevId, parentId: prevParentId } = prevValues;
            const { id: curId, parentId: curParentId } = curValues;

            return prevId !== curId || prevParentId !== curParentId;
        }}
        noStyle
    >
        {({ getFieldValue }) => {
            const questionType = getFieldValue('questionType');
            const itemSettings = getFieldValue('settings');

            if (!['pageBreak', 'startPaging'].includes(questionType?.name)) {
                return null;
            }

            return <MultiScreenAccordion settings={itemSettings} />;
        }}
    </Form.Item>
);

export default ItemMultiScreenStyles;
