import { Form, FormInstance } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import Accordion from '@/UI-Kit/Accordion';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';

const CountryCodeAccordion = ({ index }: { index: number }) => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as { questionBuilderForm: FormInstance };

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId, optionLabels: prevOptionLabels } = prevValues;
                const { id: curId, optionLabels: curOptionLabels } = curValues;

                return prevId !== curId || prevOptionLabels.length !== curOptionLabels.length;
            }}
            noStyle
        >
            {({ getFieldValue }) => {
                const questionType = getFieldValue('questionType');

                if (questionType?.name !== 'phone') {
                    return null;
                }

                return (
                    <Accordion
                        header="Settings"
                        headerClass="font-semibold sticky top-0 z-10"
                        keepMounted
                        isDefaultActive
                    >
                        <div className="flex flex-wrap">
                            <div className="flex w-full">
                                <Form.Item name={['settings', 'defaultCountryCode']} label="Default Country Code">
                                    <ComboSelect
                                        options={[
                                            { label: 'US', value: 'US' },
                                            { label: 'UK', value: 'GB' },
                                        ]}
                                        placeholder="Select Country Code"
                                        onChange={value => {
                                            questionBuilderForm.setFieldValue(
                                                ['questions', index, 'settings', 'defaultCountryCode'],
                                                value
                                            );
                                        }}
                                    />
                                </Form.Item>
                            </div>
                        </div>
                    </Accordion>
                );
            }}
        </Form.Item>
    );
};

export default CountryCodeAccordion;
