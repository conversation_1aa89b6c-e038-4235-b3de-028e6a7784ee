import { Form } from 'antd';

import ConfirmSelectRadio from '../Controls/ConfirmSelectRadio';

import EncryptCheckbox from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/EncryptCheckbox';
import QuestionDefaultValueInput from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/QuestionDefaultValueInput';
import QuestionLabelInput from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/QuestionLabelInput';
import RequiredSelectRadio from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/RequiredSelectRadio';
import InputType from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/InputType';
import OptionsType from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/OptionsType';
import { AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';

const GeneralAccordion = ({ index }: { index: number }) => (
    <Form.Item
        shouldUpdate={(prevValues, curValues) => {
            const { id: prevId, optionLabels: prevOptionLabels } = prevValues;
            const { id: curId, optionLabels: curOptionLabels } = curValues;

            return prevId !== curId || prevOptionLabels.length !== curOptionLabels.length;
        }}
        noStyle
    >
        {({ getFieldValue }) => {
            const questionType = getFieldValue('questionType');

            if (questionType?.isLayout) {
                return null;
            }

            return (
                <AccordionItem value="general">
                    <AccordionTrigger
                        className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                        iconPosition="left"
                    >
                        General
                    </AccordionTrigger>

                    <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                        <div className="flex w-full">
                            <RequiredSelectRadio index={index} />
                            <EncryptCheckbox index={index} />
                        </div>
                        {questionType?.name === 'stext' && <InputType index={index} />}
                        {(questionType.isAllowOptions || questionType.isAllowSites) && (
                            <OptionsType index={index} questionTypeName={questionType?.name} />
                        )}
                        <QuestionLabelInput index={index} />
                        {questionType.isAllowConfirm && <ConfirmSelectRadio index={index} />}
                        <QuestionDefaultValueInput index={index} />
                    </AccordionContent>
                </AccordionItem>
            );
        }}
    </Form.Item>
);

export default GeneralAccordion;
