import { Form, FormInstance } from 'antd';
import { findIndex } from 'lodash';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import NumberConditions from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/NumberConditions';
import QuestionConditions from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/QuestionConditions';
import { AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';

const ConditionalAccordion = ({ index }: { index: number }) => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
    };

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId, optionLabels: prevOptionLabels } = prevValues;
                const { id: curId, optionLabels: curOptionLabels } = curValues;

                return prevId !== curId || prevOptionLabels.length !== curOptionLabels.length;
            }}
            noStyle
        >
            {({ getFieldValue }) => {
                const parentId = getFieldValue('parentId');
                const parentIndex = findIndex(questionBuilderForm.getFieldsValue().questions, { id: parentId });

                if (parentId === null || parentIndex === -1) {
                    return null;
                }

                const parentQuestion = questionBuilderForm.getFieldValue(['questions', parentIndex]);
                const isQuestionCondition =
                    parentQuestion.questionType.isAllowChildren &&
                    (parentQuestion.questionType.isAllowOptions || parentQuestion.questionType.isAllowSites);
                const isNumberCondition =
                    parentQuestion.questionType.isAllowChildren && parentQuestion.questionType.isAllowNumber;

                return (
                    <>
                        {isNumberCondition || isQuestionCondition ? (
                            <AccordionItem value="fft-content">
                                <AccordionTrigger
                                    className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                    iconPosition="left"
                                >
                                    Conditionally Show When...
                                </AccordionTrigger>

                                <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                    {isNumberCondition && <NumberConditions index={index} />}
                                    {isQuestionCondition && <QuestionConditions index={index} />}
                                </AccordionContent>
                            </AccordionItem>
                        ) : null}
                    </>
                );
            }}
        </Form.Item>
    );
};

export default ConditionalAccordion;
