import { Form } from 'antd';
import { merge } from 'lodash';

import { cn } from '@/lib/utils';
import UserAgreementEditor from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/UserAgreementEditor';
import UserAgreementRadioPosition from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/UserAgreementRadioPosition';
import BackgroundColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BackgroundColor';
import Border from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Border';
import BorderColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BorderColor';
import BorderStyle from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/BorderStyle';
import FontColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontColor';
import FontSize from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontSize';
import FontWeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontWeight';
import Height from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Height';
import LineHeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/LineHeight';
import Margin from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Margin';
import Padding from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Padding';
import TextTransform from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/TextTransform';
import Width from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/Width';
import AdvancedAccordionHeader from '@/Pages/QuestionBuilder/Partials/Sidebar/AdvancedAccordionHeader';
import { useStoreState } from '@/store/hooks';
import { AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';

const AcceptTermsAccordion = ({ index }: { index: number }) => {
    const showAdvancedStyles = useStoreState(state => state.questionBuilderModel.showAdvancedStyles);

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId, optionLabels: prevOptionLabels } = prevValues;
                const { id: curId, optionLabels: curOptionLabels } = curValues;

                return prevId !== curId || prevOptionLabels.length !== curOptionLabels.length;
            }}
            noStyle
        >
            {({ getFieldValue }) => {
                const questionType = getFieldValue('questionType');
                const contentNamePath = ['settings', 'userAgreement', 'content'];
                const containerNamePath = ['settings', 'userAgreement', 'container'];

                if (questionType?.name !== 'acceptTerms') {
                    return null;
                }

                return (
                    <>
                        <AccordionItem value="acc-content">
                            <AccordionTrigger
                                className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                iconPosition="left"
                            >
                                Content
                            </AccordionTrigger>

                            <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                <UserAgreementRadioPosition index={index} />
                                <UserAgreementEditor index={index} />
                            </AccordionContent>
                        </AccordionItem>

                        <div className={cn({ hidden: !showAdvancedStyles })}>
                            <AccordionItem value="acc-content-styles">
                                <AccordionTrigger
                                    className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                    iconPosition="left"
                                >
                                    <AdvancedAccordionHeader header="Content Styles" />
                                </AccordionTrigger>

                                <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                    <FontSize namePath={merge([], [...contentNamePath])} label="Font Size" />
                                    <LineHeight namePath={merge([], [...contentNamePath])} label="Line Height" />
                                    <FontWeight namePath={merge([], [...contentNamePath])} label="Font Weight" />
                                    <TextTransform namePath={merge([], [...contentNamePath])} label="Font Transform" />
                                    <FontColor namePath={merge([], [...contentNamePath])} label="Font Color" />
                                </AccordionContent>
                            </AccordionItem>

                            <AccordionItem value="acc-container-styles">
                                <AccordionTrigger
                                    className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                                    iconPosition="left"
                                >
                                    <AdvancedAccordionHeader header="Container Styles" />
                                </AccordionTrigger>

                                <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                                    <Width namePath={merge([], [...containerNamePath])} label="Container" />
                                    <Height namePath={merge([], [...containerNamePath])} label="Container" />
                                    <Margin namePath={merge([], [...containerNamePath])} label="Container" />
                                    <Padding namePath={merge([], [...containerNamePath])} label="Container" />
                                    <BackgroundColor
                                        namePath={merge([], [...containerNamePath])}
                                        label="Container Background"
                                    />
                                    <Border namePath={merge([], [...containerNamePath])} label="Container Border" />
                                    <BorderStyle
                                        namePath={merge([], [...containerNamePath])}
                                        label="Container Border Style"
                                    />
                                    <BorderColor
                                        namePath={merge([], [...containerNamePath])}
                                        label="Container Border Color"
                                    />
                                </AccordionContent>
                            </AccordionItem>
                        </div>
                    </>
                );
            }}
        </Form.Item>
    );
};

export default AcceptTermsAccordion;
