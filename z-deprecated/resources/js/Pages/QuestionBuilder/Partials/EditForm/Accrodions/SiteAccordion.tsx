import { Form } from 'antd';

import SiteSelectOptions from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/SiteSelectOptions';
import { AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';

const SiteAccordion = ({ index }: { index: number }) => (
    <Form.Item
        shouldUpdate={(prevValues, curValues) => {
            const { id: prevId, optionLabels: prevOptionLabels } = prevValues;
            const { id: curId, optionLabels: curOptionLabels } = curValues;

            return prevId !== curId || prevOptionLabels.length !== curOptionLabels.length;
        }}
        noStyle
    >
        {({ getFieldValue }) => {
            const questionType = getFieldValue('questionType');

            if (!questionType?.isAllowSites) {
                return null;
            }

            return (
                <AccordionItem value="sites">
                    <AccordionTrigger
                        className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                        iconPosition="left"
                    >
                        Sites
                    </AccordionTrigger>

                    <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                        <SiteSelectOptions index={index} />
                    </AccordionContent>
                </AccordionItem>
            );
        }}
    </Form.Item>
);

export default SiteAccordion;
