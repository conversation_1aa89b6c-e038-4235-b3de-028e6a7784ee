import { Form, FormInstance, InputNumber, Radio, RadioChangeEvent } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import { AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';

const DBOAccordion = ({ index }: { index: number }) => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as { questionBuilderForm: FormInstance };

    return (
        <Form.Item
            shouldUpdate={(prevValues, curValues) => {
                const { id: prevId, optionLabels: prevOptionLabels } = prevValues;
                const { id: curId, optionLabels: curOptionLabels } = curValues;

                return prevId !== curId || prevOptionLabels.length !== curOptionLabels.length;
            }}
            noStyle
        >
            {({ getFieldValue }) => {
                const questionType = getFieldValue('questionType');

                if (questionType?.name !== 'dob') {
                    return null;
                }

                return (
                    <AccordionItem value="dob-settings">
                        <AccordionTrigger
                            className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                            iconPosition="left"
                        >
                            Settings
                        </AccordionTrigger>

                        <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                            <div className="flex flex-wrap">
                                <div className="flex w-full">
                                    <Form.Item name={['settings', 'dateInputs']}>
                                        <Radio.Group
                                            className="grid w-full grid-cols-2 gap-x-5 gap-y-2"
                                            onChange={(e: RadioChangeEvent) => {
                                                questionBuilderForm.setFieldValue(
                                                    ['questions', index, 'settings', 'dateInputs'],
                                                    e.target.value
                                                );
                                            }}
                                        >
                                            <Radio key="show-full-date" value="showFullDate">
                                                Show Full Date
                                            </Radio>
                                            <Radio key="show-year-only" value="showYearOnly">
                                                Show Year Only
                                            </Radio>
                                            <Radio key="show-month-day-date" value="showMonthDayDate">
                                                Show Month & Day
                                            </Radio>
                                            <Radio key="show-month-year-date" value="showMonthYearDate">
                                                Show Month & Year
                                            </Radio>
                                        </Radio.Group>
                                    </Form.Item>
                                </div>
                                <div className="flex w-full">
                                    <Form.Item
                                        name={['settings', 'minAge']}
                                        label="Minimum Age"
                                        help="If left blank, the referral will not be limited on what year they can enter."
                                    >
                                        <InputNumber className="w-20" placeholder="age" />
                                    </Form.Item>
                                </div>
                            </div>
                        </AccordionContent>
                    </AccordionItem>
                );
            }}
        </Form.Item>
    );
};

export default DBOAccordion;
