import { Form } from 'antd';
import { merge } from 'lodash';

import BMIHeightPosition from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/BMIHeightPosition';
import BMIOptionSelect from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/BMIOptionSelect';
import ItemTextInput from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/ItemTextInput';
import FontColor from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontColor';
import FontSize from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontSize';
import FontWeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/FontWeight';
import LineHeight from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/LineHeight';
import TextTransform from '@/Pages/QuestionBuilder/Partials/SettingsForm/Controls/TextTransform';
import { AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';

const BMIAccordion = ({ index }: { index: number }) => (
    <Form.Item
        shouldUpdate={(prevValues, curValues) => {
            const { id: prevId, optionLabels: prevOptionLabels } = prevValues;
            const { id: curId, optionLabels: curOptionLabels } = curValues;

            return prevId !== curId || prevOptionLabels.length !== curOptionLabels.length;
        }}
        noStyle
    >
        {({ getFieldValue }) => {
            const questionType = getFieldValue('questionType');

            if (questionType?.name !== 'calcBMI') {
                return null;
            }

            return (
                <AccordionItem value="bmi-settings">
                    <AccordionTrigger
                        className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                        iconPosition="left"
                    >
                        Settings
                    </AccordionTrigger>

                    <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                        <div className="flex flex-wrap">
                            <div className="flex w-full">
                                <BMIOptionSelect index={index} />
                                <BMIHeightPosition index={index} />
                            </div>
                            <div className="mt-4 flex w-full gap-x-4 divide-x">
                                <div className="w-1/2">
                                    <div className="mb-3 border-b border-gray-200 pb-2 text-center text-base font-semibold">
                                        Height
                                    </div>
                                    <ItemTextInput
                                        index={index}
                                        namePath={['settings', 'bmi', 'heightLabel', 'label']}
                                        label="Label"
                                    />
                                    <FontSize
                                        namePath={merge([], ['settings', 'bmi', 'heightLabel'])}
                                        label="Label Font Size"
                                    />
                                    <LineHeight
                                        namePath={merge([], ['settings', 'bmi', 'heightLabel'])}
                                        label="Label Line Height"
                                    />
                                    <FontWeight
                                        namePath={merge([], ['settings', 'bmi', 'heightLabel'])}
                                        label="Label Font Weight"
                                    />
                                    <TextTransform
                                        namePath={merge([], ['settings', 'bmi', 'heightLabel'])}
                                        label="Label Font Transform"
                                    />
                                    <FontColor
                                        namePath={merge([], ['settings', 'bmi', 'heightLabel'])}
                                        label="Label Font Color"
                                    />
                                </div>
                                <div className="w-1/2 pl-4">
                                    <div className="mb-3 border-b border-gray-200 pb-2 text-center text-base font-semibold">
                                        Weight
                                    </div>
                                    <ItemTextInput
                                        index={index}
                                        namePath={['settings', 'bmi', 'weightLabel', 'label']}
                                        label="Label"
                                    />
                                    <FontSize
                                        namePath={merge([], ['settings', 'bmi', 'weightLabel'])}
                                        label="Label Font Size"
                                    />
                                    <LineHeight
                                        namePath={merge([], ['settings', 'bmi', 'weightLabel'])}
                                        label="Label Line Height"
                                    />
                                    <FontWeight
                                        namePath={merge([], ['settings', 'bmi', 'weightLabel'])}
                                        label="Label Font Weight"
                                    />
                                    <TextTransform
                                        namePath={merge([], ['settings', 'bmi', 'weightLabel'])}
                                        label="Label Font Transform"
                                    />
                                    <FontColor
                                        namePath={merge([], ['settings', 'bmi', 'weightLabel'])}
                                        label="Label Font Color"
                                    />
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>
            );
        }}
    </Form.Item>
);

export default BMIAccordion;
