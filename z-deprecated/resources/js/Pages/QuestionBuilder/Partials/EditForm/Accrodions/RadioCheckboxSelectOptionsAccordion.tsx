import { Form } from 'antd';

import SingleMultiSelectOptions from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/SingleMultiSelectOptions';
import { AccordionContent, AccordionItem, AccordionTrigger } from '@/UI-Kit/Shadcn/accordion';

const RadioCheckboxSelectOptionsAccordion = ({ index }: { index: number }) => (
    <Form.Item
        shouldUpdate={(prevValues, curValues) => {
            const { id: prevId, optionLabels: prevOptionLabels } = prevValues;
            const { id: curId, optionLabels: curOptionLabels } = curValues;

            return prevId !== curId || prevOptionLabels.length !== curOptionLabels.length;
        }}
        noStyle
    >
        {({ getFieldValue }) => {
            const questionType = getFieldValue('questionType');

            if (!questionType?.isAllowOptions) {
                return null;
            }

            return (
                <AccordionItem value="options">
                    <AccordionTrigger
                        className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 px-5 font-semibold"
                        iconPosition="left"
                    >
                        Options
                    </AccordionTrigger>

                    <AccordionContent forceMount className="flex flex-col gap-y-4 p-4">
                        <SingleMultiSelectOptions index={index} />
                    </AccordionContent>
                </AccordionItem>
            );
        }}
    </Form.Item>
);

export default RadioCheckboxSelectOptionsAccordion;
