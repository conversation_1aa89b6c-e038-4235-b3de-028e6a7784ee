import { Fragment, useEffect, useMemo } from 'react';

import { Form, FormInstance } from 'antd';
import { omit } from 'lodash';

import FreeFormTextAccordion from './Accrodions/FreeFormTextAccordion';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import useEditFormActions from '@/hooks/QuestionBuilder/useEditFormActions';
import { cn } from '@/lib/utils';
import AcceptTermsAccordion from '@/Pages/QuestionBuilder/Partials/EditForm/Accrodions/AcceptTermsAccordion';
import BMIAccordion from '@/Pages/QuestionBuilder/Partials/EditForm/Accrodions/BMIAccordion';
import ConditionalAccordion from '@/Pages/QuestionBuilder/Partials/EditForm/Accrodions/ConditionalAccordion';
import CountryCodeAccordion from '@/Pages/QuestionBuilder/Partials/EditForm/Accrodions/CountryCodeAccordion';
import DBOAccordion from '@/Pages/QuestionBuilder/Partials/EditForm/Accrodions/DOBAccordion';
import GeneralAccordion from '@/Pages/QuestionBuilder/Partials/EditForm/Accrodions/GeneralAccordion';
import RadioCheckboxSelectOptionsAccordion from '@/Pages/QuestionBuilder/Partials/EditForm/Accrodions/RadioCheckboxSelectOptionsAccordion';
import SiteAccordion from '@/Pages/QuestionBuilder/Partials/EditForm/Accrodions/SiteAccordion';
import HiddenFields from '@/Pages/QuestionBuilder/Partials/EditForm/Controls/HiddenFields';
import FormItemStyles from '@/Pages/QuestionBuilder/Partials/EditForm/StyleGroups/FormItemStyes';
import ItemCheckboxGroupStyles from '@/Pages/QuestionBuilder/Partials/EditForm/StyleGroups/ItemCheckboxGroupStyles';
import ItemInputStyles from '@/Pages/QuestionBuilder/Partials/EditForm/StyleGroups/ItemInputStyles';
import ItemLabelStyles from '@/Pages/QuestionBuilder/Partials/EditForm/StyleGroups/ItemLabelStyles';
import ItemMultiScreenStyles from '@/Pages/QuestionBuilder/Partials/EditForm/StyleGroups/ItemMultiScreenStyles';
import ItemNextButtonStyles from '@/Pages/QuestionBuilder/Partials/EditForm/StyleGroups/ItemNextButtonStyles';
import ItemRadioGroupStyles from '@/Pages/QuestionBuilder/Partials/EditForm/StyleGroups/ItemRadioGroupStyles';
import ItemSubmitButtonStyles from '@/Pages/QuestionBuilder/Partials/EditForm/StyleGroups/ItemSubmitButtonStyles';
import CardWrapper from '@/Pages/QuestionBuilder/Partials/Sidebar/CardWrapper';
import EditFormTitle from '@/Pages/QuestionBuilder/Partials/Sidebar/EditFormTitle';
import TransitionWrapper from '@/Pages/QuestionBuilder/Partials/Sidebar/TransitionWrapper';
import { useStoreActions, useStoreState } from '@/store/hooks';
import Button from '@/UI-Kit/Button';
import { Accordion } from '@/UI-Kit/Shadcn/accordion';

const EditForm = () => {
    const editIndex = useStoreState(state => state.questionBuilderModel.editIndex) as number;
    const setFormErrors = useStoreActions(actions => actions.questionBuilderModel.setFormErrors);
    const showSidebar = useMemo(() => editIndex !== null, [editIndex]);
    const { editQuestionBuilderForm } = useQuestionBuilderFormContext() as {
        editQuestionBuilderForm: FormInstance;
    };
    const { cancelEditForm, handleFormValuesChange, saveToQuestionBuilderForm } = useEditFormActions();

    useEditUseEffect(editIndex);

    return (
        <TransitionWrapper showSidebar={showSidebar}>
            <div>
                {editIndex !== null ? (
                    <Form
                        key="edit-question-form"
                        form={editQuestionBuilderForm}
                        name="editQuestionForm"
                        layout="vertical"
                        size="small"
                        onFinish={formData => {
                            saveToQuestionBuilderForm(formData);
                        }}
                        onFinishFailed={({ errorFields }) => {
                            setFormErrors({ formErrors: errorFields, editIndex });
                        }}
                        onValuesChange={() => {
                            handleFormValuesChange();
                        }}
                    >
                        <CardWrapper
                            cardTitle={<EditFormTitle />}
                            cardActions={[
                                <Fragment key="save-close-edit-buttons">
                                    <Button type="submit" key="save-btn" size="mini" label="Save" color="green" />
                                    <Button
                                        type="button"
                                        key="close-btn"
                                        buttonType="link"
                                        className="hover:bg-transparent"
                                        size="mini"
                                        label="cancel"
                                        color="gray"
                                        onClick={() => {
                                            cancelEditForm();
                                        }}
                                    />
                                </Fragment>,
                            ]}
                        >
                            <Accordion
                                type="multiple"
                                className={cn('w-full border border-b-0 border-gray-100 [&_h3]:mb-0')}
                                defaultValue={['general', 'multi-screen']}
                            >
                                <GeneralAccordion index={editIndex} />
                                <AcceptTermsAccordion index={editIndex} />
                                <FreeFormTextAccordion index={editIndex} />
                                <RadioCheckboxSelectOptionsAccordion index={editIndex} />
                                <SiteAccordion index={editIndex} />
                                <BMIAccordion index={editIndex} />
                                <DBOAccordion index={editIndex} />
                                <CountryCodeAccordion index={editIndex} />
                                <ConditionalAccordion index={editIndex} />
                                <FormItemStyles />
                                <ItemLabelStyles />
                                <ItemRadioGroupStyles />
                                <ItemCheckboxGroupStyles />
                                <ItemInputStyles />
                                <ItemMultiScreenStyles />
                                <ItemNextButtonStyles />
                                <ItemSubmitButtonStyles />
                                <HiddenFields />
                            </Accordion>
                        </CardWrapper>
                    </Form>
                ) : null}
            </div>
        </TransitionWrapper>
    );
};

export default EditForm;

const useEditUseEffect = (editIndex: number | null) => {
    const { editQuestionBuilderForm, questionBuilderForm } = useQuestionBuilderFormContext() as {
        editQuestionBuilderForm: FormInstance;
        questionBuilderForm: FormInstance;
    };

    useEffect(() => {
        if (editIndex !== null) {
            const questionFields = questionBuilderForm.getFieldValue(['questions', editIndex]);
            questionFields.json = JSON.stringify(omit(questionFields, ['json']));

            /** Reset arrays and objects so they don't merge with the previous question */
            editQuestionBuilderForm?.setFieldsValue({
                conditionOptions: undefined,
                conditionSource: undefined,
                optionLabels: undefined,
                options: undefined,
                question: undefined,
                questionType: undefined,
                rawOptions: undefined,
                settings: undefined,
            });
            editQuestionBuilderForm?.setFieldsValue({ ...questionFields });
        }
    }, [editIndex]);
};
