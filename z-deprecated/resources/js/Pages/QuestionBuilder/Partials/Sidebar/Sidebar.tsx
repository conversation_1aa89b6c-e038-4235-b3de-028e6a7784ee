import EditForm from '@/Pages/QuestionBuilder/Partials/EditForm';
import QuestionTypes from '@/Pages/QuestionBuilder/Partials/QuestionTypes';
import SettingsForm from '@/Pages/QuestionBuilder/Partials/SettingsForm';
import { Card, CardContent } from '@/UI-Kit/Shadcn/card';

const Sidebar = () => (
    <div className="w-4/12">
        <div className="sticky top-[135px] mb-5 pb-5">
            <Card>
                <CardContent className="min-h-[300px] p-0">
                    <SettingsForm />
                    <QuestionTypes />
                    <EditForm />
                </CardContent>
            </Card>
        </div>
    </div>
);

export default Sidebar;
