import { ReactNode, useId } from 'react';

import { useStoreActions, useStoreState } from '@/store/hooks';
import Button from '@/UI-Kit/Button';
import { Card, CardAction, CardContent, CardFooter, CardHeader, CardTitle } from '@/UI-Kit/Shadcn/card';

type Props = {
    children: ReactNode;
    cardTitle: ReactNode | string;
    cardActions: ReactNode[];
};

const CardWrapper = ({ children, cardTitle, cardActions }: Props) => {
    const setShowAdvancedStyles = useStoreActions(actions => actions.questionBuilderModel.setShowAdvancedStyles);
    const showAdvancedStyles = useStoreState(state => state.questionBuilderModel.showAdvancedStyles);
    const { addAfterQuestion } = useStoreState(state => state.questionBuilderModel.addQuestionSettings);

    return (
        <Card key={useId()} className="mb-0 border-0 bg-transparent">
            <CardHeader border="none">
                <CardTitle>{cardTitle}</CardTitle>
                <CardAction>{cardActions}</CardAction>
            </CardHeader>

            <CardContent className="max-h-[600px] min-h-[300px] overflow-y-auto p-0">{children}</CardContent>

            <CardFooter>
                {addAfterQuestion === null && (
                    <Button
                        key="basic-advanced-styles"
                        data-testid="advanced-styles-button"
                        type="button"
                        label={showAdvancedStyles ? 'Hide Advanced Styles' : 'Show Advanced Styles'}
                        color={showAdvancedStyles ? 'amber' : 'gray'}
                        size="mini"
                        className="self-end"
                        onClick={() => setShowAdvancedStyles(!showAdvancedStyles)}
                    />
                )}
            </CardFooter>
        </Card>
    );
};

export default CardWrapper;
