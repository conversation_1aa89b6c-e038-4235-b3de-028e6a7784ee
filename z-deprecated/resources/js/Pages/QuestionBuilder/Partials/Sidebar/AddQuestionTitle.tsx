import { useStoreState } from '@/store/hooks';

const AddQuestionTitle = () => {
    const { addAfterQuestion, isChild } = useStoreState(state => state.questionBuilderModel.addQuestionSettings);

    return (
        <>
            <div className="text-sm">Add Question to Builder</div>
            <div className="text-xs">
                {addAfterQuestion?.questionType && Object.keys(addAfterQuestion?.questionType).length > 0
                    ? isChild
                        ? `As nested question of ${addAfterQuestion?.questionType.alias}`
                        : `Below ${addAfterQuestion?.questionType.alias}`
                    : ''}
            </div>
        </>
    );
};

export default AddQuestionTitle;
