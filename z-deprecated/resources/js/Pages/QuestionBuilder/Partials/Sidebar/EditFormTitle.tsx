import { FormInstance } from 'antd';

import { LanguageService } from '@/Components/Languages/services/languageService';
import { LanguageCode } from '@/Components/Languages/types/languages';
import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import { useStoreState } from '@/store/hooks';

const EditFormTitle = () => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as { questionBuilderForm: FormInstance };
    const editIndex = useStoreState(state => state.questionBuilderModel.editIndex) as number;
    const languageCode = useStoreState(state => state.questionBuilderModel.languageCode);
    const questionType = questionBuilderForm?.getFieldValue(['questions', editIndex, 'questionType']);
    const { label: languageLabel } = LanguageService.getLanguageObject(languageCode as LanguageCode);

    return (
        <>
            <div className="text-sm">{questionType?.alias ?? ''}</div>
            <div className="text-xs">({languageLabel})</div>
        </>
    );
};

export default EditFormTitle;
