import StyleCountAccordionHeader from '@/Pages/QuestionBuilder/Partials/Sidebar/StyleCountAccordionHeader';

const AdvancedAccordionHeader = ({ header, styleCount }: { header: string; styleCount?: number }) => (
    <>
        <StyleCountAccordionHeader header={header} styleCount={styleCount as number} />
        <div className="text-xs font-normal text-amber-700">advanced</div>
    </>
);

export default AdvancedAccordionHeader;
