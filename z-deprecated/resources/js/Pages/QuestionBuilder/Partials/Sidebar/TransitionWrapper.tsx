import { Fragment, ReactNode } from 'react';

import { Transition } from '@headlessui/react';

type Props = {
    children: ReactNode;
    showSidebar: boolean;
};

const TransitionWrapper = ({ children, showSidebar }: Props) => (
    <Transition.Root appear show={showSidebar} as={Fragment}>
        <Transition.Child
            as={Fragment}
            unmount={false}
            enter="transform transition-all ease-in duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transform transition-all ease-out duration-100"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
        >
            {children}
        </Transition.Child>
    </Transition.Root>
);

export default TransitionWrapper;
