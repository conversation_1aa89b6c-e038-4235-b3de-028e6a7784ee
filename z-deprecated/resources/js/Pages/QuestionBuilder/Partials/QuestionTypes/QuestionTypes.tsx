import { useMemo } from 'react';

import QuestionTypeItem from '@/Pages/QuestionBuilder/Partials/QuestionTypes/QuestionTypeItem';
import AddQuestionTitle from '@/Pages/QuestionBuilder/Partials/Sidebar/AddQuestionTitle';
import CardWrapper from '@/Pages/QuestionBuilder/Partials/Sidebar/CardWrapper';
import TransitionWrapper from '@/Pages/QuestionBuilder/Partials/Sidebar/TransitionWrapper';
import { useStoreActions, useStoreState } from '@/store/hooks';
import Accordion from '@/UI-Kit/Accordion';
import Button from '@/UI-Kit/Button';

// TODO: @Miguel - this file is still using the original Accordion. Please switch it out.
const QuestionTypes = () => {
    const questionTypes = useStoreState(state => state.questionBuilderModel.questionTypes);
    const { addAfterQuestion, parentQuestion, isChild } = useStoreState(
        state => state.questionBuilderModel.addQuestionSettings
    );
    const setAddQuestionSettings = useStoreActions(actions => actions.questionBuilderModel.setAddQuestionSettings);
    const showSidebar = useMemo(() => addAfterQuestion !== null, [addAfterQuestion?.id]);

    return (
        <TransitionWrapper showSidebar={showSidebar}>
            <div>
                <CardWrapper
                    cardTitle={<AddQuestionTitle />}
                    cardActions={[
                        <Button
                            type="button"
                            key="close-add-question-btn"
                            buttonType="link"
                            className="hover:bg-transparent"
                            size="mini"
                            label="close"
                            color="gray"
                            onClick={() => {
                                setAddQuestionSettings({
                                    addAfterQuestion: null,
                                    parentQuestion: null,
                                    isChild: false,
                                });
                            }}
                        />,
                    ]}
                >
                    <div className="flex flex-wrap items-start">
                        <div className="w-full">
                            <Accordion header="Predefined" headerClass="font-semibold" isDefaultActive>
                                <div className="grid grid-cols-3 gap-x-3 gap-y-3">
                                    {questionTypes
                                        .map(questionType => {
                                            if (
                                                questionType.isPredefined &&
                                                addAfterQuestion &&
                                                !questionType.isLayout
                                            ) {
                                                return (
                                                    <QuestionTypeItem
                                                        key={`question-type-${questionType.uuid}`}
                                                        questionType={questionType}
                                                        addAfterQuestionId={addAfterQuestion.id}
                                                        parentId={parentQuestion?.id ?? null}
                                                        isChild={isChild}
                                                    />
                                                );
                                            }

                                            return false;
                                        })
                                        .filter(Boolean)}
                                </div>
                            </Accordion>
                        </div>
                        <div className="w-full">
                            <Accordion header="Standard Fields" headerClass="font-semibold" isDefaultActive>
                                <div className="grid grid-cols-3 gap-x-3 gap-y-3">
                                    {questionTypes
                                        .map(questionType => {
                                            if (
                                                !questionType.isPredefined &&
                                                addAfterQuestion &&
                                                !questionType.isLayout
                                            ) {
                                                return (
                                                    <QuestionTypeItem
                                                        key={`question-type-${questionType.uuid}`}
                                                        questionType={questionType}
                                                        addAfterQuestionId={addAfterQuestion.id}
                                                        parentId={parentQuestion?.id ?? null}
                                                        isChild={isChild}
                                                    />
                                                );
                                            }

                                            return false;
                                        })
                                        .filter(Boolean)}
                                </div>
                            </Accordion>
                        </div>

                        <div className="w-full">
                            <Accordion header="Layout" headerClass="font-semibold" isDefaultActive>
                                <div className="grid grid-cols-3 gap-x-3 gap-y-3">
                                    {questionTypes
                                        .map(questionType => {
                                            if (
                                                addAfterQuestion &&
                                                questionType.isLayout &&
                                                !['startPaging', 'endPaging'].includes(questionType.name)
                                            ) {
                                                return (
                                                    <QuestionTypeItem
                                                        key={`question-type-${questionType.uuid}`}
                                                        questionType={questionType}
                                                        addAfterQuestionId={addAfterQuestion.id}
                                                        parentId={parentQuestion?.id ?? null}
                                                        isChild={isChild}
                                                    />
                                                );
                                            }

                                            return false;
                                        })
                                        .filter(Boolean)}
                                </div>
                            </Accordion>
                        </div>
                    </div>
                </CardWrapper>
            </div>
        </TransitionWrapper>
    );
};

export default QuestionTypes;
