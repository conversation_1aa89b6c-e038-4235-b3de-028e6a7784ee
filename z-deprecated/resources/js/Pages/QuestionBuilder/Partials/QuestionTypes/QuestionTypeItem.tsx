import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import useDndActions from '@/hooks/QuestionBuilder/useDndActions';
import useQuestionTypeIcons from '@/hooks/QuestionBuilder/useQuestionTypeIcons';
import { UUID } from '@/types/general';
import { QuestionType } from '@/types/question-types';
import Button from '@/UI-Kit/Button';

interface Props {
    addAfterQuestionId: UUID | null;
    isChild: boolean;
    parentId: UUID | null;
    questionType: QuestionType;
}

const QuestionTypeItem = ({ questionType, addAfterQuestionId, parentId, isChild = false }: Props) => {
    const { handleAddNewQuestion } = useDndActions();
    const { getQuestionTypeIcon } = useQuestionTypeIcons();

    return (
        <Button
            type="button"
            color="gray"
            buttonType="outline"
            size="mini"
            label=""
            disabled={questionType.isDisabled}
            className="group flex! min-h-[90px] flex-col items-stretch justify-around rounded-md! border! hover:border-gray-900! hover:bg-gray-900! focus:border-gray-900! focus:bg-gray-900!"
            onClick={() => handleAddNewQuestion(questionType, addAfterQuestionId, parentId, isChild)}
        >
            <div className="text-center text-2xl text-black group-hover:text-gray-200! group-focus:text-gray-200!">
                <FontAwesomeIcon icon={getQuestionTypeIcon(questionType.name)} fixedWidth className="text-xl" />
            </div>
            <div className="text-center text-xs text-gray-900 group-hover:text-gray-100! group-focus:text-gray-100!">
                {questionType.label}
            </div>
        </Button>
    );
};

export default QuestionTypeItem;
