import { useMemo } from 'react';
import { createPortal } from 'react-dom';

import { FormInstance } from 'antd';

import { DragOverlay, useDndContext } from '@dnd-kit/core';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import SortableTreeItem from '@/Pages/QuestionBuilder/Partials/Draggable/SortableTreeItem';
import { getChildCount } from '@/utils/QuestionBuilder/children';
import { dropAnimationConfig } from '@/utils/QuestionBuilder/drop-animation';

const DraggableOverlay = () => {
    const { active } = useDndContext();
    const { questionBuilderForm } = useQuestionBuilderFormContext() as { questionBuilderForm: FormInstance };
    const flattenedQuestions = questionBuilderForm.getFieldsValue().questions;
    const activeItem = active?.id ? flattenedQuestions?.find(({ id }: { id: string }) => id === active?.id) : null;
    const childCount = useMemo(
        () => (active?.id ? getChildCount(flattenedQuestions, active.id) + 1 : undefined),
        [active?.id]
    );

    const component =
        active && activeItem ? (
            <SortableTreeItem
                id={active?.id}
                level={activeItem.level}
                index={0}
                clone
                childCount={childCount}
                value={activeItem.questionLabel}
                question={activeItem}
            />
        ) : null;

    return createPortal(<DragOverlay dropAnimation={dropAnimationConfig}>{component}</DragOverlay>, document.body);
};

export default DraggableOverlay;
