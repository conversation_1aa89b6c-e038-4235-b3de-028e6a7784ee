import { CSSProperties } from 'react';

import type { UniqueIdentifier } from '@dnd-kit/core';
import { AnimateLayoutChanges, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import TreeItem, { TreeItemProps } from '@/Pages/QuestionBuilder/Partials/Draggable/TreeItem';
import { ModifiedQuestion } from '@/types/question';
import iOS from '@/utils/QuestionBuilder/ios';

interface Props extends TreeItemProps {
    id: UniqueIdentifier;
    level: number;
    question: ModifiedQuestion;
}

const animateLayoutChanges: AnimateLayoutChanges = ({ isSorting, wasDragging }) => !(isSorting || wasDragging);

const SortableTreeItem = ({ id, level, question, ...props }: Props) => {
    const { isDragging, isSorting, isOver, setDraggableNodeRef, setDroppableNodeRef, transform, transition } =
        useSortable({
            id,
            animateLayoutChanges,
        });

    const style: CSSProperties = {
        transform: CSS.Translate.toString(transform),
        transition,
    };

    return (
        <TreeItem
            ref={setDraggableNodeRef}
            wrapperRef={setDroppableNodeRef}
            style={style}
            level={level}
            isDragging={isDragging}
            isOver={isOver}
            disableSelection={iOS}
            disableInteraction={isSorting}
            question={question}
            {...props}
        />
    );
};

export default SortableTreeItem;
