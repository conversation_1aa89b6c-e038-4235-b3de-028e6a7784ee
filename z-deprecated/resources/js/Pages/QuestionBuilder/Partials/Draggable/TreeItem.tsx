import { CSSProperties, forwardRef, HTMLAttributes } from 'react';

import { truncate } from 'lodash';

import { cn } from '@/lib/utils';
import styles from '@/Pages/QuestionBuilder/Partials/Draggable/TreeItem.module.scss';
import { useStoreState } from '@/store/hooks';
import { ModifiedQuestion } from '@/types/question';
import removeTags from '@/utils/removeTags';

export interface TreeItemProps extends Omit<HTMLAttributes<HTMLLIElement>, 'id'> {
    childCount?: number;
    clone?: boolean;
    collapsed?: boolean;
    level: number;
    disableInteraction?: boolean;
    disableSelection?: boolean;
    isDragging?: boolean;
    isOver?: boolean;
    indicator?: boolean;
    value: string;
    index: number;
    question: ModifiedQuestion;

    onCollapse?(): void;

    onEdit?(): void;

    wrapperRef?(node: HTMLLIElement): void;
}

const TreeItem = forwardRef<HTMLDivElement, TreeItemProps>(
    (
        {
            childCount,
            clone,
            level,
            disableSelection,
            disableInteraction,
            isDragging,
            isOver,
            indicator,
            onEdit,
            style,
            value,
            wrapperRef,
            question,
            ...props
        },
        ref
    ) => {
        const indentationWidth = useStoreState(state => state.questionBuilderModel.indentationWidth);

        return (
            <li
                className={cn(
                    styles.Wrapper,
                    clone && styles.clone,
                    isDragging && styles.ghost,
                    indicator && styles.indicator,
                    disableSelection && styles.disableSelection,
                    disableInteraction && styles.disableInteraction
                )}
                ref={wrapperRef}
                style={
                    {
                        '--indent-spacing': `${indentationWidth * level}px`,
                    } as CSSProperties
                }
                {...props}
            >
                <div
                    className={cn(styles.TreeItem, {
                        'flex-wrap': !isDragging,
                        'flex-no-wrap': isDragging,
                        'bg-gray-200!': isOver && question.questionType.isAllowChildren,
                        'bg-amber-100!': isOver && !question.questionType.isAllowChildren,
                    })}
                    ref={ref}
                    style={style}
                >
                    <span className={styles.Text}>{truncate(removeTags(value ?? null) ?? 'None', { length: 40 })}</span>
                    {!clone && onEdit && (
                        <button type="button" onClick={onEdit}>
                            Edit
                        </button>
                    )}
                    {clone && childCount && childCount > 1 ? <span className={styles.Count}>{childCount}</span> : null}
                </div>
            </li>
        );
    }
);

TreeItem.displayName = 'TreeItem';

export default TreeItem;
