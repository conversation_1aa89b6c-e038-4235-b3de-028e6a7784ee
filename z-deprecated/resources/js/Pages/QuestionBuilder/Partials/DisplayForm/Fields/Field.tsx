import React, { forwardRef, HTMLAttributes } from 'react';

import { cn } from '@/lib/utils';
import AddQuestion from '@/Pages/QuestionBuilder/Partials/Actions/AddQuestion';
import AddingQuestionIndicator from '@/Pages/QuestionBuilder/Partials/DisplayForm/Fields/AddingQuestionIndicator';
import styles from '@/Pages/QuestionBuilder/Partials/DisplayForm/Fields/Field.module.scss';
import FieldActions from '@/Pages/QuestionBuilder/Partials/DisplayForm/Fields/FieldActions';
import FieldItem from '@/Pages/QuestionBuilder/Partials/DisplayForm/Fields/FieldItem';
import LevelIndicator from '@/Pages/QuestionBuilder/Partials/DisplayForm/Fields/LevelIndicator';
import { useStoreState } from '@/store/hooks';
import { ANY_TODO } from '@/types/general';
import { ModifiedQuestion } from '@/types/question';

export interface FieldProps extends Omit<HTMLAttributes<HTMLLIElement>, 'id'> {
    level: number;
    disableInteraction?: boolean;
    disableSelection?: boolean;
    index: number;
    isDragging?: boolean;
    isOver?: boolean;
    handleProps?: ANY_TODO;
    indicator?: boolean;
    question: ModifiedQuestion;

    wrapperRef?(node: HTMLLIElement): void;
}

const Field = forwardRef<HTMLDivElement, FieldProps>(
    (
        {
            level,
            disableSelection,
            disableInteraction,
            index,
            isDragging,
            isOver,
            handleProps,
            indicator,
            style,
            wrapperRef,
            question,
            ...props
        },
        ref
    ) => {
        const editIndex = useStoreState(state => state.questionBuilderModel.editIndex);
        const indentationWidth = useStoreState(state => state.questionBuilderModel.indentationWidth);
        const isNewQuestion = useStoreState(state => state.questionBuilderModel.isNewQuestion);
        const languageCode = useStoreState(state => state.questionBuilderModel.languageCode);
        const { addAfterQuestion } = useStoreState(state => state.questionBuilderModel.addQuestionSettings);
        const { isAllowChildren } = question.questionType;

        return (
            <li
                className={cn(
                    styles.Wrapper,
                    isDragging && styles.ghost,
                    indicator && styles.indicator,
                    disableSelection && styles.disableSelection,
                    disableInteraction && styles.disableInteraction,
                    { 'h-0 opacity-0': question.hidden }
                )}
                ref={wrapperRef}
                style={
                    {
                        '--indent-spacing': `${indentationWidth * level}px`,
                    } as React.CSSProperties
                }
                {...props}
            >
                <div
                    className={cn('group relative flex items-center gap-2', {
                        'flex-wrap': !question.parentId,
                    })}
                >
                    <LevelIndicator level={level} isDragging={isDragging} />
                    <div
                        id={`field-item-${question.id}`}
                        className={cn(
                            styles.Field,
                            isOver && !isAllowChildren ? styles.noChildrenAllowed : '',
                            'mt-3 mb-3 w-full rounded-sm border-2',
                            {
                                'flex-wrap': !isDragging,
                                'border-transparent bg-transparent group-hover:border-solid group-hover:border-gray-200 group-hover:bg-gray-50':
                                    editIndex !== index,
                                'border-solid border-green-500 bg-green-50': editIndex === index,
                                'border-solid! border-gray-200! bg-gray-50!': question.id === addAfterQuestion?.id,
                                'border-dashed border-red-400 bg-red-100! opacity-50':
                                    isOver && !isAllowChildren && isNewQuestion,
                            }
                        )}
                        ref={ref}
                        style={style}
                    >
                        <div className="m-2 w-full">
                            <FieldItem index={index} question={question} />
                        </div>
                        <FieldActions handleProps={handleProps} index={index} question={question} />
                        {editIndex === null && languageCode === 'en' && !question.parentId && (
                            <AddQuestion question={question} />
                        )}
                    </div>
                </div>
                <AddingQuestionIndicator question={question} level={level} />
            </li>
        );
    }
);

Field.displayName = 'Field';

export default Field;
