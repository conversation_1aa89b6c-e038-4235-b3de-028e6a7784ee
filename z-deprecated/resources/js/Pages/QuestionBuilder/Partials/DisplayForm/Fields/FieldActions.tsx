import useDndActions from '@/hooks/QuestionBuilder/useDndActions';
import useEditFormActions from '@/hooks/QuestionBuilder/useEditFormActions';
import { cn } from '@/lib/utils';
import AddChildQuestion from '@/Pages/QuestionBuilder/Partials/Actions/AddChildQuestion';
import Edit from '@/Pages/QuestionBuilder/Partials/Actions/Edit';
import Handle from '@/Pages/QuestionBuilder/Partials/Actions/Handle';
import Remove from '@/Pages/QuestionBuilder/Partials/Actions/Remove';
import { useStoreState } from '@/store/hooks';
import { ANY_TODO } from '@/types/general';
import { ModifiedQuestion } from '@/types/question';

type FieldActionsProps = {
    handleProps: ANY_TODO;
    index: number;
    question: ModifiedQuestion;
};

const FieldActions = ({ handleProps, index, question }: FieldActionsProps) => {
    const {
        id,
        questionType: { alias, isAllowChildren, name },
    } = question;
    const editIndex = useStoreState(state => state.questionBuilderModel.editIndex);
    const { addAfterQuestion } = useStoreState(state => state.questionBuilderModel.addQuestionSettings);
    const languageCode = useStoreState(state => state.questionBuilderModel.languageCode);
    const { handleRemove } = useDndActions();
    const { openEditForm } = useEditFormActions();

    return (
        <div
            className={cn('absolute h-full w-full rounded', {
                'opacity-0 group-hover:opacity-100': editIndex !== index,
                'opacity-100': editIndex === index,
            })}
        >
            <div
                className={cn(
                    'grid grid-cols-3 items-start justify-between opacity-0 group-hover:opacity-100',
                    'gap-x-3 px-2',
                    'transition-all duration-300 ease-in-out',
                    {
                        'opacity-0 group-hover:opacity-100': editIndex !== index,
                        'opacity-100': editIndex === index,
                    }
                )}
            >
                <div className="-mt-5 flex gap-x-2">
                    {editIndex === null &&
                        languageCode === 'en' &&
                        addAfterQuestion === null &&
                        name !== 'startPaging' &&
                        name !== 'endPaging' && <Handle {...handleProps} size="sm" />}
                </div>
                <div
                    className={cn('-mt-[2px] justify-self-center rounded-b-sm px-2 py-1 text-xs uppercase', {
                        'bg-gray-300 text-gray-900': editIndex !== index,
                        'bg-green-500 text-white': editIndex === index,
                    })}
                >
                    {alias}
                </div>
                <div className="-mt-5 flex gap-x-2 justify-self-end">
                    {editIndex === null && name !== 'endPaging' && (
                        <Edit size="sm" onClick={() => openEditForm(index)} />
                    )}
                    {editIndex === null && languageCode === 'en' && isAllowChildren && (
                        <AddChildQuestion question={question} />
                    )}
                    {editIndex === null && languageCode === 'en' && addAfterQuestion === null && (
                        <Remove size="sm" onClick={() => handleRemove(id)} />
                    )}
                </div>
            </div>
        </div>
    );
};

export default FieldActions;
