import { light } from '@fortawesome/fontawesome-svg-core/import.macro';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const LevelIndicator = ({ level, isDragging }: { level: number; isDragging: boolean | undefined }) => (
    <>
        {level > 0 && !isDragging && (
            <div className="w-auto">
                <FontAwesomeIcon icon={light('arrow-turn-down-right')} className="text-xl text-blue-500" />
            </div>
        )}
    </>
);

export default LevelIndicator;
