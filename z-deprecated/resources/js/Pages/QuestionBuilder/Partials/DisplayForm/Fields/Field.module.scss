.Wrapper {
    margin-bottom: -1px;
    list-style: none;
    box-sizing: border-box;

    padding-left: var(--indent-spacing);

    &.ghost {
        &.indicator {
            opacity: 1;
            position: relative;
            z-index: 1;
            margin-bottom: -1px;

            .Field {
                position: relative;
                height: calc(var(--spacing) * 2);
                border-radius: var(--radius-lg);
                border: 1px solid var(--color-blue-400);
                background-color: var(--color-blue-400);
                padding: 0;

                &::before {
                    content: '';
                    position: absolute;
                    display: block;
                    top: calc(var(--spacing) * -1);
                    left: calc(var(--spacing) * -1);
                    width: calc(var(--spacing) * 3);
                    height: calc(var(--spacing) * 3);
                    border-radius: 50%;
                    border: 1px solid var(--color-blue-400);
                    background-color: var(--color-white);
                }

                &.noChildrenAllowed {
                    border: 1px solid var(--color-red-400);
                    background-color: var(--color-red-400);

                    &::before {
                        border-color: var(--color-red-400);
                    }
                }

                > * {
                    /* Items are hidden using height and opacity to retain focus */
                    height: 0;
                    opacity: 0;
                }
            }
        }

        &:not(.indicator) {
            opacity: 0.05;
        }

        .Field > * {
            background-color: transparent;
            box-shadow: none;
        }
    }
}

.Field {
    box-sizing: border-box;
    position: relative;
    display: flex;
    align-items: center;

    :global .ant-radio-wrapper,
    :global .ant-checkbox-wrapper {
        display: flex;
    }
}

:global .display-form .ant-form-item {
    margin-bottom: 0 !important;

    :global .ant-input-affix-wrapper,
    :global .ant-picker,
    :global .ant-form-item-control-input > .ant-form-item-control-input-content > input.ant-input {
        padding: calc(var(--spacing) * 1) calc(var(--spacing) * 3);
        border-radius: 0;
        background-color: var(--color-white);
    }

    &.ant-form-item-has-error {
        .ant-form-item-explain {
            margin-top: calc(var(--spacing) * 2);
            border: 1px solid var(--color-red-500);
            background-color: var(--color-red-100);
            padding: calc(var(--spacing) * 2);
        }
    }
}

.Text {
    flex-grow: 1;
    padding-left: 0.5rem;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.disableInteraction {
    pointer-events: none;
}

.disableSelection {
    .Text,
    .Count {
        user-select: none;
        -webkit-user-select: none;
    }
}
