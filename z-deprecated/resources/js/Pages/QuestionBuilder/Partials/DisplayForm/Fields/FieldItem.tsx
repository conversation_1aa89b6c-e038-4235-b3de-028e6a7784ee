import FreeFormTextFormItem from '../FormItems/FreeFormTextFormItem';

import AcceptTermsFormItem from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/AcceptTermsFormItem';
import CalcBMIFormItem from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/CalcBMIFormItem';
import CheckboxGroupFormItem from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/CheckboxGroupFormItem';
import DobFormItem from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/DobFormItem';
import EndPaging from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/EndPaging';
import InputFormItem from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/InputFormItem';
import InputNumberFormItem from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/InputNumberFormItem';
import MissingFormItem from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/MissingFormItem';
import NextButton from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/NextButton';
import PageBreak from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/PageBreak';
import RadioGroupFormItem from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/RadioGroupFormItem';
import SelectFormItem from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/SelectFormItem';
import StartPaging from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/StartPaging';
import SubmitButton from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/SubmitButton';
import TextAreaFormItem from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/TextAreaFormItem';
import { ModifiedQuestion } from '@/types/question';

const FieldItem = ({ index, question }: { index: number; question: ModifiedQuestion }) => {
    if (question.questionType.name === 'stext' || question.questionType.isPredefined) {
        return <InputFormItem index={index} question={question} />;
    }

    if (question.questionType.name === 'essay') {
        return <TextAreaFormItem index={index} question={question} />;
    }

    if (question.questionType.name === 'calcBMI') {
        return <CalcBMIFormItem index={index} question={question} />;
    }

    if (question.questionType.name === 'acceptTerms') {
        return <AcceptTermsFormItem index={index} question={question} />;
    }

    if (question.questionType.name === 'dob') {
        return <DobFormItem index={index} question={question} />;
    }

    if (question.questionType.name === 'freeFormText') {
        return <FreeFormTextFormItem index={index} question={question} />;
    }

    if (question.questionType.isAllowNumber) {
        return <InputNumberFormItem index={index} question={question} />;
    }

    if (question.questionType.isAllowOptions || question.questionType.isAllowSites) {
        const settings = question.questionType.name === 'anyof' ? question.settings.checkbox : question.settings.radio;

        switch (settings?.optionsType?.value) {
            case 'dropdown':
                return <SelectFormItem index={index} question={question} />;

            default:
                if (question.questionType.name === 'oneof' || question.questionType.isAllowSites) {
                    return (
                        <RadioGroupFormItem
                            index={index}
                            question={question}
                            optionType={settings?.optionsType?.value === 'radioGroup' ? 'button' : 'default'}
                        />
                    );
                }

                return <CheckboxGroupFormItem index={index} question={question} />;
        }
    }

    if (question.questionType.name === 'pageBreak') {
        return <PageBreak />;
    }

    if (question.questionType.name === 'startPaging') {
        return <StartPaging />;
    }

    if (question.questionType.name === 'endPaging') {
        return <EndPaging />;
    }

    if (question.questionType.name === 'nextButton') {
        return <NextButton index={index} question={question} />;
    }

    if (question.questionType.name === 'submitButton') {
        return <SubmitButton index={index} question={question} />;
    }

    return <MissingFormItem index={index} question={question} />;
};

export default FieldItem;
