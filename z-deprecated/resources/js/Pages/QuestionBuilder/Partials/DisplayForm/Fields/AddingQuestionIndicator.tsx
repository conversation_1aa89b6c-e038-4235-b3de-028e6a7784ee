import { Fragment, useMemo } from 'react';

import { Transition } from '@headlessui/react';

import { useStoreState } from '@/store/hooks';
import { ModifiedQuestion } from '@/types/question';

const AddingQuestionIndicator = ({ question, level }: { question: ModifiedQuestion; level: number }) => {
    const { addAfterQuestion, isChild } = useStoreState(state => state.questionBuilderModel.addQuestionSettings);
    const indentationWidth = useStoreState(state => state.questionBuilderModel.indentationWidth);
    const showIndicator = useMemo(() => question.id === addAfterQuestion?.id, [question.id, addAfterQuestion?.id]);
    const paddingLeft = isChild ? indentationWidth * (level + 1) : 0;

    return (
        <Transition.Root appear show={showIndicator} as={Fragment}>
            <Transition.Child
                as={Fragment}
                unmount={false}
                enter="transform transition-all ease-in-out duration-200"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="transform transition-all ease-in-out duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
            >
                <div className="relative mt-2" style={{ marginLeft: `${paddingLeft}px` }}>
                    <div className="absolute inset-0 flex items-center" aria-hidden="true">
                        <div className="w-full border-t-2 border-blue-300" />
                    </div>
                    <div className="relative flex justify-center">
                        <span className="bg-white px-2 text-sm font-semibold uppercase text-blue-500">
                            {isChild ? 'Adding Nested Question Field(s) Here' : 'Adding Question Field(s) Here'}
                        </span>
                    </div>
                </div>
            </Transition.Child>
        </Transition.Root>
    );
};

export default AddingQuestionIndicator;
