import PhoneInput from 'react-phone-number-input';

import { Form, Input } from 'antd';
import { CountryCode, E164Number } from 'libphonenumber-js';

import { usePage } from '@inertiajs/react';

import '@/Pages/EmbedForm/css/phone-input.css';

import usePrimaryFormSettings from '@/hooks/QuestionBuilder/usePrimaryFormSettings';
import useShowFormErrors from '@/hooks/QuestionBuilder/useShowFormErrors';
import { cn } from '@/lib/utils';
import { FormItemStyleSheet, InputStyleSheet, LabelStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import QuestionEnEquivalent from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/QuestionEnEquivalent';
import RequiredEncryptedText from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/RequiredEncryptedText';
import { PageProps } from '@/types/general';
import { FormItemComponentProps } from '@/types/question-builder';
import shouldFormItemUpdate from '@/utils/QuestionBuilder/should-form-item-update';

const InputFormItem = ({ index, question }: FormItemComponentProps) => {
    const { id } = question;
    const { errorMessages } = useShowFormErrors(index);
    const { globalSettings } = usePrimaryFormSettings();
    const { currentTenant } = usePage<PageProps>().props;
    const tenantCountryCode = currentTenant.server === 'ukSouth' ? 'GB' : 'US';
    const jsxClass = `jsx-${id}`;

    return (
        <div className={cn('m-0! w-full! p-0!', jsxClass)}>
            <Form.Item
                shouldUpdate={(prevValues, curValues) => shouldFormItemUpdate(prevValues, curValues, index)}
                noStyle
            >
                {({ getFieldValue }) => {
                    const isRequired = getFieldValue(['questions', index, 'isRequired']);
                    const isEncrypted = getFieldValue(['questions', index, 'isEncrypted']);
                    const isConfirmed = getFieldValue(['questions', index, 'isConfirmed']);
                    const questionLabel = getFieldValue(['questions', index, 'questionLabel']);
                    const itemSettings = getFieldValue(['questions', index, 'settings']);
                    const defaultCountryCode = itemSettings?.defaultCountryCode?.value ?? tenantCountryCode;
                    const confirmedQuestionLabel = itemSettings?.confirm?.label;
                    const isHidden = itemSettings.input?.type?.value === 'hidden';

                    return (
                        <>
                            <RequiredEncryptedText question={{ id, isRequired, isEncrypted, isHidden }} />
                            <QuestionEnEquivalent question={question} />
                            <Form.Item
                                name={['doNotSubmit', question.id]}
                                label={<span dangerouslySetInnerHTML={{ __html: questionLabel }} />}
                                validateStatus={errorMessages ? 'error' : undefined}
                                help={errorMessages}
                                className={isHidden ? 'opacity-[.5]' : ''}
                            >
                                {question.questionType.name === 'phone' ? (
                                    <PhoneInputCnt defaultCountryCode={defaultCountryCode} />
                                ) : (
                                    <Input type={question.questionType.name === 'email' ? 'email' : 'text'} />
                                )}
                            </Form.Item>

                            {isConfirmed === '1' && (
                                <Form.Item
                                    name="confirmed"
                                    label={<span dangerouslySetInnerHTML={{ __html: confirmedQuestionLabel }} />}
                                    className="mt-3"
                                >
                                    {question.questionType.name === 'phone' ? (
                                        <PhoneInputCnt defaultCountryCode={defaultCountryCode} />
                                    ) : (
                                        <Input type={question.questionType.name === 'email' ? 'email' : 'text'} />
                                    )}
                                </Form.Item>
                            )}

                            <FormItemStyleSheet
                                generalSettings={globalSettings}
                                itemSettings={itemSettings}
                                classPrefix={jsxClass}
                                jsxId="delete-this"
                            />
                            <LabelStyleSheet settings={itemSettings} classPrefix={jsxClass} jsxId="delete-this" />
                            <InputStyleSheet settings={itemSettings} classPrefix={jsxClass} jsxId="delete-this" />
                        </>
                    );
                }}
            </Form.Item>
        </div>
    );
};

const PhoneInputCnt = ({
    value: valueProp,
    onChange,
    defaultCountryCode,
}: {
    value?: string;
    onChange?: (value?: E164Number | undefined) => void;
    defaultCountryCode?: CountryCode;
}) => (
    <PhoneInput
        defaultCountry={defaultCountryCode}
        value={valueProp}
        onChange={onChange as (value?: string | undefined) => void}
    />
);

export default InputFormItem;
