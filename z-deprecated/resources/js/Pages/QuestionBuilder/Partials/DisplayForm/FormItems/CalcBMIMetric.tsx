import { ReactNode } from 'react';

import { Form, Input } from 'antd';

import { cn } from '@/lib/utils';
import { KeyValueGeneric } from '@/types/general';
import { FormItemComponentProps } from '@/types/question-builder';
import getStyles from '@/utils/EmbedForm/get-styles';

type Props = {
    errorMessages: ReactNode | undefined;
    itemSettings: KeyValueGeneric;
    question: FormItemComponentProps['question'];
};

const CalcBMIMetric = ({ itemSettings, question, errorMessages }: Props) => {
    const heightLabelStyle = getStyles(itemSettings?.bmi?.heightLabel?.style);
    const weightLabelStyle = getStyles(itemSettings?.bmi?.weightLabel?.style);

    return (
        <div
            role="row"
            className={cn('flex gap-x-3', {
                'flex-row': !itemSettings?.bmi?.heightPosition || itemSettings?.bmi?.heightPosition === 'right',
                'flex-row-reverse justify-end': itemSettings?.bmi?.heightPosition === 'left',
            })}
        >
            <div className="w-1/3">
                {itemSettings?.bmi?.weightLabel?.label ? (
                    <div className="ant-col ant-form-item-label">
                        <label>
                            <span
                                dangerouslySetInnerHTML={{
                                    __html: itemSettings.bmi.weightLabel.label,
                                }}
                            />
                            <style jsx={undefined}>{`
                                {
                                    ${weightLabelStyle.join('\n')}
                                }
                            `}</style>
                        </label>
                    </div>
                ) : null}
                <Form.Item
                    name={['doNotSubmit', question.id, 'kilograms']}
                    validateStatus={errorMessages ? 'error' : undefined}
                >
                    <Input addonAfter="kgs" type="number" min={0} max={700} />
                </Form.Item>
            </div>
            <div className="w-1/3">
                {itemSettings?.bmi?.heightLabel?.label ? (
                    <div className="ant-col ant-form-item-label">
                        <label>
                            <span
                                dangerouslySetInnerHTML={{
                                    __html: itemSettings.bmi.heightLabel.label,
                                }}
                            />
                            <style jsx={undefined}>{`
                                {
                                    ${heightLabelStyle.join('\n')}
                                }
                            `}</style>
                        </label>
                    </div>
                ) : null}
                <Form.Item
                    name={['doNotSubmit', question.id, 'centimeters']}
                    validateStatus={errorMessages ? 'error' : undefined}
                >
                    <Input addonAfter="cm" type="number" min={0} max={275} />
                </Form.Item>
            </div>
        </div>
    );
};

export default CalcBMIMetric;
