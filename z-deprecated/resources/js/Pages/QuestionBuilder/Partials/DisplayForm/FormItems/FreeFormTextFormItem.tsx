import { Form } from 'antd';
import edJsHTML from 'editorjs-html';

import { cn } from '@/lib/utils';
import { FormItemComponentProps } from '@/types/question-builder';
import getStyles from '@/utils/EmbedForm/get-styles';
import shouldFormItemUpdate from '@/utils/QuestionBuilder/should-form-item-update';

const FreeFormTextFormItem = ({ index, question }: FormItemComponentProps) => {
    const { id } = question;
    const jsxClass = `jsx-${id}`;

    return (
        <div className="m-0! w-full! p-0!">
            <Form.Item
                shouldUpdate={(prevValues, curValues) => shouldFormItemUpdate(prevValues, curValues, index)}
                noStyle
            >
                {({ getFieldValue }) => {
                    const itemSettings = getFieldValue(['questions', index, 'settings']);
                    const freeFormTextContent = itemSettings?.freeFormText?.text;

                    const freeFormTextContainerStyles = getStyles(itemSettings?.freeFormText?.container?.style);
                    const freeFormTextContentStyles = getStyles(itemSettings?.freeFormText?.content?.style);
                    const htmlContent = freeFormTextContent?.userAgreement?.terms?.blocks
                        ? edJsHTML().parse(freeFormTextContent).join('\n <br>')
                        : '';

                    return (
                        <>
                            <div
                                className={cn('free-form-text-container list-decimal', jsxClass)}
                                dangerouslySetInnerHTML={{
                                    __html: htmlContent,
                                }}
                            />

                            <style jsx={undefined}>{`
                                .free-form-text-container.${jsxClass} {
                                    ${freeFormTextContainerStyles.join('\n')}
                                }

                                .free-form-text-container.${jsxClass} p {
                                    ${freeFormTextContentStyles.join('\n')}
                                }

                                .free-form-text-container.${jsxClass} ol,
                                .free-form-text-container.${jsxClass} ul {
                                    padding-left: 40px;
                                }

                                .free-form-text-container.${jsxClass} ol {
                                    list-style-type: decimal;
                                }

                                .free-form-text-container.${jsxClass} ul {
                                    list-style-type: disc;
                                }

                                .free-form-text-container.${jsxClass} ol li,
                                .free-form-text-container.${jsxClass} ul li {
                                    margin-bottom: 5px;
                                }
                            `}</style>
                        </>
                    );
                }}
            </Form.Item>
        </div>
    );
};

export default FreeFormTextFormItem;
