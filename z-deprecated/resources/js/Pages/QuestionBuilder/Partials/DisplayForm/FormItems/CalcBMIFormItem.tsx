import { Form } from 'antd';

import usePrimaryFormSettings from '@/hooks/QuestionBuilder/usePrimaryFormSettings';
import useShowFormErrors from '@/hooks/QuestionBuilder/useShowFormErrors';
import { cn } from '@/lib/utils';
import { FormItemStyleSheet, InputStyleSheet, LabelStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import CalcBMIMetric from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/CalcBMIMetric';
import CalcBMIUS from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/CalcBMIUS';
import QuestionEnEquivalent from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/QuestionEnEquivalent';
import RequiredEncryptedText from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/RequiredEncryptedText';
import { FormItemComponentProps } from '@/types/question-builder';
import shouldFormItemUpdate from '@/utils/QuestionBuilder/should-form-item-update';

const CalcBMIFormItem = ({ index, question }: FormItemComponentProps) => {
    const { id } = question;
    const { errorMessages } = useShowFormErrors(index);
    const { globalSettings } = usePrimaryFormSettings();
    const jsxClass = `jsx-${id}`;

    return (
        <div className={cn('m-0! w-full! p-0!', jsxClass)}>
            <Form.Item
                shouldUpdate={(prevValues, curValues) => shouldFormItemUpdate(prevValues, curValues, index)}
                noStyle
            >
                {({ getFieldValue }) => {
                    const isRequired = getFieldValue(['questions', index, 'isRequired']);
                    const isEncrypted = getFieldValue(['questions', index, 'isEncrypted']);
                    const questionLabel = getFieldValue(['questions', index, 'questionLabel']);
                    const itemSettings = getFieldValue(['questions', index, 'settings']);
                    const unit = itemSettings.bmi.unit ?? 'us';

                    return (
                        <>
                            <RequiredEncryptedText question={{ id, isRequired, isEncrypted }} />
                            <QuestionEnEquivalent question={question} />
                            <Form.Item
                                shouldUpdate={() => false}
                                label={<span dangerouslySetInnerHTML={{ __html: questionLabel }} />}
                                validateStatus={errorMessages ? 'error' : undefined}
                                help={errorMessages}
                            >
                                {unit === 'us' ? (
                                    <CalcBMIUS
                                        question={question}
                                        errorMessages={errorMessages}
                                        itemSettings={itemSettings}
                                    />
                                ) : (
                                    <CalcBMIMetric
                                        question={question}
                                        errorMessages={errorMessages}
                                        itemSettings={itemSettings}
                                    />
                                )}
                            </Form.Item>
                            <FormItemStyleSheet
                                generalSettings={globalSettings}
                                itemSettings={itemSettings}
                                classPrefix={jsxClass}
                                jsxId="delete-this"
                            />
                            <LabelStyleSheet settings={itemSettings} classPrefix={jsxClass} jsxId="delete-this" />
                            <InputStyleSheet settings={itemSettings} classPrefix={jsxClass} jsxId="delete-this" />
                        </>
                    );
                }}
            </Form.Item>
        </div>
    );
};

export default CalcBMIFormItem;
