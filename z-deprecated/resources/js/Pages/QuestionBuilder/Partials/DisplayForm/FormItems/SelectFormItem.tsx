import { Form, Select, SelectProps } from 'antd';

import '@/Pages/EmbedForm/css/select-form-item.css';

import usePrimaryFormSettings from '@/hooks/QuestionBuilder/usePrimaryFormSettings';
import useShowFormErrors from '@/hooks/QuestionBuilder/useShowFormErrors';
import { cn } from '@/lib/utils';
import { FormItemStyleSheet, LabelStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import QuestionEnEquivalent from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/QuestionEnEquivalent';
import RequiredEncryptedText from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/RequiredEncryptedText';
import { KeyValueGeneric } from '@/types/general';
import { FormItemComponentProps } from '@/types/question-builder';
import getRadioCheckboxSelectOptions from '@/utils/QuestionBuilder/get-radio-checkbox-select-options';
import shouldFormItemUpdate from '@/utils/QuestionBuilder/should-form-item-update';

const SelectFormItem = ({ index, question }: FormItemComponentProps) => {
    const { id } = question;
    const { errorMessages } = useShowFormErrors(index);
    const { globalSettings } = usePrimaryFormSettings();
    const jsxClass = `jsx-${id}`;

    return (
        <div className={cn('m-0! w-full! p-0!', jsxClass)}>
            <Form.Item
                shouldUpdate={(prevValues, curValues) => shouldFormItemUpdate(prevValues, curValues, index)}
                noStyle
            >
                {({ getFieldValue }) => {
                    const isRequired = getFieldValue(['questions', index, 'isRequired']);
                    const isEncrypted = getFieldValue(['questions', index, 'isEncrypted']);
                    const questionLabel = getFieldValue(['questions', index, 'questionLabel']);
                    const options = getFieldValue(['questions', index, 'options']);
                    const itemSettings = getFieldValue(['questions', index, 'settings']);

                    const selectProps: SelectProps<KeyValueGeneric> = {
                        showSearch: true,
                        style: { width: '100%' },
                        placeholder: 'Select Value(s)',
                        options: options?.length > 0 ? getRadioCheckboxSelectOptions(options) : [],
                        optionFilterProp: 'label',
                    };

                    if (question.questionType.name === 'anyof') {
                        selectProps.mode = 'multiple';
                    }

                    return (
                        <>
                            <RequiredEncryptedText question={{ id, isRequired, isEncrypted }} />
                            <QuestionEnEquivalent question={question} />
                            <Form.Item
                                name={['doNotSubmit', question.id]}
                                label={<span dangerouslySetInnerHTML={{ __html: questionLabel }} />}
                                validateStatus={errorMessages ? 'error' : undefined}
                                help={errorMessages}
                            >
                                <Select {...selectProps} />
                            </Form.Item>
                            <FormItemStyleSheet
                                generalSettings={globalSettings}
                                itemSettings={itemSettings}
                                classPrefix={jsxClass}
                                jsxId="delete-this"
                            />
                            <LabelStyleSheet settings={itemSettings} classPrefix={jsxClass} jsxId="delete-this" />
                        </>
                    );
                }}
            </Form.Item>
        </div>
    );
};

export default SelectFormItem;
