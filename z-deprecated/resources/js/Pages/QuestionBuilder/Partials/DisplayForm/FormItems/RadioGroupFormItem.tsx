import { Form, Radio } from 'antd';

import usePrimaryFormSettings from '@/hooks/QuestionBuilder/usePrimaryFormSettings';
import useShowFormErrors from '@/hooks/QuestionBuilder/useShowFormErrors';
import { cn } from '@/lib/utils';
import { FormItemStyleSheet, LabelStyleSheet, RadioGroupStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import QuestionEnEquivalent from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/QuestionEnEquivalent';
import RequiredEncryptedText from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/RequiredEncryptedText';
import { FormItemComponentProps } from '@/types/question-builder';
import getRadioCheckboxSelectOptions from '@/utils/QuestionBuilder/get-radio-checkbox-select-options';
import shouldFormItemUpdate from '@/utils/QuestionBuilder/should-form-item-update';

const RadioGroupFormItem = ({ index, question, optionType = 'default' }: FormItemComponentProps) => {
    const { id } = question;
    const { errorMessages } = useShowFormErrors(index);
    const { globalSettings } = usePrimaryFormSettings();
    const jsxClass = `jsx-${id}`;

    // TODO: make adjustment for group

    return (
        <Form.Item shouldUpdate={(prevValues, curValues) => shouldFormItemUpdate(prevValues, curValues, index)} noStyle>
            {({ getFieldValue }) => {
                const isRequired = getFieldValue(['questions', index, 'isRequired']);
                const isEncrypted = getFieldValue(['questions', index, 'isEncrypted']);
                const questionLabel = getFieldValue(['questions', index, 'questionLabel']);
                const options = getFieldValue(['questions', index, 'options']);
                const itemSettings = getFieldValue(['questions', index, 'settings']);

                return (
                    <>
                        <RequiredEncryptedText question={{ id, isRequired, isEncrypted }} />
                        <QuestionEnEquivalent question={question} />

                        <div className={cn('m-0! w-full! p-0!', jsxClass)}>
                            <Form.Item
                                name={['doNotSubmit', question.id]}
                                label={<span dangerouslySetInnerHTML={{ __html: questionLabel }} />}
                                validateStatus={errorMessages ? 'error' : undefined}
                                help={errorMessages}
                            >
                                <Radio.Group
                                    options={options?.length > 0 ? getRadioCheckboxSelectOptions(options) : []}
                                    optionType={optionType}
                                    buttonStyle="solid"
                                />
                            </Form.Item>
                            <FormItemStyleSheet
                                generalSettings={globalSettings}
                                itemSettings={itemSettings}
                                classPrefix={jsxClass}
                                jsxId="delete-this"
                            />
                            <LabelStyleSheet settings={itemSettings} classPrefix={jsxClass} jsxId="delete-this" />
                            <RadioGroupStyleSheet settings={itemSettings} classPrefix={jsxClass} jsxId="delete-this" />
                        </div>
                    </>
                );
            }}
        </Form.Item>
    );
};

export default RadioGroupFormItem;
