import { Form } from 'antd';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import usePrimaryFormSettings from '@/hooks/QuestionBuilder/usePrimaryFormSettings';
import { cn } from '@/lib/utils';
import { ButtonStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import { FormItemComponentProps } from '@/types/question-builder';
import Button from '@/UI-Kit/Button';
import { ButtonProps } from '@/UI-Kit/Button/types';
import { iconList } from '@/utils/dynamic-fontawesome-icons';
import getProps from '@/utils/EmbedForm/get-props';
import shouldFormItemUpdate from '@/utils/QuestionBuilder/should-form-item-update';
import { buttonShapes } from '@/utils/QuestionBuilder/styles/button-props';

const NextButton = ({ index, question }: FormItemComponentProps) => {
    const [{ value: defaultButtonShape }] = buttonShapes.filter(buttonShape => buttonShape.value === 'rounded');
    const { globalSettings } = usePrimaryFormSettings();
    const jsxClass = `jsx-${question.id}`;

    return (
        <div className={cn('m-0! w-full! p-0!', jsxClass)}>
            <Form.Item
                shouldUpdate={(prevValues, curValues) => shouldFormItemUpdate(prevValues, curValues, index)}
                noStyle
            >
                {({ getFieldValue }) => {
                    const itemSettings = getFieldValue(['questions', index, 'settings']);
                    const generalProps = getProps(globalSettings?.pageBreak?.nextButton?.buttonProps);
                    const defaultProps: ButtonProps = {
                        type: 'button',
                        label:
                            itemSettings?.pageBreak?.nextButton?.btnLabel?.text ??
                            globalSettings?.pageBreak?.nextButton?.btnLabel?.text ??
                            'Next',
                        color: 'blue',
                        // @ts-ignore
                        buttonShape: defaultButtonShape,
                        icon: globalSettings?.pageBreak?.nextButton?.icon?.value ? (
                            <FontAwesomeIcon icon={iconList[globalSettings.pageBreak.nextButton.icon.value]} />
                        ) : undefined,
                        ...generalProps,
                    };

                    return (
                        <>
                            <Button {...defaultProps} />
                            <ButtonStyleSheet
                                jsxId="delete-this"
                                classPrefix={jsxClass}
                                globalSettings={globalSettings?.pageBreak?.nextButton}
                                itemSettings={itemSettings?.pageBreak?.nextButton}
                            />
                        </>
                    );
                }}
            </Form.Item>
        </div>
    );
};

export default NextButton;
