import { useStoreState } from '@/store/hooks';
import { ModifiedQuestion } from '@/types/question';

const QuestionEnEquivalent = ({ question }: { question: ModifiedQuestion }) => {
    const languageCode = useStoreState(state => state.questionBuilderModel.languageCode);
    let enEquivalent = <span />;

    if (languageCode !== 'en') {
        enEquivalent = (
            <div className="mb-1">
                <span className="mr-1 font-bold uppercase text-red-800">EN:</span>
                <span className="italic text-red-800" dangerouslySetInnerHTML={{ __html: question.question.en }} />
            </div>
        );
    }

    return enEquivalent;
};

export default QuestionEnEquivalent;
