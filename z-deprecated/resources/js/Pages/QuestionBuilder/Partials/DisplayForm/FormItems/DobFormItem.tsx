import { Form, Input } from 'antd';

import usePrimaryFormSettings from '@/hooks/QuestionBuilder/usePrimaryFormSettings';
import useShowFormErrors from '@/hooks/QuestionBuilder/useShowFormErrors';
import { cn } from '@/lib/utils';
import { FormItemStyleSheet, InputStyleSheet, LabelStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import QuestionEnEquivalent from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/QuestionEnEquivalent';
import RequiredEncryptedText from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/RequiredEncryptedText';
import { FormItemComponentProps } from '@/types/question-builder';
import shouldFormItemUpdate from '@/utils/QuestionBuilder/should-form-item-update';

const DobFormItem = ({ index, question }: FormItemComponentProps) => {
    const { id } = question;
    const { errorMessages } = useShowFormErrors(index);
    const { globalSettings } = usePrimaryFormSettings();
    const jsxClass = `jsx-${id}`;

    return (
        <div className={cn('m-0! w-full! p-0!', jsxClass)}>
            <Form.Item
                shouldUpdate={(prevValues, curValues) => shouldFormItemUpdate(prevValues, curValues, index)}
                noStyle
            >
                {({ getFieldValue }) => {
                    const isRequired = getFieldValue(['questions', index, 'isRequired']);
                    const isEncrypted = getFieldValue(['questions', index, 'isEncrypted']);
                    const questionLabel = getFieldValue(['questions', index, 'questionLabel']);
                    const itemSettings = getFieldValue(['questions', index, 'settings']);
                    const showMonth = ['showFullDate', 'showMonthDayDate', 'showMonthYearDate'].includes(
                        itemSettings?.dateInputs
                    );
                    const showDay = ['showFullDate', 'showMonthDayDate'].includes(itemSettings?.dateInputs);
                    const showYear = ['showFullDate', 'showYearOnly', 'showMonthYearDate'].includes(
                        itemSettings?.dateInputs
                    );
                    const showFirstSlash = showMonth && showDay;
                    const showSecondSlash = showMonth && showYear;

                    return (
                        <>
                            <RequiredEncryptedText question={{ id, isRequired, isEncrypted }} />
                            <QuestionEnEquivalent question={question} />
                            <Form.Item
                                name={['doNotSubmit', question.id]}
                                label={<span dangerouslySetInnerHTML={{ __html: questionLabel }} />}
                                validateStatus={errorMessages ? 'error' : undefined}
                                help={errorMessages}
                            >
                                <div className="flex items-center gap-x-3">
                                    {showMonth && (
                                        <div>
                                            <span className="ml-1 block">Month</span>
                                            <Form.Item name={['doNotSubmit', question.id, 'month']} noStyle>
                                                <Input
                                                    type="number"
                                                    min={1}
                                                    max={12}
                                                    size="small"
                                                    placeholder="MM"
                                                    className="ui-input ui-input-compact w-20"
                                                />
                                            </Form.Item>
                                        </div>
                                    )}

                                    {showFirstSlash && <span className="inline-block pt-5">/</span>}

                                    {showDay && (
                                        <div>
                                            <span className="ml-1 block">Day</span>
                                            <Form.Item name={['doNotSubmit', question.id, 'day']} noStyle>
                                                <Input
                                                    type="number"
                                                    size="small"
                                                    placeholder="DD"
                                                    className="ui-input ui-input-compact w-20"
                                                />
                                            </Form.Item>
                                        </div>
                                    )}

                                    {showSecondSlash && <span className="inline-block pt-5">/</span>}

                                    {showYear && (
                                        <div>
                                            <span className="ml-1 block">Year</span>
                                            <Form.Item name={['doNotSubmit', question.id, 'year']} noStyle>
                                                <Input
                                                    type="number"
                                                    size="small"
                                                    placeholder="YYYY"
                                                    className="ui-input ui-input-compact w-24"
                                                />
                                            </Form.Item>
                                        </div>
                                    )}
                                </div>
                            </Form.Item>
                            <FormItemStyleSheet
                                generalSettings={globalSettings}
                                itemSettings={itemSettings}
                                classPrefix={jsxClass}
                                jsxId="delete-this"
                            />
                            <LabelStyleSheet settings={itemSettings} classPrefix={jsxClass} jsxId="delete-this" />
                            <InputStyleSheet settings={itemSettings} classPrefix={jsxClass} jsxId="delete-this" />
                        </>
                    );
                }}
            </Form.Item>
        </div>
    );
};

export default DobFormItem;
