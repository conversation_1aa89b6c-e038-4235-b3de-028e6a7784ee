import { ReactElement } from 'react';

import { ModifiedQuestion } from '@/types/question';

const RequiredEncryptedText = ({
    question,
}: {
    question: Pick<ModifiedQuestion, 'id' | 'isRequired' | 'isEncrypted' | 'isHidden'>;
}): ReactElement<any> | null => {
    const { id, isRequired, isEncrypted, isHidden } = question;
    const intIsRequired = parseInt(isRequired as unknown as string, 10);
    const intIsEncrypted = parseInt(isEncrypted as unknown as string, 10);

    if (!intIsRequired && !isEncrypted && !isHidden) return null;

    const elements = [];

    if (intIsRequired > 0) {
        elements.push(
            <span key={`required-label-${id}`} className="text-[10px] font-semibold text-red-500">
                REQUIRED
            </span>
        );
    }

    if (isHidden) {
        elements.push(
            <span key={`hidden-label-${id}`} className="text-[10px] font-semibold text-blue-500">
                HIDDEN
            </span>
        );
    }

    if (intIsEncrypted) {
        if (intIsRequired > 0 || isHidden) {
            elements.push(
                <span key={`separator-label-${id}`} className="mx-1 inline-block text-gray-300">
                    |
                </span>
            );
        }

        elements.push(
            <span key={`encrypted-label-${id}`} className="text-[10px] font-semibold text-gray-800">
                ENCRYPTED
            </span>
        );
    }

    return <span>{elements}</span>;
};
export default RequiredEncryptedText;
