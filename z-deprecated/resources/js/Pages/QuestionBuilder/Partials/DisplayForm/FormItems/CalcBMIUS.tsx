import { ReactNode } from 'react';

import { Form, Input } from 'antd';

import { cn } from '@/lib/utils';
import { KeyValueGeneric } from '@/types/general';
import { FormItemComponentProps } from '@/types/question-builder';
import getStyles from '@/utils/EmbedForm/get-styles';

type Props = {
    errorMessages: ReactNode | undefined;
    itemSettings: KeyValueGeneric;
    question: FormItemComponentProps['question'];
};

const CalcBMIUS = ({ itemSettings, errorMessages, question }: Props) => {
    const heightLabelStyle = getStyles(itemSettings?.bmi?.heightLabel?.style);
    const weightLabelStyle = getStyles(itemSettings?.bmi?.weightLabel?.style);

    return (
        <div
            role="row"
            className={cn('flex gap-x-3', {
                'flex-row': !itemSettings?.bmi?.heightPosition || itemSettings?.bmi?.heightPosition === 'right',
                'flex-row-reverse': itemSettings?.bmi?.heightPosition === 'left',
            })}
        >
            <div className="w-full justify-end  md:w-1/3">
                <div className="w-full">
                    {itemSettings?.bmi?.weightLabel?.label ? (
                        <div className="ant-col ant-form-item-label">
                            <label>
                                <span
                                    dangerouslySetInnerHTML={{
                                        __html: itemSettings.bmi.weightLabel.label,
                                    }}
                                />
                                <style jsx={undefined}>{`
                                    {
                                        ${weightLabelStyle.join('\n')}
                                    }
                                `}</style>
                            </label>
                        </div>
                    ) : null}
                    <Form.Item
                        name={['doNotSubmit', question.id, 'weight']}
                        validateStatus={errorMessages ? 'error' : undefined}
                    >
                        <Input addonAfter="lbs" type="number" min={0} max={1500} />
                    </Form.Item>
                </div>
            </div>
            <div className="w-full md:w-2/3">
                {itemSettings?.bmi?.heightLabel?.label ? (
                    <div className="ant-col ant-form-item-label">
                        <label>
                            <span
                                dangerouslySetInnerHTML={{
                                    __html: itemSettings.bmi.heightLabel.label,
                                }}
                            />
                            <style jsx={undefined}>{`
                                {
                                    ${heightLabelStyle.join('\n')}
                                }
                            `}</style>
                        </label>
                    </div>
                ) : null}
                <div className="flex gap-x-3">
                    <div className="w-1/2">
                        <Form.Item
                            name={['doNotSubmit', question.id, 'feet']}
                            validateStatus={errorMessages ? 'error' : undefined}
                        >
                            <Input addonAfter="ft" type="number" min={0} max={10} />
                        </Form.Item>
                    </div>
                    <div className="w-1/2">
                        <Form.Item
                            name={['doNotSubmit', question.id, 'inches']}
                            validateStatus={errorMessages ? 'error' : undefined}
                        >
                            <Input addonAfter="in" type="number" min={0} max={12} />
                        </Form.Item>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CalcBMIUS;
