import { Form, Radio } from 'antd';
import edJsHTML from 'editorjs-html';

import usePrimaryFormSettings from '@/hooks/QuestionBuilder/usePrimaryFormSettings';
import useShowFormErrors from '@/hooks/QuestionBuilder/useShowFormErrors';
import { cn } from '@/lib/utils';
import { FormItemStyleSheet, LabelStyleSheet, RadioGroupStyleSheet } from '@/Pages/EmbedForm/Partials/GeneralStyles';
import QuestionEnEquivalent from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/QuestionEnEquivalent';
import RequiredEncryptedText from '@/Pages/QuestionBuilder/Partials/DisplayForm/FormItems/RequiredEncryptedText';
import { FormItemComponentProps } from '@/types/question-builder';
import getStyles from '@/utils/EmbedForm/get-styles';
import shouldFormItemUpdate from '@/utils/QuestionBuilder/should-form-item-update';

const AcceptTermsFormItem = ({ index, question }: FormItemComponentProps) => {
    const { id } = question;
    const { errorMessages } = useShowFormErrors(index);
    const jsxClass = `jsx-${id}`;
    const { globalSettings } = usePrimaryFormSettings();

    return (
        <div className={cn('m-0! w-full! p-0!', jsxClass)}>
            <Form.Item
                shouldUpdate={(prevValues, curValues) => shouldFormItemUpdate(prevValues, curValues, index)}
                noStyle
            >
                {({ getFieldValue }) => {
                    const isRequired = getFieldValue(['questions', index, 'isRequired']);
                    const isEncrypted = getFieldValue(['questions', index, 'isEncrypted']);
                    const questionLabel = getFieldValue(['questions', index, 'questionLabel']);
                    const options = getFieldValue(['questions', index, 'options']);
                    const itemSettings = getFieldValue(['questions', index, 'settings']);
                    const userAgreementContainerStyles = getStyles(itemSettings?.userAgreement?.container?.style);
                    const userAgreementContentStyles = getStyles(itemSettings?.userAgreement?.content?.style);

                    return (
                        <>
                            <RequiredEncryptedText question={{ id, isRequired, isEncrypted }} />
                            <QuestionEnEquivalent question={question} />
                            <Form.Item
                                shouldUpdate={() => false}
                                style={{ marginBottom: 0 }}
                                label={
                                    itemSettings?.userAgreement?.terms ? (
                                        <span dangerouslySetInnerHTML={{ __html: questionLabel }} />
                                    ) : null
                                }
                            >
                                <div
                                    className={cn('flex', {
                                        'flex-col':
                                            !itemSettings?.userAgreement?.radioPosition ||
                                            itemSettings?.userAgreement?.radioPosition === 'top',
                                        'flex-col-reverse': itemSettings?.userAgreement?.radioPosition === 'bottom',
                                    })}
                                >
                                    <Form.Item
                                        name={['doNotSubmit', id]}
                                        style={{ marginBottom: 0 }}
                                        validateStatus={errorMessages ? 'error' : undefined}
                                        help={errorMessages}
                                    >
                                        <Radio.Group className="block" options={options} />
                                    </Form.Item>
                                    {itemSettings?.userAgreement?.terms?.blocks ? (
                                        <>
                                            <div
                                                className="user-agreement-container overflow-y-scroll"
                                                dangerouslySetInnerHTML={{
                                                    __html: edJsHTML().parse(itemSettings?.userAgreement?.terms)[0],
                                                }}
                                            />
                                            <style jsx={undefined}>{`
                                                :global(.user-agreement-container) {
                                                    ${userAgreementContainerStyles.join('\n')}
                                                }

                                                :global(.user-agreement-container p) {
                                                    ${userAgreementContentStyles.join('\n')}
                                                }
                                            `}</style>
                                        </>
                                    ) : null}
                                </div>
                            </Form.Item>

                            {!itemSettings?.userAgreement?.terms ? (
                                <p className={isRequired ? 'ant-form-custom-required' : ''}>
                                    <span dangerouslySetInnerHTML={{ __html: questionLabel }} /> Read the legal terms
                                    and agreements by clicking{' '}
                                    <a href="https://1nhealth.com/privacy-policy" target="_blank" rel="noreferrer">
                                        Privacy Policy
                                    </a>
                                    .
                                </p>
                            ) : null}

                            <FormItemStyleSheet
                                generalSettings={globalSettings}
                                itemSettings={itemSettings}
                                classPrefix={jsxClass}
                                jsxId="delete-this"
                            />
                            <LabelStyleSheet settings={itemSettings} classPrefix={jsxClass} jsxId="delete-this" />
                            <RadioGroupStyleSheet settings={itemSettings} classPrefix={jsxClass} jsxId="delete-this" />
                        </>
                    );
                }}
            </Form.Item>
        </div>
    );
};

export default AcceptTermsFormItem;
