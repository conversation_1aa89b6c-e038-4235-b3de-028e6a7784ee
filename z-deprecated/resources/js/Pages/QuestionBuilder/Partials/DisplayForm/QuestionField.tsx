import { CSSProperties } from 'react';

import { AnimateLayoutChanges, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import useProjected from '@/hooks/QuestionBuilder/useProjected';
import Field from '@/Pages/QuestionBuilder/Partials/DisplayForm/Fields/Field';
import { useStoreState } from '@/store/hooks';
import { ModifiedQuestion } from '@/types/question';
import iOS from '@/utils/QuestionBuilder/ios';

type QuestionProps = {
    flattenQuestion: ModifiedQuestion;
    index: number;
};

const animateLayoutChanges: AnimateLayoutChanges = ({ isSorting, wasDragging }) => !(isSorting || wasDragging);

const QuestionField = ({ flattenQuestion, index }: QuestionProps) => {
    const { id, level } = flattenQuestion;
    const activeId = useStoreState(state => state.questionBuilderModel.activeId);
    const { projected } = useProjected();
    const {
        attributes,
        isDragging,
        isSorting,
        isOver,
        listeners,
        setDraggableNodeRef,
        setDroppableNodeRef,
        transform,
        transition,
    } = useSortable({ id, animateLayoutChanges });

    const style: CSSProperties = {
        transform: CSS.Translate.toString(transform),
        transition,
    };

    return (
        <Field
            ref={setDraggableNodeRef}
            wrapperRef={setDroppableNodeRef}
            style={style}
            level={id === activeId && projected ? projected.level : level}
            index={index}
            isDragging={isDragging}
            isOver={isOver}
            disableSelection={iOS}
            disableInteraction={isSorting}
            question={flattenQuestion}
            indicator
            handleProps={{
                ...attributes,
                ...listeners,
            }}
        />
    );
};

export default QuestionField;
