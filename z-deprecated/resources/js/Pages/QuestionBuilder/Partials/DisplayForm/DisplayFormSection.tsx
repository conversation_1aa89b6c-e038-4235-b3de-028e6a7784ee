import { useCallback, useId, useRef, useState } from 'react';

import { Form, FormInstance } from 'antd';
import { debounce } from 'lodash';

import { DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import useDndActions from '@/hooks/QuestionBuilder/useDndActions';
import usePrimaryFormSettings from '@/hooks/QuestionBuilder/usePrimaryFormSettings';
import {
    CheckboxGroupStyleSheet,
    FormGlobalStyleSheet,
    InputStyleSheet,
    LabelStyleSheet,
    LinkStyleSheet,
    RadioGroupStyleSheet,
} from '@/Pages/EmbedForm/Partials/GeneralStyles';
import DisplayForm from '@/Pages/QuestionBuilder/Partials/DisplayForm';
import DraggableOverlay from '@/Pages/QuestionBuilder/Partials/Draggable/DraggableOverlay';
import { useStoreActions, useStoreState } from '@/store/hooks';
import { QuestionBuilderInitialValues, SensorContext } from '@/types/question-builder';
import sortableTreeKeyboardCoordinates from '@/utils/QuestionBuilder/keyboard-coordinates';
import { measuring } from '@/utils/QuestionBuilder/measuring';

const DisplayFormSection = () => {
    const { questionBuilderForm, initialFormValues } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
        initialFormValues: QuestionBuilderInitialValues;
    };
    const { questions: flattenedQuestions } = questionBuilderForm.getFieldsValue();
    const indentationWidth = useStoreState(state => state.questionBuilderModel.indentationWidth);
    const isFormDirty = useStoreState(state => state.appSettingsModel.isFormDirty);
    const offsetLeft = useStoreState(state => state.questionBuilderModel.offsetLeft);
    const setIsFormDirty = useStoreActions(actions => actions.appSettingsModel.setIsFormDirty);
    const { handleDragCancel, handleDragEnd, handleDragMove, handleDragOver, handleDragStart, cancelDrop } =
        useDndActions();
    const { globalSettings } = usePrimaryFormSettings();

    const sensorContext: SensorContext = useRef({
        items: flattenedQuestions,
        offset: offsetLeft,
    });
    const [coordinateGetter] = useState(() => sortableTreeKeyboardCoordinates(sensorContext, true, indentationWidth));
    const sensors = useSensors(
        useSensor(PointerSensor),
        useSensor(KeyboardSensor, {
            coordinateGetter,
        })
    );
    const handleFormValuesChange = useCallback(
        debounce(() => setIsFormDirty(true), 1000),
        []
    );

    return (
        <DndContext
            autoScroll={{
                // slow down to 5 instead of default 10
                acceleration: 5,
            }}
            sensors={sensors}
            measuring={measuring}
            cancelDrop={cancelDrop}
            onDragStart={handleDragStart}
            onDragMove={handleDragMove}
            onDragOver={handleDragOver}
            onDragEnd={handleDragEnd}
            onDragCancel={handleDragCancel}
        >
            <div id="embed-form" className="w-8/12 transition-width duration-200 ease-in-out">
                <div className="embed-form-cont">
                    <FormGlobalStyleSheet generalSettings={globalSettings ?? initialFormValues.settings} />
                    <LabelStyleSheet settings={globalSettings ?? initialFormValues.settings} jsxId={useId()} />
                    <LinkStyleSheet settings={globalSettings ?? initialFormValues.settings} jsxId={useId()} />
                    <RadioGroupStyleSheet settings={globalSettings ?? initialFormValues.settings} jsxId={useId()} />
                    <CheckboxGroupStyleSheet settings={globalSettings ?? initialFormValues.settings} jsxId={useId()} />
                    <InputStyleSheet settings={globalSettings ?? initialFormValues.settings} jsxId={useId()} />
                    <Form
                        key="form-question-builder"
                        id="save-questions"
                        form={questionBuilderForm}
                        name="questionBuilderForm"
                        layout="vertical"
                        initialValues={{ ...initialFormValues }}
                        onValuesChange={() => {
                            if (!isFormDirty) {
                                handleFormValuesChange();
                            }
                        }}
                    >
                        <Form.List name="questions">{fields => <DisplayForm formFields={fields} />}</Form.List>
                    </Form>
                </div>
            </div>
            <DraggableOverlay />
        </DndContext>
    );
};

export default DisplayFormSection;
