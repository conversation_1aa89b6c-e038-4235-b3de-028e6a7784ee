import { useEffect, useMemo } from 'react';

import { FormInstance } from 'antd';
import { FormListFieldData } from 'antd/lib/form/FormList';
import md5 from 'md5';

import { UniqueIdentifier } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import QuestionField from '@/Pages/QuestionBuilder/Partials/DisplayForm/QuestionField';
import { useStoreState } from '@/store/hooks';
import { removeChildrenOf } from '@/utils/QuestionBuilder/children';

const QuestionsContainer = ({ formFields }: { formFields: FormListFieldData[] }) => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
    };
    const flattenedQuestions = questionBuilderForm.getFieldValue(['questions']);
    const sortedIds = useMemo(
        () => flattenedQuestions?.map(({ id }: { id: UniqueIdentifier }) => id),
        [md5(JSON.stringify(flattenedQuestions))]
    );

    const activeId = useStoreState(state => state.questionBuilderModel.activeId);

    useEffect(() => {
        if (activeId) {
            const newFlattenedQuestions = removeChildrenOf(flattenedQuestions, activeId ? [activeId] : []);
            questionBuilderForm.setFieldsValue({ questions: [...newFlattenedQuestions] });
        }
    }, [activeId]);

    return (
        <div className="display-form">
            <SortableContext
                id="question-builder-sortable"
                items={sortedIds ?? []}
                strategy={verticalListSortingStrategy}
            >
                <ul>
                    {formFields.map(({ key, name }) => {
                        const question = questionBuilderForm.getFieldValue(['questions', name]);
                        const { id } = question;

                        return <QuestionField key={`question-${id}`} index={key} flattenQuestion={question} />;
                    })}
                </ul>
            </SortableContext>
        </div>
    );
};

export default QuestionsContainer;
