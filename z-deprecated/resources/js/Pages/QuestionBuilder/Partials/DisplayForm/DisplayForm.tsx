import { FormInstance } from 'antd';
import { FormListFieldData } from 'antd/lib/form/FormList';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import AddQuestion from '@/Pages/QuestionBuilder/Partials/Actions/AddQuestion';
import QuestionsContainer from '@/Pages/QuestionBuilder/Partials/DisplayForm/QuestionsContainer';
import { useStoreState } from '@/store/hooks';
import Alert from '@/UI-Kit/Alert';
import { Card, CardContent } from '@/UI-Kit/Shadcn/card';

const DisplayForm = ({ formFields }: { formFields: FormListFieldData[] }) => {
    const formErrors = useStoreState(state => state.questionBuilderModel.formErrors);
    const { questionBuilderForm } = useQuestionBuilderFormContext() as { questionBuilderForm: FormInstance };
    const flattenedQuestions = questionBuilderForm.getFieldValue(['questions']);

    return (
        <>
            {Object.keys(formErrors).length > 0 && (
                <Alert
                    type="error"
                    showIcon
                    className="mb-5"
                    message="You have errors on the form"
                    description="The form has errors. Please edit the question(s) below with error(s) before you continue."
                />
            )}
            <Card>
                <CardContent variant="fullPadding">
                    {flattenedQuestions.length > 0 ? (
                        <QuestionsContainer formFields={formFields} />
                    ) : (
                        <div className="group relative">
                            <Alert
                                type="info"
                                showIcon
                                message="You haven't added any questions to the form"
                                description="To begin building your form, click the green plus button below."
                            />
                            <AddQuestion question={{ id: 'startForm', questionType: {} }} isStatic />
                        </div>
                    )}
                </CardContent>
            </Card>
        </>
    );
};

export default DisplayForm;
