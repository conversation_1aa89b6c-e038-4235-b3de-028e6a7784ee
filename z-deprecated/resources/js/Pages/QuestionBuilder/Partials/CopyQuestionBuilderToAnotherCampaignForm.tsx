import { Form } from 'antd';
import { useRoute } from 'ziggy-js';

import { usePage } from '@inertiajs/react';

import { PageProps } from '@/types/general';
import { QuestionBuilderPageProps } from '@/types/question-builder';
import Alert from '@/UI-Kit/Alert';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';

const CopyQuestionBuilderToAnotherCampaignForm = () => {
    const route = useRoute();
    const { entity } = usePage<PageProps<QuestionBuilderPageProps>>().props;

    return (
        <div>
            <div className="mt-2">
                <Alert
                    type="warning"
                    paddingSize="sm"
                    className="mb-3 text-left"
                    description="This will override all questions you have on the selected form."
                />
                <p className="mb-4">
                    To copy this form to another from, choose the form you want to copy to and click the{' '}
                    <span className="font-semibold">Copy</span> button below. Once the form has been copied, the new
                    form will be loaded for modifications.
                </p>
                <div className="text-left">
                    <Form.Item
                        name="uuid"
                        className="mb-0"
                        rules={[{ required: true, message: 'Please select a campaign form' }]}
                    >
                        <ComboSelect
                            async
                            placeholder="Select Campaign Form"
                            isClearable={false}
                            endpoint={route('campaign-forms-for-select')}
                            queryRequest={{ excludeCampaignForms: [entity.uuid] }}
                        />
                    </Form.Item>
                </div>
            </div>
        </div>
    );
};

export default CopyQuestionBuilderToAnotherCampaignForm;
