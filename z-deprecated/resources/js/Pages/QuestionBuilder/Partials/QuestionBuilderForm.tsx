import { Form } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import useSubmitQuestionBuilder from '@/hooks/QuestionBuilder/useSubmitQuestionBuilder';
import usePreventNavigation from '@/hooks/usePreventNavigation';
import DisplayFormSection from '@/Pages/QuestionBuilder/Partials/DisplayForm/DisplayFormSection';
import Sidebar from '@/Pages/QuestionBuilder/Partials/Sidebar';
import { useStoreActions } from '@/store/hooks';
import { ANY_TODO } from '@/types/general';
import { Card, CardContent } from '@/UI-Kit/Shadcn/card';

const QuestionBuilderForm = () => {
    const { isLoading } = useQuestionBuilderFormContext();
    const setFormErrors = useStoreActions(actions => actions.questionBuilderModel.setFormErrors);
    const setIsFormDirty = useStoreActions(actions => actions.appSettingsModel.setIsFormDirty);
    const { submitQuestionBuilder } = useSubmitQuestionBuilder();

    usePreventNavigation();

    if (isLoading)
        return (
            <Card>
                <CardContent isLoading={isLoading} />
            </Card>
        );

    return (
        <div className="relative" id="form-builder">
            <Form.Provider
                onFormFinish={(name: string, info: ANY_TODO) => {
                    if (name === 'primaryFormBuilder') {
                        setFormErrors({ formErrors: [], editIndex: null });
                        setIsFormDirty(false);
                        submitQuestionBuilder({
                            ...info.values,
                            ...info.forms.questionBuilderForm.getFieldsValue(['questions']),
                        });
                    }
                }}
            >
                <div className="flex gap-x-8">
                    <DisplayFormSection />
                    <Sidebar />
                </div>
            </Form.Provider>
        </div>
    );
};

export default QuestionBuilderForm;
