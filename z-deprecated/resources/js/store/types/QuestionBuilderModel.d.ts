/* eslint-disable */
// @ts-nocheck
import { Action } from 'easy-peasy';
import { InternalNamePath } from 'rc-field-form/lib/interface';
import { UniqueIdentifier } from '@dnd-kit/core';

import { ModifiedQuestion } from '@/types/question';
import { QuestionType } from '@/types/question-types';
import { SiteDataList } from '@/types/site-data';
import { KeyValueGeneric } from '@/types/general';

export type FormError = {
    errors: string[];
    name: InternalNamePath;
};

export type AddQuestionSettings = {
    addAfterQuestion:
        | ModifiedQuestion
        | {
              id: string;
              questionType: KeyValueGeneric;
          }
        | null;
    parentQuestion:
        | ModifiedQuestion
        | {
              id: string;
              questionType: KeyValueGeneric;
          }
        | null;
    isChild: boolean;
};

export interface QuestionBuilderModel {
    /**
     * State
     */
    activeId: UniqueIdentifier | null;
    addQuestionSettings: AddQuestionSettings;
    campaignName: string;
    editIndex: number | null;
    formErrors: Record<number, string[]>;
    languageCode: string;
    indentationWidth: number;
    isDragging: boolean;
    isNewQuestion: boolean;
    offsetLeft: number;
    overId: UniqueIdentifier | null;
    questionTypes: QuestionType[];
    showAdvancedStyles: boolean;
    sites: SiteDataList[];
    /**
     * Actions
     */
    setActiveId: Action<QuestionBuilderModel, UniqueIdentifier | null>;
    setAddQuestionSettings: Action<QuestionBuilderModel, AddQuestionSettings>;
    setCampaignName: Action<QuestionBuilderModel, string>;
    setEditIndex: Action<QuestionBuilderModel, number | null>;
    setFormErrors: Action<
        QuestionBuilderModel,
        {
            formErrors: FormErrors[];
            editIndex: number | null;
        }
    >;
    setIsDragging: Action<QuestionBuilderModel, boolean>;
    setIsNewQuestion: Action<QuestionBuilderModel, boolean>;
    setLanguageCode: Action<QuestionBuilderModel, string>;
    setOffsetLeft: Action<QuestionBuilderModel, number>;
    setOverId: Action<QuestionBuilderModel, UniqueIdentifier | null>;
    setQuestionTypes: Action<QuestionBuilderModel, QuestionType[]>;
    setShowAdvancedStyles: Action<QuestionBuilderModel, boolean>;
    setSites: Action<QuestionBuilderModel, SiteDataList[]>;
}
