import { action } from 'easy-peasy';

import { FormError, QuestionBuilderModel } from '@/store/types/QuestionBuilderModel';

const questionBuilderModel: QuestionBuilderModel = {
    /**
     * Initial State
     */
    activeId: null,
    addQuestionSettings: {
        addAfterQuestion: null,
        parentQuestion: null,
        isChild: false,
    },
    campaignName: '',
    editIndex: null,
    formErrors: [],
    indentationWidth: 30,
    isDragging: false,
    isNewQuestion: false,
    languageCode: 'en',
    offsetLeft: 0,
    overId: null,
    questionTypes: [],
    showAdvancedStyles: false,
    sites: [],

    /**
     * Actions
     */
    setActiveId: action((state, payload) => {
        state.activeId = payload;
    }),
    setAddQuestionSettings: action((state, payload) => {
        state.addQuestionSettings = payload;
    }),
    setCampaignName: action((state, payload) => {
        state.campaignName = payload;
    }),
    setEditIndex: action((state, payload) => {
        state.editIndex = payload;
    }),
    setFormErrors: action((state, payload) => {
        const formattedErrors: Record<number, string[]> = {};
        const { formErrors, editIndex } = payload;

        if (editIndex !== null) {
            formErrors.forEach(({ errors, name }: FormError) => {
                const fieldName = name[0] as string;
                let errorValues = [...errors];
                let optionCount: number;

                if (errorValues.length > 0) {
                    if (!formattedErrors[editIndex]) {
                        formattedErrors[editIndex] = [];
                    }

                    if (fieldName === 'optionLabels' && typeof name[1] !== 'undefined') {
                        optionCount = (name[1] as number) + 1;
                        errorValues = errors.map((error: string) => {
                            if (error.includes('enter an option')) {
                                return `Please enter a value for option ${optionCount}`;
                            }

                            return error;
                        });
                    }

                    formattedErrors[editIndex].push(...errorValues);
                }
            });
        }

        state.formErrors = { ...formattedErrors };
    }),
    setIsDragging: action((state, payload) => {
        state.isDragging = payload;
    }),
    setIsNewQuestion: action((state, payload) => {
        state.isNewQuestion = payload;
    }),
    setLanguageCode: action((state, payload) => {
        state.languageCode = payload;
    }),
    setOffsetLeft: action((state, payload) => {
        state.offsetLeft = payload;
    }),
    setOverId: action((state, payload) => {
        state.overId = payload;
    }),
    setQuestionTypes: action((state, payload) => {
        state.questionTypes = payload;
    }),
    setShowAdvancedStyles: action((state, payload) => {
        state.showAdvancedStyles = payload;
    }),
    setSites: action((state, payload) => {
        state.sites = payload;
    }),
};

export default questionBuilderModel;
