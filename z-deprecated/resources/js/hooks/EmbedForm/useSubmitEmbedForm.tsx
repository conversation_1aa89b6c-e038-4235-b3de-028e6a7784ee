import { useRoute } from 'ziggy-js';

import { LanguageCode } from '@/Components/Languages/types/languages';
import useAxiosClient from '@/hooks/useAxiosClient';
import useEventCallback from '@/hooks/useEventCallback';
import { EmbedFormSubmission } from '@/Pages/EmbedForm/types';
import getCookieValue from '@/Pages/Modules/FormBuilder/utils/get-cookie-value';
import { useStoreActions, useStoreState } from '@/store/hooks';
import { ANY_TODO } from '@/types/general';
import convertAnswerPropsToTree from '@/utils/EmbedForm/convert-answer-props-to-tree';

const useSubmitEmbedForm = () => {
    const route = useRoute();
    const { setErrorMessage, setIsFormSubmitting } = useStoreActions(actions => actions.embedFormModel);
    const clientIp = useStoreState(state => state.embedFormModel.clientIp);
    const formQuestions = useStoreState(state => state.embedFormModel.formQuestions);
    const languageCodes = useStoreState(state => state.embedFormModel.languageCodes);
    const selectedLanguage = useStoreState(state => state.embedFormModel.selectedLanguage);
    const trackingId = useStoreState(state => state.embedFormModel.trackingId);
    const { axiosClient } = useAxiosClient();

    const submitEmbedForm = useEventCallback(
        async ({
            formValues,
            formId,
        }: {
            formValues: {
                [id: string]: string | number[] | number;
            };
            formId: string;
            isTest?: boolean;
        }): Promise<void> => {
            const isTest = new URLSearchParams(window.location.search).get('test') === 'true';

            const form: EmbedFormSubmission = {
                answers: convertAnswerPropsToTree(formValues, formQuestions, languageCodes as LanguageCode[]),
                clientIp,
                clientUserAgent: navigator.userAgent,
                fbcCookie: getCookieValue('_fbc'),
                fbpCookie: getCookieValue('_fbp'),
                formId,
                lang: selectedLanguage,
                trackingId,
                is_test: isTest,
            };

            await axiosClient()
                .post(route('ext.embed-form.store'), form)
                // eslint-disable-next-line consistent-return
                .then((data: ANY_TODO) => {
                    if (!data.success) {
                        setErrorMessage(data.description);
                        setIsFormSubmitting(false);
                        return false;
                    }

                    // @ts-ignore - Required because typing for window doesn't recognize top
                    window.top.location.href = data.redirect ? data.redirect : 'https://1nhealth.com';
                })
                .catch(() => {
                    setErrorMessage('There was an error submitting your form. Please try again.');
                    setIsFormSubmitting(false);
                });
        },
        [clientIp, formQuestions, languageCodes, selectedLanguage, trackingId, setErrorMessage]
    );

    return { submitEmbedForm };
};

export default useSubmitEmbedForm;
