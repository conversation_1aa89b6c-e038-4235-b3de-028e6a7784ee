import { FormInstance } from 'antd';

import { useStoreActions, useStoreState } from '@/store/hooks';

const usePagination = (form: FormInstance) => {
    const currentPage = useStoreState(state => state.embedFormModel.currentPage);
    const paginatedQuestionIds = useStoreState(state => state.embedFormModel.paginatedQuestionIds);
    const setCurrentPage = useStoreActions(actions => actions.embedFormModel.setCurrentPage);

    const goToNextPage = async () => {
        await form
            .validateFields(paginatedQuestionIds[currentPage])
            .then(() => {
                setCurrentPage(currentPage + 1);
            })
            .catch(() => {});
    };

    const goToPreviousPage = async () => {
        await form
            .validateFields(paginatedQuestionIds[currentPage])
            .then(() => {
                setCurrentPage(currentPage - 1);
            })
            .catch(() => {});
    };

    return {
        goToNextPage,
        goToPreviousPage,
    };
};

export default usePagination;
