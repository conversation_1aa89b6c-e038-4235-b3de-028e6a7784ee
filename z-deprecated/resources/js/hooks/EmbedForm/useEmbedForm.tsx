import { useEffect, useState } from 'react';

import { hasIn, reduce } from 'lodash';
import { publicIpv4 } from 'public-ip';
import { useRoute } from 'ziggy-js';

import { usePage } from '@inertiajs/react';

import { LanguageCode } from '@/Components/Languages/types/languages';
import useAxiosClient from '@/hooks/useAxiosClient';
import { useStoreActions } from '@/store/hooks';
import { EmbedFormData, EmbedFormPageProps, EmbedFormRequest, PaginatedQuestionReturn } from '@/types/embed-form';
import { PageProps } from '@/types/general';
import { ModifiedQuestion } from '@/types/question';
import buildFormQuestions from '@/utils/EmbedForm/build-form-questions';
import getLanguageCodes from '@/utils/EmbedForm/get-language-codes';
import paginateQuestions from '@/utils/EmbedForm/paginate-questions';

const useEmbedForm = () => {
    const route = useRoute();
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const { formId, utms, lang } = usePage<PageProps<EmbedFormPageProps>>().props;
    const { axiosClient } = useAxiosClient();
    const setClientIp = useStoreActions(actions => actions.embedFormModel.setClientIp);
    const setFormQuestions = useStoreActions(actions => actions.embedFormModel.setFormQuestions);
    const setFormContainers = useStoreActions(actions => actions.embedFormModel.setFormContainers);
    // const setErrorMessage = useStoreActions(actions => actions.embedFormModel.setErrorMessage);
    const setLanguageCodes = useStoreActions(actions => actions.embedFormModel.setLanguageCodes);
    const setPageCount = useStoreActions(actions => actions.embedFormModel.setPageCount);
    const setPaginatedQuestionIds = useStoreActions(actions => actions.embedFormModel.setPaginatedQuestionIds);
    const setSelectedLanguage = useStoreActions(actions => actions.embedFormModel.setSelectedLanguage);
    const setSettings = useStoreActions(actions => actions.embedFormModel.setSettings);
    const setTrackingId = useStoreActions(actions => actions.embedFormModel.setTrackingId);

    const getForm = async () => {
        const currentRoute = route().current();
        const ipResult: string = await getClientIp();
        const requestParams: EmbedFormRequest = {
            clientIp: ipResult,
            clientUserAgent: navigator.userAgent,
            formId,
            utms,
            lang,
        };

        setClientIp(ipResult);

        const data: { props: { embedFormData: EmbedFormData } } = await axiosClient({
            isInertia: true,
            only: ['embedFormData'],
        }).get(route(currentRoute as string, route().params), {
            params: { ...requestParams },
        });

        if (!hasIn(data, 'props.embedFormData')) {
            // TODO: Let's return a an error message
        }

        const { embedFormData } = data.props;
        const objLanguages = getLanguageCodes(lang ?? 'en', embedFormData.languages);
        let formQuestions: ModifiedQuestion[] = [];

        if (embedFormData.questions?.length > 0) {
            formQuestions = buildFormQuestions(embedFormData.questions, objLanguages.languageCodes as LanguageCode[]);
        }

        if (embedFormData?.settings?.general?.customFont?.link) {
            const customFont = document.createElement('link');
            customFont.href = embedFormData?.settings?.general?.customFont?.link;
            customFont.rel = 'stylesheet';
            document.body.appendChild(customFont);
        }

        const pageCount = reduce(
            formQuestions,
            (sum, question) => {
                if (question.questionType.name === 'pageBreak') {
                    return sum + 1;
                }
                return sum;
            },
            1
        );

        const results: PaginatedQuestionReturn = paginateQuestions({ formQuestions });

        setFormContainers(results.containers);
        setPaginatedQuestionIds(results.ids);
        setFormQuestions(formQuestions);
        setPageCount(pageCount);
        setSettings(embedFormData?.settings ?? {});
        setTrackingId(embedFormData?.trackingId);
        setLanguageCodes(objLanguages.languageCodes);
        setSelectedLanguage(objLanguages.selectedLanguage);
        setIsLoading(false);
    };

    useEffect(() => {
        const script = document.createElement('script');
        script.src = '/lib/iframe-resizer/iframeResizer.contentWindow.min.js';
        script.async = true;
        document.body.appendChild(script);

        (async () => {
            await getForm();
        })();

        return (): void => {
            document.body.removeChild(script);
        };
    }, []);

    return { isLoading, setIsLoading };
};

const getClientIp = async (): Promise<string> =>
    publicIpv4({
        fallbackUrls: ['https://ifconfig.co/ip'],
    });

export default useEmbedForm;
