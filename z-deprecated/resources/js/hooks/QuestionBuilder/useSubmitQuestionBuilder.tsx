import { LanguageCode } from '@/Components/Languages/types/languages';
import { standardApiNotification } from '@/Components/Notifications';
import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import useAxiosClient from '@/hooks/useAxiosClient';
import { useStoreActions, useStoreState } from '@/store/hooks';
import { KeyValueGeneric, UUID } from '@/types/general';
import { ModifiedQuestion } from '@/types/question';
import { QuestionBuilderResponse } from '@/types/question-builder';
import { QuestionnaireSchema } from '@/types/questionnaire-schema';
import buildTree from '@/utils/QuestionBuilder/build-tree';
import convertQuestionTreeToProps from '@/utils/QuestionBuilder/convert-question-tree-to-props';
import createAnswerSchema from '@/utils/QuestionBuilder/create-answer-schema';
import flattenTree from '@/utils/QuestionBuilder/flatten-tree';

type FormValues = {
    questions: ModifiedQuestion[];
    thankYouUrlDisqualified: KeyValueGeneric<string>;
    thankYouUrlQualified: KeyValueGeneric<string>;
    uuid: UUID;
};

type FormData = FormValues & {
    answerSchema: QuestionnaireSchema;
    languageCode: string;
    questions: ModifiedQuestion[];
    uuid: UUID;
};

const useSubmitQuestionBuilder = () => {
    const { questionBuilderForm, primaryForm } = useQuestionBuilderFormContext();
    const languageCode = useStoreState(state => state.questionBuilderModel.languageCode);
    const { setQuestionTypes, setSites } = useStoreActions(actions => actions.questionBuilderModel);
    const { axiosClient } = useAxiosClient();

    const submitQuestionBuilder = async (formValues: FormValues) => {
        const questionTree = buildTree(formValues?.questions);

        const formData: FormData = {
            ...formValues,
            languageCode: languageCode ?? 'en',
            questions: [...questionTree],
            answerSchema: createAnswerSchema(questionTree ?? []),
        };

        const { success, message, description, duration, entity }: QuestionBuilderResponse = await axiosClient({
            enableLoading: true,
        }).post(`/api/admin/question-builder/${formData.uuid}`, formData);

        standardApiNotification(success, message, description, duration);

        if (success) {
            const { questions, sites, questionTypes, thankYouUrlQualified, thankYouUrlDisqualified } = entity;

            setQuestionTypes(questionTypes);
            setSites(sites);

            if (questions) {
                const newFlattenedQuestions = entity?.questions.map((question: ModifiedQuestion) =>
                    convertQuestionTreeToProps(question, languageCode as LanguageCode)
                );

                questionBuilderForm?.setFieldsValue({ questions: [...flattenTree(newFlattenedQuestions)] });
            }

            if (primaryForm) {
                const thankYouUrlQualifiedValue =
                    thankYouUrlQualified?.[languageCode] ?? thankYouUrlQualified?.en ?? '';
                const thankYouUrlDisqualifiedValue =
                    thankYouUrlDisqualified?.[languageCode] ?? thankYouUrlDisqualified?.en ?? '';

                primaryForm.setFieldsValue({
                    thankYouUrlQualifiedValue,
                    thankYouUrlDisqualifiedValue,
                    thankYouUrlQualified,
                    thankYouUrlDisqualified,
                });
            }
        }
    };

    return { submitQuestionBuilder };
};

export default useSubmitQuestionBuilder;
