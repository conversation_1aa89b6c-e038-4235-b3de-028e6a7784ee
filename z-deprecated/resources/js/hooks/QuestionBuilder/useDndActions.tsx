import { useCallback } from 'react';

import { FormInstance } from 'antd';
import { debounce, findIndex } from 'lodash';

import {
    CancelDrop,
    DragEndEvent,
    DragMoveEvent,
    DragOverEvent,
    DragStartEvent,
    UniqueIdentifier,
} from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import useNewQuestion from '@/hooks/QuestionBuilder/useNewQuestion';
import useProjected from '@/hooks/QuestionBuilder/useProjected';
import { useStoreActions, useStoreState } from '@/store/hooks';
import { UUID } from '@/types/general';
import { ModifiedQuestion } from '@/types/question';
import { QuestionType } from '@/types/question-types';
import buildTree from '@/utils/QuestionBuilder/build-tree';
import { unhideChildren } from '@/utils/QuestionBuilder/children';
import { findQuestion } from '@/utils/QuestionBuilder/find-question';
import flattenTree from '@/utils/QuestionBuilder/flatten-tree';
import removeQuestion from '@/utils/QuestionBuilder/remove-question';

const useDndActions = () => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
    };
    const {
        setActiveId,
        setAddQuestionSettings,
        setIsDragging,
        setIsNewQuestion,
        setOffsetLeft,
        setOverId,
        setQuestionTypes,
    } = useStoreActions(actions => actions.questionBuilderModel);
    const setIsFormDirty = useStoreActions(actions => actions.appSettingsModel.setIsFormDirty);
    const isFormDirty = useStoreState(state => state.appSettingsModel.isFormDirty);
    const questionTypes = useStoreState(state => state.questionBuilderModel.questionTypes);
    const { getNewQuestion } = useNewQuestion();
    const { projected } = useProjected();

    const handleDragStart = ({ active }: DragStartEvent) => {
        setIsDragging(true);
        setActiveId(active.id);
        setIsNewQuestion(active?.data?.current?.newQuestion ?? false);
    };

    const handleDragOver = useCallback(
        debounce(({ over }: DragOverEvent) => {
            setOverId(over?.id ?? null);
        }, 100),
        []
    );

    const handleDragMove = useCallback(
        debounce(({ active, delta }: DragMoveEvent) => {
            if (!active?.data?.current?.newQuestion) {
                setOffsetLeft(delta.x);
            }
        }, 100),
        []
    );

    const handleDragEnd = ({ active, over }: DragEndEvent) => {
        resetState();

        if (over) {
            const {
                data: { current: activeCurrent },
            } = active;
            const { level, parentId } = projected ?? { level: 0, parentId: null };

            const flattenedQuestions: ModifiedQuestion[] = [...questionBuilderForm.getFieldsValue().questions];
            const overIndex = flattenedQuestions.findIndex(({ id }: { id: string }) => id === over?.id);
            const activeIndex = flattenedQuestions.findIndex(({ id }: { id: string }) => id === active.id);

            if (activeIndex !== -1 && overIndex !== -1) {
                const activeTreeItem = activeCurrent?.newQuestion
                    ? getNewQuestion(activeCurrent as QuestionType)
                    : flattenedQuestions[activeIndex];

                flattenedQuestions[activeIndex] = {
                    ...activeTreeItem,
                    level,
                    parentId,
                    condition: null,
                    conditionSource: null,
                    conditionOptions: undefined,
                    conditionValue: 0,
                };

                const sortedItems = arrayMove(flattenedQuestions, activeIndex, overIndex);
                const newFlattenedQuestions: ModifiedQuestion[] = unhideChildren(sortedItems);

                questionBuilderForm.setFieldsValue({ questions: [...newFlattenedQuestions] });

                if (!isFormDirty) {
                    setIsFormDirty(true);
                }
            }
        }
    };

    const handleAddNewQuestion = (
        questionType: QuestionType,
        addAfterQuestionId: UUID | null = null,
        parentId: UUID | null = null,
        isChild = false
    ) => {
        resetState();

        let flattenedQuestions: ModifiedQuestion[] = [...questionBuilderForm.getFieldsValue().questions];
        flattenedQuestions = addStartPagingQuestion(questionType, flattenedQuestions);
        const newIndex = flattenedQuestions.length;
        let overIndex = 0;
        let addAfterQuestion: ModifiedQuestion;
        let parentQuestion: ModifiedQuestion | null = null;
        let level = 0;

        if (addAfterQuestionId) {
            overIndex = flattenedQuestions.findIndex(({ id }: { id: string }) => id === addAfterQuestionId);
            addAfterQuestion = { ...flattenedQuestions[overIndex] };

            if (isChild) {
                const parentIdex = flattenedQuestions.findIndex(({ id }: { id: string }) => id === parentId);
                parentQuestion = { ...flattenedQuestions[parentIdex] };
                level = addAfterQuestion.level + 1;
            }
        }

        const newTreeItem = getNewQuestion(questionType);

        flattenedQuestions[newIndex] = {
            ...newTreeItem,
            level,
            parentId: isChild && parentId ? parentId : null,
            condition: null,
            conditionSource: null,
            conditionOptions: undefined,
            conditionValue: 0,
        };

        const sortedItems = arrayMove(flattenedQuestions, newIndex, overIndex + 1);
        let newFlattenedQuestions: ModifiedQuestion[] = unhideChildren(sortedItems);
        newFlattenedQuestions = addEndPagingQuestion(questionType, newFlattenedQuestions);

        questionBuilderForm.setFieldsValue({ questions: [...newFlattenedQuestions] });

        setAddQuestionSettings({ addAfterQuestion: flattenedQuestions[newIndex], parentQuestion, isChild });

        setTimeout(() => {
            questionBuilderForm.scrollToField(['questions', 'doNotSubmit', newTreeItem.id], { behavior: 'smooth' });
        }, 500);

        if (!isFormDirty) {
            setIsFormDirty(true);
        }
    };

    const addStartPagingQuestion = (
        questionType: QuestionType,
        flattenedQuestions: ModifiedQuestion[]
    ): ModifiedQuestion[] => {
        const questions = [...flattenedQuestions];

        if (questionType.name === 'pageBreak') {
            const hasStartPaging = questions.some(question => question.questionType.name === 'startPaging');

            if (!hasStartPaging) {
                const startPagingQuestionType = questionTypes.find(type => type.name === 'startPaging') as QuestionType;

                const newIndex = questions.length;
                const newTreeItem = getNewQuestion(startPagingQuestionType);

                questions[newIndex] = {
                    ...newTreeItem,
                    level: 0,
                    parentId: null,
                    condition: null,
                    conditionSource: null,
                    conditionOptions: undefined,
                    conditionValue: 0,
                };

                return arrayMove(questions, newIndex, 0);
            }
        }

        return questions;
    };

    const addEndPagingQuestion = (
        questionType: QuestionType,
        flattenedQuestions: ModifiedQuestion[]
    ): ModifiedQuestion[] => {
        const questions = [...flattenedQuestions];

        if (questionType.name === 'pageBreak') {
            const hasEndPaging = questions.some(question => question.questionType.name === 'endPaging');

            if (!hasEndPaging) {
                const endPagingQuestionType = questionTypes.find(type => type.name === 'endPaging') as QuestionType;

                const newIndex = questions.length;
                const newTreeItem = getNewQuestion(endPagingQuestionType);

                questions[newIndex] = {
                    ...newTreeItem,
                    level: 0,
                    parentId: null,
                    condition: null,
                    conditionSource: null,
                    conditionOptions: undefined,
                    conditionValue: 0,
                };
            }
        }

        return questions;
    };

    const handleDragCancel = () => {
        resetState();
    };

    const handleRemove = (id: UniqueIdentifier) => {
        const flattenedQuestions = questionBuilderForm.getFieldsValue().questions;
        const treeQuestions = buildTree([...flattenedQuestions]);
        const newTreeQuestions = removeQuestion([...treeQuestions], id);
        const newFlattenedQuestions = flattenTree([...newTreeQuestions]);
        questionBuilderForm.setFieldsValue({ questions: [...newFlattenedQuestions] });

        /**
         * Check if predefined question and enable it in the Question Type List
         */
        const { questionType } = findQuestion([...flattenedQuestions], id) ?? {};

        if (questionType?.isPredefined) {
            const questionTypeIndex = findIndex(questionTypes, ['uuid', questionType.uuid]);

            if (questionTypeIndex !== -1) {
                questionTypes[questionTypeIndex].isDisabled = false;

                setQuestionTypes([...questionTypes]);
            }
        }

        if (!isFormDirty) {
            setIsFormDirty(true);
        }
    };

    /**
     * Cancel drop if any of the following are true:
     * 1. Attempting to drag on item over a question that doesn't exist in the store and it's not the top or bottom dropzone.
     *    The overItem is empty and the dropzone is not the top or bottom
     * 2. Attempting to create a child on an item that doesn't allow children.
     *    The overItem does not allow children and the projected parentId is equal to the over.id
     * 3. Attempting to drag an item as a child of one of the it's parent children (active.id === overItem.parentId)
     * 4. Attempting to drag an item onto itself (active.id === over.id)
     * @param param0
     * @returns
     */
    const cancelDrop: CancelDrop = async ({ active, over }) => {
        const flattenedQuestions = questionBuilderForm.getFieldsValue().questions;
        const overItem = findQuestion(flattenedQuestions, over?.id as number);

        if (over?.id && active?.id && projected) {
            if (
                (!overItem && !['drop-top', 'drop-bottom'].includes(over.id as string)) ||
                (!overItem?.questionType?.isAllowChildren && projected.parentId === over?.id) ||
                active.id === overItem?.parentId ||
                active.id === over.id
            ) {
                const newFlattenedQuestions = unhideChildren(flattenedQuestions);
                questionBuilderForm.setFieldsValue({ questions: [...newFlattenedQuestions] });

                return true;
            }

            return false;
        }

        return true;
    };

    const resetState = () => {
        setOverId(null);
        setActiveId(null);
        setOffsetLeft(0);
        setIsDragging(false);

        document.body.style.setProperty('cursor', '');
    };

    return {
        handleAddNewQuestion,
        handleDragStart,
        handleDragOver,
        handleDragMove,
        handleDragEnd,
        handleDragCancel,
        handleRemove,
        cancelDrop,
    };
};

export default useDndActions;
