import { v4 as uuid } from 'uuid';

import { LanguageCode } from '@/Components/Languages/types/languages';
import { useStoreActions, useStoreState } from '@/store/hooks';
import { ModifiedQuestion } from '@/types/question';
import { QuestionType } from '@/types/question-types';
import {
    handleAcceptTerms,
    handleBestTime,
    handleCalcBmi,
    handleDbo,
    handleDivider,
    handleEmail,
    handleEssay,
    handleFirstName,
    handleFreeFormText,
    handleLastName,
    handleMultipleChoice,
    handleNextButton,
    handleNumber,
    handleOneOfSite,
    handlePageBreak,
    handlePhone,
    handlePredefined,
    handleSingleChoice,
    handleStartPaging,
    handleStext,
    handleSubmitButton,
} from '@/utils/QuestionBuilder/convert-question-tree-to-props';

const useNewQuestion = () => {
    const sites = useStoreState(state => state.questionBuilderModel.sites);
    const questionTypes = useStoreState(state => state.questionBuilderModel.questionTypes);
    const languageCode = useStoreState(state => state.questionBuilderModel.languageCode);
    const setQuestionTypes = useStoreActions(actions => actions.questionBuilderModel.setQuestionTypes);

    const getNewQuestion = (questionType: QuestionType): ModifiedQuestion => {
        const questionUUID = uuid();
        let initQuestionProps: ModifiedQuestion = {
            children: [],
            condition: null,
            conditionSource: null,
            conditionOptions: undefined,
            conditionValue: 0,
            depth: 0,
            id: questionUUID,
            index: 0,
            isConfirmed: '0',
            isPredefined: false,
            isEncrypted: '0',
            isRequired: '1',
            level: 0,
            options: [],
            optionLabels: [],
            parentId: null,
            question: { [languageCode]: '' },
            questionLabel: questionType.alias.replace(' (Predefined)', ''),
            questionType: { ...questionType },
            rawOptions: null,
            settings: {},
            uuid: questionUUID,
        };

        initQuestionProps = handlePredefined(initQuestionProps, setQuestionTypes, questionTypes, true);
        initQuestionProps = handleAcceptTerms(initQuestionProps, languageCode as LanguageCode, true);
        initQuestionProps = handleMultipleChoice(initQuestionProps, languageCode as LanguageCode, true);
        initQuestionProps = handleSingleChoice(initQuestionProps, languageCode as LanguageCode, true);
        initQuestionProps = handleOneOfSite(initQuestionProps, languageCode as LanguageCode, sites, true);
        initQuestionProps = handleBestTime(initQuestionProps);
        initQuestionProps = handleCalcBmi(initQuestionProps, languageCode as LanguageCode);
        initQuestionProps = handleDbo(initQuestionProps);
        initQuestionProps = handleDivider(initQuestionProps);
        initQuestionProps = handleEmail(initQuestionProps);
        initQuestionProps = handleEssay(initQuestionProps);
        initQuestionProps = handleFirstName(initQuestionProps);
        initQuestionProps = handleLastName(initQuestionProps);
        initQuestionProps = handleNextButton(initQuestionProps);
        initQuestionProps = handleNumber(initQuestionProps, true);
        initQuestionProps = handlePageBreak(initQuestionProps);
        initQuestionProps = handlePhone(initQuestionProps);
        initQuestionProps = handleStartPaging(initQuestionProps);
        initQuestionProps = handleStext(initQuestionProps);
        initQuestionProps = handleFreeFormText(initQuestionProps);
        initQuestionProps = handleSubmitButton(initQuestionProps);

        return initQuestionProps;
    };

    return { getNewQuestion };
};

export default useNewQuestion;
