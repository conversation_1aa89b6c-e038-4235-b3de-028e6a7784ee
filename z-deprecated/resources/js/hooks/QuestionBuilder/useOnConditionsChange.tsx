import { SingleValue } from 'react-select';

import { merge } from 'lodash';
import hasIn from 'lodash/hasIn';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import useBuildOptionsCondition from '@/hooks/QuestionBuilder/useBuildOptionsCondition';
import { ConvertedQuestion } from '@/types/question';
import { ReactSelectOptionType } from '@/types/react-select';

const useOnConditionsChange = () => {
    const { editQuestionBuilderForm, questionBuilderForm } = useQuestionBuilderFormContext();
    const { buildOptionsCondition } = useBuildOptionsCondition();
    const onConditionsChange = (
        data:
            | ConvertedQuestion['conditionValue']
            | ConvertedQuestion['conditionOptions']
            | SingleValue<ReactSelectOptionType>,
        type: string,
        editIndex: number,
        parentIndex: number
    ): void => {
        const fieldValues = merge({}, editQuestionBuilderForm?.getFieldsValue());
        const parentFieldValues = questionBuilderForm?.getFieldValue(['questions', parentIndex]);

        if (fieldValues && hasIn(fieldValues, ['id']) && parentFieldValues && hasIn(parentFieldValues, ['id'])) {
            if (type === 'number-input') {
                fieldValues.conditionValue = data as ConvertedQuestion['conditionValue'];
            } else {
                fieldValues.conditionOptions = data as ConvertedQuestion['conditionOptions'];
            }

            fieldValues.condition = buildOptionsCondition(fieldValues, parentFieldValues);
            editQuestionBuilderForm?.setFieldsValue({ ...fieldValues });
            questionBuilderForm?.setFields([{ name: ['questions', editIndex], value: fieldValues }]);
        }
    };

    return { onConditionsChange };
};

export default useOnConditionsChange;
