import { useMemo } from 'react';

import { FormInstance } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import { useStoreState } from '@/store/hooks';
import getProjection from '@/utils/QuestionBuilder/get-projection';

const useProjected = () => {
    const { questionBuilderForm } = useQuestionBuilderFormContext() as { questionBuilderForm: FormInstance };
    const { questions: flattenedQuestions } = questionBuilderForm.getFieldsValue();
    const activeId = useStoreState(state => state.questionBuilderModel.activeId);
    const overId = useStoreState(state => state.questionBuilderModel.overId);
    const offsetLeft = useStoreState(state => state.questionBuilderModel.offsetLeft);
    const indentationWidth = useStoreState(state => state.questionBuilderModel.indentationWidth);

    const projected = useMemo(
        () =>
            activeId && overId
                ? getProjection(flattenedQuestions ?? [], activeId, overId, offsetLeft, indentationWidth)
                : null,
        [activeId, overId, offsetLeft]
    );

    return { projected };
};

export default useProjected;
