import { useCallback } from 'react';

import { FormInstance } from 'antd';
import { debounce, findIndex } from 'lodash';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';

const useOptionTextChange = ({ index }: { index: number }) => {
    const { questionBuilderForm, editQuestionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
        editQuestionBuilderForm: FormInstance;
    };

    const onOptionTextChange = useCallback(
        debounce((e, optionValue) => {
            const options = questionBuilderForm.getFieldValue(['questions', index, 'options']);
            const optIndex = findIndex(options, { value: optionValue });
            const option = options[optIndex];

            option.label = e.target.value;

            questionBuilderForm.setFieldValue(['questions', index, 'options', optIndex], option);
            questionBuilderForm.setFieldValue(['questions', index, 'optionLabels', optIndex], e.target.value);

            editQuestionBuilderForm.setFieldValue(['options', optIndex], option);
        }, 200),
        [index]
    );

    return { onOptionTextChange };
};

export default useOptionTextChange;
