import { Form, FormInstance } from 'antd';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';

const usePrimaryFormSettings = () => {
    const { primaryForm } = useQuestionBuilderFormContext() as {
        primaryForm: FormInstance;
    };

    const globalSettings = Form.useWatch('settings', primaryForm);

    return { globalSettings };
};

export default usePrimaryFormSettings;
