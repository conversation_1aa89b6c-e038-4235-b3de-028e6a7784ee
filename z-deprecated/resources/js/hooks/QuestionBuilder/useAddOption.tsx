import React from 'react';

import { hasIn, merge } from 'lodash';
import { v4 as uuid } from 'uuid';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import { store } from '@/store';
import { ANY_TODO } from '@/types/general';
import { SiteDataList } from '@/types/site-data';

const useAddOption = () => {
    const { editQuestionBuilderForm, questionBuilderForm } = useQuestionBuilderFormContext();

    const addOption = (e: React.MouseEvent<HTMLElement>, editIndex: number): void => {
        e.preventDefault();

        const { sites } = store.getState().questionBuilderModel;
        const fieldValues = merge({}, editQuestionBuilderForm?.getFieldsValue());

        if (fieldValues && hasIn(fieldValues, ['id'])) {
            let label = '';
            let value = uuid();

            if (!fieldValues.optionLabels) {
                if (fieldValues.questionType.isAllowSites) {
                    label = sites[0].name;
                    value = sites[0].site_public;
                }

                fieldValues.options = [{ label, value }];
                fieldValues.optionLabels = [label];
            } else {
                const options = [...fieldValues.options];
                const optionLabels = [...fieldValues.optionLabels];

                if (fieldValues.questionType.isAllowSites) {
                    const availableSites = sites.filter(
                        (site: SiteDataList) =>
                            fieldValues.options.findIndex((option: ANY_TODO) => option.value === site.site_public) < 0
                    );

                    /**
                     * Didn't check that a site is available because we should never get if there aren't sites.
                     * If there are no available sites, there is a bug in the code that calls this method.
                     */
                    label = availableSites[0].name;
                    value = availableSites[0].site_public;
                }

                options.push({ label, value });
                optionLabels.push(label);

                fieldValues.options = [...options];
                fieldValues.optionLabels = [...optionLabels];
            }

            questionBuilderForm?.setFields([
                { name: ['questions', editIndex, 'options'], value: [...fieldValues.options] },
                { name: ['questions', editIndex, 'optionLabels'], value: [...fieldValues.optionLabels] },
            ]);
            editQuestionBuilderForm?.setFields([
                { name: 'options', value: [...fieldValues.options] },
                { name: 'optionLabels', value: [...fieldValues.optionLabels] },
            ]);
        }
    };

    return { addOption };
};

export default useAddOption;
