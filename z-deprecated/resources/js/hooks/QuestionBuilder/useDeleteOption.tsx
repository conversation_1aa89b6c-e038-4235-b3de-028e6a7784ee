import { merge } from 'lodash';
import hasIn from 'lodash/hasIn';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import { QuestionOption } from '@/types/question-options';

const useDeleteOption = () => {
    const { editQuestionBuilderForm, questionBuilderForm } = useQuestionBuilderFormContext();
    const deleteOption = (optionValue: string, namePath: (number | string)[]): void => {
        const fieldValues = merge({}, questionBuilderForm?.getFieldValue(namePath));
        const setFields = [];
        const editSetFields = [];

        if (fieldValues && hasIn(fieldValues, ['id'])) {
            const options = fieldValues.options.filter(
                (questionOption: QuestionOption) => questionOption.value !== optionValue
            );
            const optionLabels = options.map((questionOption: QuestionOption) => questionOption.label);

            setFields.push({ name: [...namePath, 'options'], value: [...options] });
            setFields.push({ name: [...namePath, 'optionLabels'], value: [...optionLabels] });

            editSetFields.push({ name: 'options', value: [...options] });
            editSetFields.push({ name: 'optionLabels', value: [...optionLabels] });

            editQuestionBuilderForm?.setFields(editSetFields);
            questionBuilderForm?.setFields(setFields);
        }
    };

    return { deleteOption };
};

export default useDeleteOption;
