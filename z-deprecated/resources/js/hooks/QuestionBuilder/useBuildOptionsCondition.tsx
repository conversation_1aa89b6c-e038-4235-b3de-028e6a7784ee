import { isEmpty } from 'lodash';

import { KeyValueGeneric } from '@/types/general';
import { ModifiedQuestion } from '@/types/question';

const useBuildOptionsCondition = () => {
    const buildOptionsCondition = (
        fieldValues: ModifiedQuestion,
        parentFieldValues: Pick<ModifiedQuestion, 'questionType'>
    ): string | null => {
        let condition = null;
        const { conditionOptions, conditionValue } = fieldValues;

        if (parentFieldValues.questionType.isAllowOptions || parentFieldValues.questionType.isAllowSites) {
            if (!isEmpty(conditionOptions) && conditionOptions?.length === 1) {
                const optionsString = (conditionOptions as (string | number)[])[0];
                condition = `typeof ANSWER !== "undefined" && ANSWER.length && ANSWER.includes('${optionsString}')`;
            } else if (Array.isArray(conditionOptions) && conditionOptions.length > 1) {
                const optionsString = conditionOptions.join("', '");
                condition = `typeof ANSWER !== "undefined" && ANSWER.length && ANSWER.some(e => ['${optionsString}'].includes(e))`;
            }
        } else {
            condition = `ANSWER ${(conditionOptions as KeyValueGeneric).value} ${conditionValue || 0}`;
        }

        return condition;
    };

    return { buildOptionsCondition };
};

export default useBuildOptionsCondition;
