import { useCallback } from 'react';

import { FormInstance } from 'antd';
import { debounce } from 'lodash';

import { useQuestionBuilderFormContext } from '@/Contexts/QuestionBuilderFormContext';
import { useStoreActions, useStoreState } from '@/store/hooks';
import { ModifiedQuestion } from '@/types/question';

const useEditFormActions = () => {
    const { questionBuilderForm, editQuestionBuilderForm } = useQuestionBuilderFormContext() as {
        questionBuilderForm: FormInstance;
        editQuestionBuilderForm: FormInstance;
    };
    const editIndex = useStoreState(state => state.questionBuilderModel.editIndex);
    const isFormDirty = useStoreState(state => state.appSettingsModel.isFormDirty);
    const setEditIndex = useStoreActions(actions => actions.questionBuilderModel.setEditIndex);
    const setFormErrors = useStoreActions(actions => actions.questionBuilderModel.setFormErrors);
    const setIsFormDirty = useStoreActions(actions => actions.appSettingsModel.setIsFormDirty);
    const setAddQuestionSettings = useStoreActions(actions => actions.questionBuilderModel.setAddQuestionSettings);

    const handleFormValuesChange = useCallback(
        debounce(() => {
            if (!isFormDirty) {
                setIsFormDirty(true);
            }

            editQuestionBuilderForm
                ?.validateFields()
                .then(() => {
                    setFormErrors({ formErrors: [], editIndex });
                })
                .catch(({ errorFields }) => {
                    const questionId = editQuestionBuilderForm.getFieldValue('id');
                    setFormErrors({ formErrors: errorFields, editIndex });
                    questionBuilderForm.scrollToField(['questions', 'doNotSubmit', questionId], { behavior: 'smooth' });
                });
        }, 500),
        [editIndex]
    );

    const saveToQuestionBuilderForm = useCallback(
        (formData: ModifiedQuestion) => {
            if (editIndex === null) return;

            const newFlattenedQuestions = [...questionBuilderForm.getFieldsValue().questions];
            newFlattenedQuestions[editIndex] = { ...formData };

            questionBuilderForm.setFieldsValue({ questions: [...newFlattenedQuestions] });
            setEditIndex(null);
        },
        [editIndex]
    );

    const openEditForm = (index: number) => {
        setAddQuestionSettings({ addAfterQuestion: null, parentQuestion: null, isChild: false });

        if (editIndex !== index && editIndex !== null) {
            editQuestionBuilderForm
                ?.validateFields()
                .then(() => {
                    if (!isFormDirty) {
                        setIsFormDirty(true);
                    }

                    saveToQuestionBuilderForm(editQuestionBuilderForm.getFieldsValue());
                    setEditIndex(index);
                })
                .catch(({ errorFields }) => {
                    const questionId = editQuestionBuilderForm.getFieldValue('id');
                    setFormErrors({ formErrors: errorFields, editIndex });
                    questionBuilderForm.scrollToField(['questions', 'doNotSubmit', questionId], { behavior: 'smooth' });
                });
        } else {
            setEditIndex(index);
        }
    };

    const cancelEditForm = useCallback(() => {
        if (editIndex === null) return;

        const newFlattenedQuestions = [...questionBuilderForm.getFieldsValue().questions];
        const editingQuestion = editQuestionBuilderForm.getFieldsValue();
        newFlattenedQuestions[editIndex] = {
            ...JSON.parse(editingQuestion.json),
        };
        questionBuilderForm.setFieldsValue({
            questions: [...newFlattenedQuestions],
        });
        setEditIndex(null);
        setFormErrors({ formErrors: [], editIndex: null });
    }, [editIndex]);

    return {
        cancelEditForm,
        handleFormValuesChange,
        openEditForm,
        saveToQuestionBuilderForm,
    };
};

export default useEditFormActions;
