import { ReactNode, useEffect, useState } from 'react';

import md5 from 'md5';

import { useStoreState } from '@/store/hooks';
import { getRandomInt } from '@/utils/helpers';

const useShowFormErrors = (index: number) => {
    const [errorMessages, setErrorMessages] = useState<ReactNode>();
    const formErrors = useStoreState(state => state.questionBuilderModel.formErrors);

    useEffect(() => {
        if (formErrors[index]) {
            setErrorMessages(
                <>
                    {formErrors[index].map(error => (
                        <div key={`error-${getRandomInt(388738)}`}>{error}</div>
                    ))}
                </>
            );
        } else {
            setErrorMessages(undefined);
        }
    }, [md5(JSON.stringify(formErrors))]);

    return { errorMessages };
};

export default useShowFormErrors;
