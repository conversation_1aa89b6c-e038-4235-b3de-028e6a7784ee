import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { light } from '@fortawesome/fontawesome-svg-core/import.macro';

const useQuestionTypeIcons = () => {
    const getQuestionTypeIcon = (type: string) => {
        const icons: Record<string, IconProp> = {
            acceptTerms: light('circle-check'),
            anyof: light('list-check'),
            bestTime: light('circle'),
            calcBMI: light('weight-hanging'),
            dob: light('cake-candles'),
            email: light('at'),
            essay: light('paragraph'),
            firstName: light('id-card'),
            lastName: light('id-card'),
            number: light('input-numeric'),
            oneof: light('list-radio'),
            oneofsite: light('location-dot'),
            pageBreak: light('file-dashed-line'),
            phone: light('mobile-retro'),
            stext: light('i-cursor'),
            freeFormText: light('paragraph'),
        };

        return icons[type] ?? light('pen-field');
    };

    return { getQuestionTypeIcon };
};

export default useQuestionTypeIcons;
