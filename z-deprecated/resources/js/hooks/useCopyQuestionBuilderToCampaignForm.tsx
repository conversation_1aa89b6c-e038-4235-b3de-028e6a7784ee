import { useRoute } from 'ziggy-js';

import { router, usePage } from '@inertiajs/react';

import { standardApiNotification } from '@/Components/Notifications';
import { useModalContext } from '@/Contexts/ModalContext';
import useAxiosClient from '@/hooks/useAxiosClient';
import { ApiResponseData } from '@/types/api-response-data';
import { PageProps, UUID } from '@/types/general';
import { QuestionBuilderPageProps } from '@/types/question-builder';
import { ReactSelectOptionType } from '@/types/react-select';

const useCopQuestionBuilderToCampaignForm = () => {
    const route = useRoute();
    const { entity } = usePage<PageProps<QuestionBuilderPageProps>>().props;
    const { axiosClient } = useAxiosClient();
    const { setShowModal } = useModalContext();

    const copQuestionBuilderToCampaignForm = async (formData: { uuid: ReactSelectOptionType }) => {
        const {
            success,
            message,
            description,
            duration,
            uuid: newCampaignUUID,
        }: ApiResponseData & {
            uuid: UUID;
        } = await axiosClient({ enableLoading: true }).get(
            route('admin.question-builder.copyToAnotherForm', {
                sourceCampaignForm: entity.uuid,
                targetCampaignForm: formData.uuid.value,
            })
        );

        standardApiNotification(success, message, description, duration);
        setShowModal(false);

        if (success) {
            router.visit(route('admin.form-builder.edit', newCampaignUUID));
        }
    };

    return { copQuestionBuilderToCampaignForm };
};

export default useCopQuestionBuilderToCampaignForm;
