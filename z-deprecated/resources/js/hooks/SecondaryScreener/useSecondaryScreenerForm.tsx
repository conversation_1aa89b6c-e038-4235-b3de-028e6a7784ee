import { useEffect } from 'react';

import { reduce } from 'lodash';

import { usePage } from '@inertiajs/react';

import { LanguageCode } from '@/Components/Languages/types/languages';
import { useStoreActions } from '@/store/hooks';
import { EmbedFormData, EmbedFormPageProps, PaginatedQuestionReturn } from '@/types/embed-form';
import { ANY_TODO, PageProps } from '@/types/general';
import { ModifiedQuestion } from '@/types/question';
import { ReferralData } from '@/types/referral-data';
import buildFormQuestions from '@/utils/EmbedForm/build-form-questions';
import getLanguageCodes from '@/utils/EmbedForm/get-language-codes';
import paginateQuestions from '@/utils/EmbedForm/paginate-questions';

const useSecondaryScreenerForm = () => {
    const { embedFormData } = usePage<
        PageProps<{
            embedFormData: EmbedFormData;
            formId: string;
            referral: ReferralData;
            campaignForm: ANY_TODO;
        }>
    >().props;
    const { lang } = usePage<PageProps<EmbedFormPageProps>>().props;

    const setFormQuestions = useStoreActions(actions => actions.embedFormModel.setFormQuestions);
    const setFormContainers = useStoreActions(actions => actions.embedFormModel.setFormContainers);
    const setLanguageCodes = useStoreActions(actions => actions.embedFormModel.setLanguageCodes);
    const setPageCount = useStoreActions(actions => actions.embedFormModel.setPageCount);
    const setPaginatedQuestionIds = useStoreActions(actions => actions.embedFormModel.setPaginatedQuestionIds);
    const setSelectedLanguage = useStoreActions(actions => actions.embedFormModel.setSelectedLanguage);
    const setSettings = useStoreActions(actions => actions.embedFormModel.setSettings);
    const setTrackingId = useStoreActions(actions => actions.embedFormModel.setTrackingId);

    useEffect(() => {
        const objLanguages = getLanguageCodes(lang ?? 'en', embedFormData.languages);
        let formQuestions: ModifiedQuestion[] = [];

        if (embedFormData.questions?.length > 0) {
            formQuestions = buildFormQuestions(embedFormData.questions, objLanguages.languageCodes as LanguageCode[]);
        }

        const pageCount = reduce(
            formQuestions,
            (sum, question) => {
                if (question.questionType.name === 'pageBreak') {
                    return sum + 1;
                }
                return sum;
            },
            1
        );

        const results: PaginatedQuestionReturn = paginateQuestions({ formQuestions });

        setFormContainers(results.containers);
        setPaginatedQuestionIds(results.ids);
        setFormQuestions(formQuestions);
        setPageCount(pageCount);
        setSettings(embedFormData?.settings ?? {});
        setTrackingId(embedFormData?.trackingId);
        setLanguageCodes(objLanguages.languageCodes);
        setSelectedLanguage(objLanguages.selectedLanguage);
    }, []);
};

export default useSecondaryScreenerForm;
