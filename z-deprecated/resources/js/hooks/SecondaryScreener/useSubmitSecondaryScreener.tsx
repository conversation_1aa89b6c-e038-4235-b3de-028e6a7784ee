import { useRoute } from 'ziggy-js';

import { router, usePage } from '@inertiajs/react';

import { LanguageCode } from '@/Components/Languages/types/languages';
import useAxiosClient from '@/hooks/useAxiosClient';
import useEventCallback from '@/hooks/useEventCallback';
import { SecondaryScreenerSubmission } from '@/Pages/CampaignForms/types';
import { useStoreActions, useStoreState } from '@/store/hooks';
import { ApiResponseData } from '@/types/api-response-data';
import { ANY_TODO, PageProps, UUID } from '@/types/general';
import convertAnswerPropsToTree from '@/utils/EmbedForm/convert-answer-props-to-tree';

const useSubmitSecondaryScreener = () => {
    const route = useRoute();
    const { setErrorMessage, setIsFormSubmitting } = useStoreActions(actions => actions.embedFormModel);
    const clientIp = useStoreState(state => state.embedFormModel.clientIp);
    const formQuestions = useStoreState(state => state.embedFormModel.formQuestions);
    const languageCodes = useStoreState(state => state.embedFormModel.languageCodes);
    const selectedLanguage = useStoreState(state => state.embedFormModel.selectedLanguage);
    const { axiosClient } = useAxiosClient();
    const { referral } = usePage<PageProps<{ referral: ANY_TODO }>>().props;

    const submitSecondaryScreener = useEventCallback(
        async ({
            formValues,
            formId,
            isEdit,
            isDraft = false,
        }: {
            formValues: {
                [id: string]: string | number[] | number;
            };
            formId: string;
            isEdit: boolean;
            isDraft: boolean;
        }): Promise<void> => {
            const form: SecondaryScreenerSubmission = {
                answers: convertAnswerPropsToTree(formValues, formQuestions, languageCodes as LanguageCode[]),
                formId,
                lang: selectedLanguage,
                leadId: referral.uuid,
                isDraft,
            };

            const { success, campaignFormUUID }: ApiResponseData & { campaignFormUUID: UUID } = await axiosClient({
                enableLoading: true,
            }).post(
                isEdit
                    ? route('referral-answers.update', referral.uuid)
                    : route('referral-answers.store', referral.uuid),
                form
            );

            if (success) {
                setIsFormSubmitting(false);
                router.visit(route('referral-answers.show', [referral.uuid, campaignFormUUID]));
            } else {
                setErrorMessage('There was an error submitting your form. Please try again.');
                setIsFormSubmitting(false);
            }
        },
        [clientIp, formQuestions, languageCodes, selectedLanguage, setErrorMessage]
    );

    return { submitSecondaryScreener };
};

export default useSubmitSecondaryScreener;
