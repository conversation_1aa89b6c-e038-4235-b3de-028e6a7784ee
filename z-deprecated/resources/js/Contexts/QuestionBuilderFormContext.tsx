import { createContext, useContext, useEffect, useMemo, useState } from 'react';

import { Form, FormInstance } from 'antd';
import isEmpty from 'lodash/isEmpty';

import { usePage } from '@inertiajs/react';

import { LanguageCode } from '@/Components/Languages/types/languages';
import { useStoreActions } from '@/store/hooks';
import { PageProps } from '@/types/general';
import { ModifiedQuestion } from '@/types/question';
import { QuestionBuilderInitialValues, QuestionBuilderPageProps } from '@/types/question-builder';
import convertQuestionTreeToProps from '@/utils/QuestionBuilder/convert-question-tree-to-props';
import flattenTree from '@/utils/QuestionBuilder/flatten-tree';
import globalSettings from '@/utils/QuestionBuilder/settings/global-settings';

type QuestionBuilderFormContextProps = {
    editQuestionBuilderForm: FormInstance | undefined;
    initialFormValues: QuestionBuilderInitialValues;
    isLoading: boolean;
    primaryForm: FormInstance | undefined;
    questionBuilderForm: FormInstance | undefined;
};

export const QuestionBuilderFormContext = createContext<QuestionBuilderFormContextProps>({
    editQuestionBuilderForm: undefined,
    initialFormValues: {},
    isLoading: true,
    primaryForm: undefined,
    questionBuilderForm: undefined,
});

export const useQuestionBuilderFormContext = () => useContext(QuestionBuilderFormContext);

const QuestionBuilderFormProvider = ({ children }: { children: React.ReactNode }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [initialFormValues, setInitialFormValues] = useState({});
    const { entity } = usePage<PageProps<QuestionBuilderPageProps>>().props;
    const setIsFormDirty = useStoreActions(actions => actions.appSettingsModel.setIsFormDirty);
    const setQuestionTypes = useStoreActions(actions => actions.questionBuilderModel.setQuestionTypes);
    const setSites = useStoreActions(actions => actions.questionBuilderModel.setSites);
    const setLanguageCode = useStoreActions(actions => actions.questionBuilderModel.setLanguageCode);
    const [form] = Form.useForm();
    const [editForm] = Form.useForm();
    const [primaryForm] = Form.useForm();

    useEffect(() => {
        const {
            doNotSubmit,
            languageCode,
            questions,
            questionTypes,
            settings,
            sites,
            thankYouUrlQualified,
            thankYouUrlDisqualified,
            calendlyEventType,
            uuid,
        } = entity;
        let questionProps: ModifiedQuestion[] = [];
        const modifiedQuestionTypes = questionTypes.map(questionType => ({
            ...questionType,
            isDisabled: questionType.name === 'oneofsite' && sites.length === 0 ? true : questionType.isDisabled,
        }));

        setQuestionTypes([...modifiedQuestionTypes]);
        setSites([...(sites ?? [])]);

        if (questions) {
            questionProps = questions.map((questionTree: ModifiedQuestion) =>
                convertQuestionTreeToProps(questionTree, languageCode as LanguageCode)
            );
        }

        if (settings?.general?.customFont?.link) {
            const customFont = document.createElement('link');
            customFont.href = settings?.general?.customFont?.link;
            customFont.rel = 'stylesheet';
            document.body.appendChild(customFont);
        }

        const thankYouUrlQualifiedValue = thankYouUrlQualified?.[languageCode] ?? thankYouUrlQualified?.en ?? '';

        const thankYouUrlDisqualifiedValue =
            thankYouUrlDisqualified?.[languageCode] ?? thankYouUrlDisqualified?.en ?? '';

        setInitialFormValues({
            questions: [...flattenTree(questionProps)],
            settings: settings && !isEmpty(settings) ? { ...settings } : { ...globalSettings },
            thankYouUrlQualified,
            thankYouUrlDisqualified,
            thankYouUrlQualifiedValue,
            thankYouUrlDisqualifiedValue,
            calendlyEventType,
            uuid,
            doNotSubmit,
        });
        setLanguageCode(languageCode);
        setIsLoading(false);
        setIsFormDirty(false);
    }, []);

    const value = useMemo(
        () => ({
            initialFormValues,
            isLoading,
            editQuestionBuilderForm: editForm,
            primaryForm,
            questionBuilderForm: form,
        }),
        [form, isLoading]
    );

    return <QuestionBuilderFormContext.Provider value={value}>{children}</QuestionBuilderFormContext.Provider>;
};

export default QuestionBuilderFormProvider;
