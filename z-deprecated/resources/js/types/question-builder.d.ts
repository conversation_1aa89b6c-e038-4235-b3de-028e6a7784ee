import { MutableRefObject } from 'react';

import { RadioGroupOptionType } from 'antd/lib/radio';

import { ApiResponseData } from '@/types/api-response-data';
import { ANY_TODO, EmptyObject, KeyValueGeneric, UUID } from '@/types/general';
import { ModifiedQuestion } from '@/types/question';
import { QuestionType } from '@/types/question-types';
import { ReactSelectOptionType } from '@/types/react-select';
import { SiteDataList } from '@/types/site-data';

type QuestionBuilderPageProps = {
    entity: QuestionBuilderEntity;
};

export type CalendlyEventType = {
    name: string;
    uuid: string;
};

export type QuestionBuilderEntity = {
    campaignName: string;
    doNotSubmit: {
        selectedLanguages: ReactSelectOptionType;
    };
    formPublic: string;
    languageCode: string;
    languages: string[];
    questions: ModifiedQuestion[];
    questionTypes: QuestionType[];
    settings: KeyValueGeneric;
    showInternal: boolean;
    sites: SiteDataList[];
    studyName: string;
    studyUUID: UUID;
    thankYouUrlQualified: KeyValueGeneric<string> | null;
    thankYouUrlDisqualified: KeyValueGeneric<string> | null;
    calendlyEventType: CalendlyEventType;
    calendarEngineIntegrationEnabled: boolean;
    uuid: UUID;
};

export type SensorContext = MutableRefObject<{
    items: ModifiedQuestion[];
    offset: number;
}>;

export type FormItemComponentProps = {
    index: number;
    question: ModifiedQuestion;
    optionType?: RadioGroupOptionType;
};

export interface QuestionBuilderResponse extends ApiResponseData {
    description: string;
    entity: QuestionBuilderEntity;
}

export type QuestionBuilderInitialValues =
    | {
          questions: ModifiedQuestion[];
          settings: ANY_TODO;
          thankYouUrlQualified: KeyValueGeneric<string> | null;
          thankYouUrlDisqualified: KeyValueGeneric<string> | null;
          thankYouUrlQualifiedValue: string;
          thankYouUrlDisqualifiedValue: string;
          uuid: UUID;
      }
    | EmptyObject;
