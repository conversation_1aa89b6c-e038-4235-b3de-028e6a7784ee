import { ANY_TODO, KeyValueGeneric } from '@/types/general';
import { ModifiedQuestion } from '@/types/question';

export type Utms = {
    utm_campaign?: string;
    utm_content?: string;
    utm_medium?: string;
    utm_source?: string;
    utm_term?: string;
};

export type EmbedFormPageProps = {
    formId: string;
    lang?: string;
    utms?: Utms;
    embedFormData?: ANY_TODO;
};

export type EmbedFormRequest = EmbedFormPageProps & {
    clientIp?: string;
    clientUserAgent?: string;
};

export type EmbedFormData = {
    formId: string;
    languages: KeyValueGeneric<string>;
    questions: ModifiedQuestion[];
    settings: KeyValueGeneric;
    trackingId: string;
};

export type ObjLanguages = {
    selectedLanguage: string;
    languageCodes: string[];
};

export interface PaginationQuestionProps {
    formQuestions: ModifiedQuestion[];
}

export type PaginatedQuestionReturn = {
    containers: Array<ModifiedQuestion[]>;
    ids: { [key: number]: Array<string | [string, string]> };
};
