import type { <PERSON><PERSON>ventH<PERSON><PERSON> } from 'react';

import { LanguageCode } from '@/Components/Languages/types/languages';
import { KeyValueGeneric } from '@/types/general';
import { ReactSelectOptionType } from '@/types/react-select';

export interface FormSettingFieldProps {
    isRequired?: boolean;
    label: string;
    namePath: (string | number)[];
    onChange?: ChangeEventHandler<HTMLInputElement>;
    placeholder?: string;
    settings?: KeyValueGeneric;
}

export interface IQuestionSettings<TSetting> {
    settings: TSetting;
}

/**
 * Question Type Types
 */
export interface IStextSettings extends FormItemSettings, InputStyleSettings {
    form: Omit<FormSettings['form'], 'submitButton'>;
}

export interface IFirstNameSettings extends FormItemSettings {
    input: Omit<InputStyleSettings['input'], 'type'>;
    form: Omit<FormSettings['form'], 'submitButton'>;
}

export interface ILastNameSettings extends FormItemSettings {
    input: Omit<InputStyleSettings['input'], 'type'>;
    form: Omit<FormSettings['form'], 'submitButton'>;
}

export interface IEmailSettings extends FormItemSettings {
    input: Omit<InputStyleSettings['input'], 'type'>;
    form: Omit<FormSettings['form'], 'submitButton'>;
}

export interface IFreeFormTextSettings extends FreeFormTextSettings, FormItemSettings {
    form: Omit<FormSettings['form'], 'submitButton'>;
}

export interface IPhoneSettings extends FormItemSettings {
    input: Omit<InputStyleSettings['input'], 'type'>;
    form: Omit<FormSettings['form'], 'submitButton'>;
}

export interface IBestTimeSettings extends FormItemSettings {
    input: Omit<InputStyleSettings['input'], 'type'>;
    form: Omit<FormSettings['form'], 'submitButton'>;
}

export interface IEssaySettings extends FormItemSettings {
    input: Omit<InputStyleSettings['input'], 'type'>;
    form: Omit<FormSettings['form'], 'submitButton'>;
}

export interface INumberSettings extends FormItemSettings {
    input: Omit<InputStyleSettings['input'], 'type'>;
    form: Omit<FormSettings['form'], 'submitButton'>;
}

export interface IAnyOfSettings extends FormItemSettings, CheckboxStyleSettings {
    form: Omit<FormSettings['form'], 'submitButton'>;
}

export interface IOneOfSettings extends FormItemSettings, RadioStyleSettings {
    form: Omit<FormSettings['form'], 'submitButton'>;
}

export interface IOneOfSiteSettings extends FormItemSettings, RadioStyleSettings {
    form: Omit<FormSettings['form'], 'submitButton'>;
}

export interface IDobSettings extends FormItemSettings {
    input: Omit<InputStyleSettings['input'], 'type'>;
    form: Omit<FormSettings['form'], 'submitButton'>;
}

export interface IAcceptTermsSettings extends AcceptTermsSettings, FormItemSettings, RadioStyleSettings {
    form: Omit<FormSettings['form'], 'submitButton'>;
}

export interface IPageBreakSettings {
    pageBreak: Omit<MultiScreenSettings['pageBreak'], 'nextButton'>;
}

export interface IStartPagingSettings {
    pageBreak: Omit<MultiScreenSettings['pageBreak'], 'nextButton'>;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface IDividerSettings {}

export interface INextButtonSettings {
    pageBreak: Pick<MultiScreenSettings['pageBreak'], 'nextButton'>;
}

export interface ISubmitButtonSettings {
    form: Pick<FormSettings['form'], 'submitButton'>;
}

export interface ICalcBmiSettings extends FormItemSettings, CalcBmiSettings {
    input: Omit<InputStyleSettings['input'], 'type'>;
    form: Omit<FormSettings['form'], 'submitButton'>;
}

/**
 * Style Group  Types
 */

export interface AcceptTermsSettings {
    userAgreement: {
        content: {
            style: FontSettings;
        };
        optionsType: ReactSelectOptionType;
        container: {
            style: BackgroundSettings &
                BorderSettings &
                HeightSettings &
                MarginSettings &
                PaddingSettings &
                WidthSettings;
        };
        radioPosition: 'top' | 'bottom';
        terms: KeyValueGeneric;
    };
}

export interface FreeFormTextSettings {
    freeFormText: {
        content: {
            style: FontSettings;
        };
        container: {
            style: BackgroundSettings &
                BorderSettings &
                HeightSettings &
                MarginSettings &
                PaddingSettings &
                WidthSettings;
        };
        text: KeyValueGeneric;
    };
}

export interface ButtonSettings {
    btnLabel: {
        style: FontSettings;
        text: string | null;
    };
    button: {
        style: BackgroundSettings & BorderSettings & MarginSettings & PaddingSettings;
    };
    buttonProps: ButtonPropsSettings;
    icon: ReactSelectOptionType | null;
}

export type CalcBmiUnit = Record<LanguageCode, 'us' | 'metric'>;

export interface CalcBmiSettings {
    bmi: {
        unit: 'us' | 'metric';
        unitOptions: CalcBmiUnit | EmptyObject;
        heightLabel: {
            style: FontSettings;
            label: string | null;
        };
        heightPosition: 'left' | 'right';
        weightLabel: {
            style: FontSettings;

            label: string | null;
        };
    };
}

export interface CheckboxStyleSettings {
    checkbox: {
        checked: {
            style: BackgroundSettings & BorderSettings;
        };
        container: {
            style: DisplayFlexSettings;
        };
        input: {
            style: BackgroundSettings & BorderSettings & HeightSettings & WidthSettings;
        };
        label: {
            style: FontSettings;
        };
        optionsType: ReactSelectOptionType;
    };
}

export interface DobSettings {
    dateInputs: 'showFullDate' | 'showYearOnly' | 'showMonthDayDate' | 'showMonthYearDate';
    minAge: number;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface GlobalSettings
    extends CheckboxStyleSettings,
        FormSettings,
        FormItemSettings,
        InputStyleSettings,
        MultiScreenSettings,
        RadioStyleSettings {
    general: {
        customFont: {
            link: null;
        };
        style: FontSettings & {
            'font-family': string | null;
        };
    };
}

export interface FormSettings {
    form: {
        label: {
            style: FontSettings;
        };
        submitButton: ButtonSettings;
    };
}

export interface FormItemSettings {
    formItem: {
        container: {
            style: BorderSettings & HeightSettings & MarginSettings & PaddingSettings & WidthSettings;
        };
        control: {
            style: BorderSettings & HeightSettings & MarginSettings & PaddingSettings & WidthSettings;
        };
        label: {
            style: BorderSettings & HeightSettings & MarginSettings & PaddingSettings & WidthSettings;
        };
        row: {
            style: BorderSettings & HeightSettings & MarginSettings & PaddingSettings & WidthSettings;
        };
    };
}

export interface InputStyleSettings {
    input: {
        addon: {
            style: BackgroundSettings & BorderSettings & BorderRadiusSettings & FontSettings;
        };
        container: {
            style: HeightSettings & MarginSettings & PaddingSettings & WidthSettings;
        };
        control: {
            style: BackgroundSettings & BorderSettings & BorderRadiusSettings & FontSettings & PaddingSettings;
        };
        type: ReactSelectOptionType | null;
    };
}

export interface MultiScreenSettings {
    pageBreak: {
        container: {
            style: BackgroundSettings & BorderSettings & MarginSettings & PaddingSettings & WidthSettings;
        };
        header: string | null;
        headerContainer: {
            style: BorderSettings & MarginSettings & PaddingSettings & WidthSettings;
        };
        nextButton: ButtonSettings;
        paging: {
            style: FontSettings;
            title: string | null;
        };
        showPaging: ReactSelectOptionType;
        title: {
            style: FontSettings;
        };
    };
}

export interface RadioStyleSettings {
    radio: {
        button: {
            style: BackgroundSettings & BorderSettings & HeightSettings & WidthSettings;
        };
        checked: {
            style: BackgroundSettings & BorderSettings;
        };
        container: {
            style: DisplayFlexSettings;
        };
        label: {
            style: FontSettings;
        };
        optionsType: ReactSelectOptionType;
    };
}

/**
 * Style Types
 */

export interface BackgroundSettings {
    'background-color': string | null;
}

export interface BorderSettings {
    border: string | null;
    'border-color': string | null;
    borderStyle: string | null;
}

export interface BorderRadiusSettings {
    borderRadius: string | null;
}

export interface DisplayFlexSettings {
    displayFlex: ReactSelectOptionType | null;
}

export interface ButtonPropsSettings {
    buttonType: ReactSelectOptionType | null;
    blockType: ReactSelectOptionType | null;
    buttonShape: ReactSelectOptionType | null;
    size: ReactSelectOptionType | null;
    iconPosition: ReactSelectOptionType | null;
}

export interface FontSettings {
    color: string | null;
    'line-height': string | null;
    'font-size': string | null;
    fontWeight: string | null;
    textTransform: string | null;
}

export interface HeightSettings {
    height: string | null;
    'min-height': string | null;
    'max-height': string | null;
}

export interface MarginSettings {
    'margin-top': null;
    'margin-right': null;
    'margin-bottom': null;
    'margin-left': null;
}

export interface PaddingSettings {
    'padding-top': null;
    'padding-right': null;
    'padding-bottom': null;
    'padding-left': null;
}

export interface WidthSettings {
    width: string | null;
    'min-width': string | null;
    'max-width': string | null;
}
