# Partially adapted from bkuhl/fpm-nginx:8.3.0
FROM php:8.4.4-fpm-alpine3.20

# <<< NEW: Define Arguments for Host User/Group ID >>>
ARG HOST_UID=1000
ARG HOST_GID=1000

WORKDIR /var/www/html

# Install basic tools first
RUN apk add --no-cache shadow nginx curl tar git jq # shadow provides usermod/groupmod capabilities

# <<< NEW: Modify www-data user and group >>>
# Be careful if the original www-data owns critical system files installed by base image,
# but usually safe for common web stacks.
# Delete the original www-data user and group if they exist
RUN if getent passwd www-data ; then deluser www-data ; fi && \
    if getent group www-data ; then delgroup www-data ; fi && \
# Create new group with HOST_GID
    addgroup -g ${HOST_GID} www-data && \
# Create new user with HOST_UID, HOST_GID, assign to the new group, set home dir, no password, non-login shell
    adduser -u ${HOST_UID} -G www-data -h /home/<USER>/sbin/nologin -D www-data && \
# Create home directory if it doesn't exist and set ownership
    mkdir -p /home/<USER>
    chown ${HOST_UID}:${HOST_GID} /home/<USER>

COPY .docker/conf/install_composer.sh /var/www/html/install_composer.sh

# RUN apk add nginx # Already added above
COPY .docker/conf/nginx.conf /etc/nginx/nginx.conf

COPY .docker/conf/run_php_fpm /etc/services.d/php-fpm/run_php_fpm
RUN chmod +x /etc/services.d/php-fpm/run_php_fpm

# Adding the opcache configuration
ADD .docker/conf/opcache.ini /usr/local/etc/php/opcache_disabled.ini
ADD .docker/conf/healthcheck.ini /usr/local/etc/php/healthcheck.ini

RUN rm -rf /var/cache/apk/* && \
        rm -rf /tmp/*

# ------------------------ Common PHP Dependencies ------------------------
RUN apk update && apk add \
        # see https://github.com/docker-library/php/issues/880
        oniguruma-dev \
        # needed for gd
        libpng-dev libjpeg-turbo-dev \
		# needed for xdebug
		$PHPIZE_DEPS \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* \
    # Installing composer
    && sh /var/www/html/install_composer.sh \
    # Installing common Laravel dependencies
    && docker-php-ext-install mbstring pdo_mysql gd \
        opcache \
    && mkdir -p /home/<USER>/.composer/cache \
    && chown -R www-data:www-data /home/<USER>/ /var/www/html \
    && rm /var/www/html/install_composer.sh


# Install OpenSSH and set the password for root to "Docker!". In this example, "apk add" is the install instruction for an Alpine Linux-based image.
RUN apk add --no-cache openssh \
    git \
    nano \
    libintl \
    icu \
    icu-dev \
    libzip-dev \
    libpng \
    libpng-dev \
    zlib-dev \
    bash \
    zsh git vim zsh-autosuggestions zsh-syntax-highlighting bind-tools curl \
    supervisor \
    htop \
    mysql-client \
    mariadb-connector-c \
    procps

# Redis
RUN apk --no-cache add pcre-dev ${PHPIZE_DEPS} \
    && pecl install redis \
    && docker-php-ext-enable redis \
    && apk del pcre-dev ${PHPIZE_DEPS} \
    && rm -rf /tmp/pear

# GMP
RUN apk add --no-cache gmp-dev
RUN ln -s /usr/include/gmp.h /usr/local/include/
RUN docker-php-ext-configure gmp
RUN docker-php-ext-install gmp

## TODO: Nakisha - Figure out how to specify the version we want to install
# Node.js
RUN apk update && apk add --update nodejs npm

# SSH setup
RUN ssh-keygen -A \
    && mkdir /root/.ssh \
    && chmod 0700 /root/.ssh \
    && echo "root:Docker!" | chpasswd

# Install PHP extensions
RUN docker-php-ext-configure intl
RUN docker-php-ext-install exif \
    pcntl \
    bcmath \
    intl \
    zip

# ------------------------ xdebug ------------------------
ARG REMOTE_XDEBUG_HOST=host.docker.internal
ARG REMOTE_XDEBUG_PORT=9003

RUN apk add --update linux-headers
RUN apk update && apk add ${PHPIZE_DEPS}
RUN pecl install xdebug
RUN docker-php-ext-enable xdebug
RUN echo "xdebug.mode=debug,coverage" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
RUN echo "xdebug.start_with_request=yes" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
RUN echo "xdebug.client_host=${REMOTE_XDEBUG_HOST}" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
RUN echo "xdebug.client_port=${REMOTE_XDEBUG_PORT}" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
# RUN echo "xdebug.remote_handler=dbgp" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
RUN echo "xdebug.discover_client_host=true" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
RUN echo "xdebug.idekey=x_debug" >> /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini

RUN docker-php-ext-install sockets

# Copy configs
COPY ./.nginx/sshd_config /etc/ssh/
COPY ./.nginx/zzz-1ndata-php-fpm.conf /usr/local/etc/php-fpm.d/
COPY ./.nginx/php.ini /usr/local/etc/php/conf.d/
COPY ./.nginx/99-custom-error-reporting-ci-tests.ini /usr/local/etc/php/conf.d/

WORKDIR /var/www/html

# Copy the application files to the container
# <<< MODIFIED: Use ARGs for chown >>>
# Ensure this ADD happens *after* www-data user is modified
ADD --chown=${HOST_UID}:${HOST_GID} . /var/www/html

ADD ./.nginx/conf.d/default.conf /etc/nginx/conf.d/default.conf

USER root

# Start Cron Setup
# Make the 1min dir thats not there by default
RUN mkdir /etc/periodic/1min
# Add the 1min option to crontab
## RUN echo '*       *       *       *       *       run-parts /etc/periodic/1min' >> /etc/crontabs/www-data
## RUN echo '*       *       *       *       *       run-parts /etc/periodic/1min' >> /etc/crontabs/root # Run system crons as root
# If you need user-specific crons for www-data (now UID 1000)
RUN echo '*       *       *       *       *       su www-data -s /bin/sh -c "run-parts /etc/periodic/1min"' >> /etc/crontabs/root
# Add artisan cron
ADD ./.crons/artisan-cron.txt /etc/periodic/1min/artisan-cron
RUN chmod a+x /etc/periodic/1min/artisan-cron;
# End Cron Setup

# Setup Supervisor
ADD ./.supervisor/supervisord-dev.conf /etc/supervisord-dev.conf
# Ensure supervisor logs/sockets are writable by root or configured user
RUN mkdir -p /var/log/supervisor && touch /var/run/supervisord.pid /run/supervisord.sock
RUN chmod -R 777 /var/log/supervisor /var/run/supervisord.pid /run/supervisord.sock # Looser permissions for simplicity, adjust if needed

EXPOSE 80

CMD ["supervisord", "--nodaemon", "--configuration", "/etc/supervisord-dev.conf"]
