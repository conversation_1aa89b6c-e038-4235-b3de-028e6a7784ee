APP_NAME="1nHealth"
APP_ENV=testing
APP_SERVER=usEast
APP_KEY=base64:U/k1AiLIWsxR+epEZ6d4t54CMCzA//tEy/M8J9DdRhs=
ELOQUENT_KEY=base64:4ktpIwxehZpiBCFbj59GyEU+4xAM379JdzXXyycYlSw=
APP_DEBUG=true
APP_URL=http://app.test
CENTRAL_DOMAINS=app.test

DUSK_DRIVER_URL=http://core_selenium:4444/wd/hub

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=test_1nd_core_app_db
DB_PORT=3306
DB_DATABASE=test_1nd_core_app
DB_USERNAME=root
DB_PASSWORD=
DB_TABLE_PREFIX=
DB_SERVER_VERSION=8.0

DB_UK_HOST=test_1nd_core_app_uk_db
DB_UK_DATABASE="${DB_DATABASE}"
DB_UK_USERNAME=root
DB_UK_PASSWORD=

DB_EU_HOST=test_1nd_core_app_eu_db
DB_EU_DATABASE="${DB_DATABASE}"
DB_EU_USERNAME=root
DB_EU_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
TELESCOPE_ENABLED=false
PULSE_ENABLED=false

MAIL_MAILER=array
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="1nHealth Clinical Studies"

SESSION_DOMAIN=.app.test
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_CONNECTION=mysql

REDIS_CLIENT=phpredis
REDIS_HOST=test_1nd_core_app_redis
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=2
REDIS_CACHE_DB=3
REDIS_PERSISTENT=true

FILESYSTEM_DISK=local

TWILIO_ACCOUNT_SID=Twilio-Account-Sid
TWILIO_AUTH_TOKEN=Twilio-Auth-Token
TWILIO_WEBHOOK_URL="${APP_URL}"

POSTMARK_SANDBOX_TOKEN=""

PASSPORT_PRIVATE_KEY="*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

PASSPORT_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEApvOn+6Nyi79L/JY4RKol
ybmgQpNPIuQlqRciPNL+O+iIhM39dSKfRDTXnKu68yFLomjfmKvF+CEGzLjpNVcw
jZ2RlvbqqbJK5Y7cgtx3sCAmv1uRHLhFOWqXVh65cdXgEB1P2l7sktRMFMt/UFSf
mFMBAI/RisTwbGb3Sbxd6Ygyba9XsobqxVnHr9iJxe9UIc9z5vyTQ1wtMl74PvlE
lQbRUktZezHlD8auEwHBrzDAQksItoaPeb4wyWg9/VhfDxTu6fIkCBinej0aKhww
z9yZvrcjxBAIVi1TYTciWrT26OWWXOHngZNOfFQNVAUT02Is4bvBqnLbnrmrAgl4
UboLznve4QzsyTGANTAAeeAt/Jqu8qfbnqWELCYC+7+VAs8WThZZmlvsuYkNbbMW
qR8WZrFTLpxshzkZ0AdH3oywwS7V/ueuW4TY/gOwpt7MNWcwamH7br+ua0YLf6RG
LhCnsW79RpVln6h3RlMujK2MACMnFZuPwFM2sw9Ji3q2fYA389XYYOWJ3P24E3rV
ovk6WMP6GGHP8WAlSLrwDor4K1H5ICbqUyTG+0OZcpYtUG8K+dp/r3Wi8WTop1im
7hHk5886w4QFRjxQOIvCTyi6v9T/kgTJSZbIAhYLlU9M1yl1hp7lOmzqWS94UJYH
W0JSHWhdHsJMgpIZYHx88gECAwEAAQ==
-----END PUBLIC KEY-----"
