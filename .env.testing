APP_NAME="1nHealth"
APP_ENV=testing
APP_SERVER=usEast
APP_KEY=base64:U/k1AiLIWsxR+epEZ6d4t54CMCzA//tEy/M8J9DdRhs=
ELOQUENT_KEY=base64:4ktpIwxehZpiBCFbj59GyEU+4xAM379JdzXXyycYlSw=
APP_DEBUG=true
#APP_URL=http://app.test
#CENTRAL_DOMAINS=app.test
APP_URL=http://app.localhost:9091
CENTRAL_DOMAINS=app.localhost
US_APP_DOMAIN=1nh.app.localhost
UK_APP_DOMAIN=1nh-uk.app.localhost
EU_APP_DOMAIN=1nh-eu.app.localhost
US_APP_URL=http://1nh.app.localhost:9091
UK_APP_URL=http://1nh-uk.app.localhost:9091
EU_APP_URL=http://1nh-eu.app.localhost:9091

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=test_1nd_core_app_db
DB_PORT=3306
DB_DATABASE=test_1nd_core_app
DB_USERNAME=root
DB_PASSWORD=
DB_TABLE_PREFIX=
DB_SERVER_VERSION=8.0

DB_UK_HOST=test_1nd_core_app_uk_db
DB_UK_DATABASE="${DB_DATABASE}"
DB_UK_USERNAME=root
DB_UK_PASSWORD=

DB_EU_HOST=test_1nd_core_app_eu_db
DB_EU_DATABASE="${DB_DATABASE}"
DB_EU_USERNAME=root
DB_EU_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
TELESCOPE_ENABLED=false

MAIL_MAILER=array
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="1nHealth Clinical Studies"

PUSHER_APP_ID=*******
PUSHER_APP_KEY=f9e8bc55b0fdc1c1696f
PUSHER_APP_SECRET=2e4b72650b50126fa3d8
PUSHER_APP_CLUSTER=us2
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

FORTIFY_LIMITERS_LOGIN=

SESSION_DOMAIN=.app.localhost
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_CONNECTION=mysql

REDIS_CLIENT=phpredis
REDIS_HOST=test_1nd_core_app_redis
REDIS_PASSWORD=null
REDIS_PORT=6379
REDIS_DB=2
REDIS_CACHE_DB=3
REDIS_PERSISTENT=true

FILESYSTEM_DISK=local

TWILIO_ACCOUNT_SID=Twilio-Account-Sid
TWILIO_AUTH_TOKEN=Twilio-Auth-Token
TWILIO_WEBHOOK_URL="${APP_URL}"

POSTMARK_SANDBOX_TOKEN=""

PASSPORT_PRIVATE_KEY="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"
PASSPORT_PUBLIC_KEY="LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQ0lqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FnOEFNSUlDQ2dLQ0FnRUFwdk9uKzZOeWk3OUwvSlk0UktvbAp5Ym1nUXBOUEl1UWxxUmNpUE5MK08raUloTTM5ZFNLZlJEVFhuS3U2OHlGTG9tamZtS3ZGK0NFR3pManBOVmN3CmpaMlJsdmJxcWJKSzVZN2NndHgzc0NBbXYxdVJITGhGT1dxWFZoNjVjZFhnRUIxUDJsN3NrdFJNRk10L1VGU2YKbUZNQkFJL1Jpc1R3YkdiM1NieGQ2WWd5YmE5WHNvYnF4Vm5IcjlpSnhlOVVJYzl6NXZ5VFExd3RNbDc0UHZsRQpsUWJSVWt0WmV6SGxEOGF1RXdIQnJ6REFRa3NJdG9hUGViNHd5V2c5L1ZoZkR4VHU2ZklrQ0JpbmVqMGFLaHd3Cno5eVp2cmNqeEJBSVZpMVRZVGNpV3JUMjZPV1dYT0huZ1pOT2ZGUU5WQVVUMDJJczRidkJxbkxibnJtckFnbDQKVWJvTHpudmU0UXpzeVRHQU5UQUFlZUF0L0pxdThxZmJucVdFTENZQys3K1ZBczhXVGhaWm1sdnN1WWtOYmJNVwpxUjhXWnJGVExweHNoemtaMEFkSDNveXd3UzdWL3VldVc0VFkvZ093cHQ3TU5XY3dhbUg3YnIrdWEwWUxmNlJHCkxoQ25zVzc5UnBWbG42aDNSbE11aksyTUFDTW5GWnVQd0ZNMnN3OUppM3EyZllBMzg5WFlZT1dKM1AyNEUzclYKb3ZrNldNUDZHR0hQOFdBbFNMcndEb3I0SzFINUlDYnFVeVRHKzBPWmNwWXRVRzhLK2RwL3IzV2k4V1RvcDFpbQo3aEhrNTg4Nnc0UUZSanhRT0l2Q1R5aTZ2OVQva2dUSlNaYklBaFlMbFU5TTF5bDFocDdsT216cVdTOTRVSllIClcwSlNIV2hkSHNKTWdwSVpZSHg4OGdFQ0F3RUFBUT09Ci0tLS0tRU5EIFBVQkxJQyBLRVktLS0tLQ=="
PASSPORT_PERSONAL_ACCESS_CLIENT_ID=9d9a2ab2-d5fe-4147-881a-945c0ba03853
PASSPORT_PERSONAL_ACCESS_CLIENT_SECRET=uVXWZPJjbOMnUjn7GdRM9Z1a1owNMAdTHjpII27h
