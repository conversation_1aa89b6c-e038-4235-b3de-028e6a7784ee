/* eslint-disable import/order */
import laravel from 'laravel-vite-plugin';
import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import macrosPlugin from 'vite-plugin-babel-macros';
import checker from 'vite-plugin-checker';
import eslint from 'vite-plugin-eslint';

import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react';

export default defineConfig(({ mode }) => {
    // Load env file based on `mode` in the current working directory.
    // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
    const env = loadEnv(mode, process.cwd(), '');

    const plugins = [
        laravel({
            input: ['resources/js/app.tsx'],
            /*  build: {
                manifest: true,
            }, */
            refresh: true,
        }),
        react(),
        checker({
            typescript: true,
        }),
        macrosPlugin(),
        tailwindcss(),
    ];

    // ---- CONDITIONAL ESLINT ----
    // Only add the eslint plugin if APP_ENV is *not* 'testing'
    // This means it will run locally (where APP_ENV is usually 'local' or undefined)
    // but skip during your CI run where APP_ENV=testing
    if (!env.APP_ENV || env.APP_ENV !== 'playwright') {
        console.log(`[Vite Config] APP_ENV is '${env.APP_ENV}', enabling eslint plugin.`);
        plugins.push(eslint());
    } else {
        console.log(`[Vite Config] APP_ENV is 'playwright', skipping eslint plugin for build.`);
    }
    // ---- END CONDITIONAL ESLINT ----

    return {
        plugins,
        server: { port: env.VITE_PORT },
        build: {
            outDir: 'public/build', // Match Laravel plugin's directory
            emptyOutDir: true,
            manifest: 'manifest.json', // Explicit manifest name
            rollupOptions: {
                output:
                    mode === 'production'
                        ? {
                              entryFileNames: 'entry/[name].[hash].js',
                              chunkFileNames: 'chunks/[hash:21].js',
                              assetFileNames: 'assets/[hash:21].[ext]',
                          }
                        : {
                              entryFileNames: 'entry/[name].js',
                              chunkFileNames: 'chunks/[name].js',
                              assetFileNames: 'assets/[name].[ext]',
                          },
                maxParallelFileOps: 1000,
            },
        },
        resolve: {
            alias: {
                'ziggy-js': path.resolve('vendor/tightenco/ziggy'),
            },
        },
    };
});
