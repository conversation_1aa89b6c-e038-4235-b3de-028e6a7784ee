# 🪄 This is your project's Sourcery configuration file.

# You can use it to get <PERSON>ry working in the way you want, such as
# ignoring specific refactorings, skipping directories in your project,
# or writing custom rules.

# 📚 For a complete reference to this file, see the documentation at
# https://docs.sourcery.ai/Configuration/Project-Settings/

# This file was auto-generated by Sourcery on 2025-02-07 at 20:00.

version: '1' # The schema.ts version of this config file

ignore: # A list of paths or files which Sourcery will ignore.
  - .git
  - env
  - .env
  - .tox
  - node_modules
  - vendor
  - venv
  - .venv
  - ~/.pyenv
  - ~/.rye
  - ~/.vscode
  - .vscode
  - ~/.cache
  - ~/.config
  - ~/.local

rule_settings:
  enable:
    - default
  disable: [ ] # A list of rule IDs Sourcery will never suggest.
  rule_types:
    - refactoring
    - suggestion
    - comment
  python_version: '3.9' # A string specifying the lowest Python version your project supports. Sourcery will not suggest refactorings requiring a higher Python version.

# rules:  # A list of custom rules Sourcery will include in its analysis.
# - id: no-print-statements
#   description: Do not use print statements in the test directory.
#   pattern: print(...)
#   language: python
#   replacement:
#   condition:
#   explanation:
#   paths:
#     include:
#     - test
#     exclude:
#     - conftest.py
#   tests: []
#   tags: []

# rule_tags: {} # Additional rule tags.

# metrics:
#   quality_threshold: 25.0

# github:
#   labels: []
#   ignore_labels:
#   - sourcery-ignore
#   request_review: author
#   sourcery_branch: sourcery/{base_branch}

# clone_detection:
#   min_lines: 3
#   min_duplicates: 2
#   identical_clones_only: false

# proxy:
#   url:
#   ssl_certs_file:
#   no_ssl_verify: false

coding_assistant:
  #   project_description: ''
  enabled: true
#   recipe_prompts: {}
