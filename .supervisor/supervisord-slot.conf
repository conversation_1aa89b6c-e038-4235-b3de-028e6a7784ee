# contents copied from default supervisord.conf file mixed with laravel setup docs

[unix_http_server]
file=/run/supervisord.sock  ; the path to the socket file

[supervisord]
nodaemon=true
logfile=/var/log/supervisord.log ; main log file; default $CWD/supervisord.log

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///run/supervisord.sock ; use a unix:// URL for a unix socket

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
user=root

[program:php-fpm]
command=/bin/sh /etc/services.d/php-fpm/run_php_fpm
autostart=true
autorestart=true
user=root

[program:sshd]
command=/usr/sbin/sshd -D -e
autostart=true
autorestart=true
user=root

[include]
files = /etc/supervisor.d/*.ini
