# contents copied from default supervisord.conf file mixed with laravel setup docs

[unix_http_server]
file=/run/supervisord.sock  ; the path to the socket file

[supervisord]
nodaemon=true
logfile=/var/log/supervisord.log ; main log file; default $CWD/supervisord.log

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///run/supervisord.sock ; use a unix:// URL for a unix socket

[program:horizon]
process_name=%(program_name)s
command=php /var/www/html/artisan horizon
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/horizon.log
stopwaitsecs=3600

[program:nightwatch]
process_name=%(program_name)s
command=php /var/www/html/artisan nightwatch:agent
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/nightwatch.log
stopwaitsecs=3600

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
user=root

[program:php-fpm]
command=/bin/sh /etc/services.d/php-fpm/run_php_fpm
autostart=true
autorestart=true
user=root

[program:sshd]
command=/usr/sbin/sshd -D -e
autostart=true
autorestart=true
user=root

[program:cron]
command=/usr/sbin/crond -f -l 8
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stdout_events_enabled=true
stderr_events_enabled=true

[include]
files = /etc/supervisor.d/*.ini
