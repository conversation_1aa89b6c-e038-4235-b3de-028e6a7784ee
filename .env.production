APP_NAME="1nHealth"
APP_ENV=production
APP_SERVER=${AZ_APP_SERVER}
APP_KEY="${AZ_APP_KEY}"
ELOQUENT_KEY="${AZ_ELOQUENT_KEY}"
APP_DEBUG=false
APP_URL="${AZ_APP_URL}"
APP_SLOT="${AZ_APP_SLOT}"
CENTRAL_DOMAINS="${AZ_CENTRAL_DOMAINS}"
DOC_PORTAL_KB_URL="${AZ_DOC_PORTAL_KB_URL}"

DOC_PORTAL_ADMIN_DOMAIN="${AZ_DOC_PORTAL_ADMIN_DOMAIN}"
DOC_PORTAL_INTERNAL_TENANT=1

LOG_CHANNEL=stack
LOG_STACK=single,nightwatch
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST="${AZ_DB_HOST}"
DB_PORT=3306
DB_DATABASE="${AZ_DB_DATABASE}"
DB_USERNAME="${AZ_DB_USERNAME}"
DB_PASSWORD="${AZ_DB_PASSWORD}"
DB_TABLE_PREFIX=
DB_SERVER_VERSION=8.0

DB_UK_HOST="${AZ_DB_UK_HOST}"
DB_UK_DATABASE="${AZ_DB_UK_DATABASE}"
DB_UK_USERNAME="${AZ_DB_UK_USERNAME}"
DB_UK_PASSWORD="${AZ_DB_UK_PASSWORD}"

DB_EU_HOST="${AZ_DB_EU_HOST}"
DB_EU_DATABASE="${AZ_DB_EU_DATABASE}"
DB_EU_USERNAME="${AZ_DB_EU_USERNAME}"
DB_EU_PASSWORD="${AZ_DB_EU_PASSWORD}"

PULSE_DOMAIN="${AZ_PULSE_DOMAINS}"
PULSE_DB_CONNECTION="${AZ_PULSE_DB_CONNECTION}"
PULSE_SERVER_NAME="${AZ_PULSE_SERVER_NAME}"

QUEUE_MONITOR_DB_CONNECTION="${AZ_QUEUE_MONITOR_DB_CONNECTION}"

DB_DEMO_HOST=
DB_DEMO_DATABASE=
DB_DEMO_USERNAME=
DB_DEMO_PASSWORD=
DB_DEMO_TABLE_PREFIX=

BROADCAST_DRIVER=pusher

CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_CONNECTION=mysql
SESSION_DOMAIN="${AZ_SESSION_DOMAIN}"

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST="${AZ_REDIS_HOST}"
REDIS_PASSWORD="${AZ_REDIS_PASSWORD}"
REDIS_PORT="${AZ_REDIS_PORT}"
REDIS_DB="${AZ_REDIS_DB}"
REDIS_CACHE_DB="${AZ_REDIS_CACHE_DB}"
REDIS_PERSISTENT=false
REDIS_SCHEME="${AZ_REDIS_SCHEME}"

MAIL_MAILER=postmark
POSTMARK_TOKEN="${AZ_POSTMARK_TOKEN}"
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="1nHealth Clinical Studies"

PUSHER_APP_ID=1421889
PUSHER_APP_KEY="${AZ_PUSHER_APP_KEY}"
PUSHER_APP_SECRET="${AZ_PUSHER_APP_SECRET}"
PUSHER_APP_CLUSTER=us2
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

TELESCOPE_DOMAIN="${AZ_TELESCOPE_DOMAIN}"
TELESCOPE_PATH=dev
TELESCOPE_DB_CONNECTION="${AZ_TELESCOPE_DB_CONNECTION}"

NIGHTWATCH_TOKEN="${AZ_NIGHTWATCH_TOKEN}"

FILESYSTEM_DISK=azure
AZURE_STORAGE_NAME=1ndataproductionstorage
AZURE_STORAGE_KEY="${AZ_AZURE_STORAGE_KEY}"
AZURE_STORAGE_CONTAINER=storage
AZURE_STORAGE_EDITOR_CONTAINER=editor-prod
AZURE_STORAGE_DOC_PORTAL_CONTAINER=document-portal-prod

HORIZON_PATH=1nh-queues
HORIZON_DOMAIN="${AZ_HORIZON_DOMAIN}"

SENTRY_LARAVEL_DSN="${AZ_SENTRY_LARAVEL_DSN}"
SENTRY_ENVIRONMENT=production

TWILIO_WEBHOOK_URL="${AZ_APP_URL}"

FACEBOOK_PIXEL_ID="${AZ_FACEBOOK_PIXEL_ID}"
FACEBOOK_ACCESS_TOKEN="${AZ_FACEBOOK_ACCESS_TOKEN}"

SCHEDULE_RESTRICTED_ACCESS=true

VITE_GOOGLE_MAPS_API_KEY="${GOOGLE_MAPS_API_KEY}"
VITE_AG_GRID_LICENSE="${AG_GRID_LICENSE}"
GOOGLE_TIMEZONE_API_KEY="${AZ_GOOGLE_TIMEZONE_API_KEY}"

ALGOLIA_ID=
ALGOLIA_SEARCH_KEY=

TORCHLIGHT_TOKEN="root:${AZ_TORCHLIGHT_TOKEN}"
TORCHLIGHT_LIGHT_THEME=github-light
TORCHLIGHT_DARK_THEME=github-dark

DEFAULT_DOC_VERSION=4.x

IPDATA_API_KEY=${AZ_IPDATA_API_KEY}

PASSPORT_PRIVATE_KEY="${AZ_PASSPORT_PRIVATE_KEY}"
PASSPORT_PUBLIC_KEY="${AZ_PASSPORT_PUBLIC_KEY}"
PASSPORT_PERSONAL_ACCESS_CLIENT_ID="${AZ_PASSPORT_PERSONAL_ACCESS_CLIENT_ID}"
PASSPORT_PERSONAL_ACCESS_CLIENT_SECRET="${AZ_PASSPORT_PERSONAL_ACCESS_CLIENT_SECRET}"

FULLSTORY_ENABLED=true
