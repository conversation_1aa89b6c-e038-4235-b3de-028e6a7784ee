{
    "root": true,
    "env": {
        "browser": true,
        "es2021": true,
        "node": true,
        "jest/globals": true
    },
    "extends": [
        "airbnb",
        "airbnb/hooks",
        "plugin:@typescript-eslint/recommended",
        "plugin:react/recommended",
        "plugin:react-hooks/recommended",
        "plugin:import/errors",
        "plugin:import/warnings",
        "plugin:import/typescript",
        "prettier"
    ],
    "parser": "@typescript-eslint/parser",
    "parserOptions": {
        "ecmaFeatures": {
            "jsx": true
        },
        "ecmaVersion": 12,
        "sourceType": "module"
    },
    "plugins": ["react", "react-hooks", "@typescript-eslint", "import", "simple-import-sort", "jest"],
    "rules": {
        "react/react-in-jsx-scope": "off",
        "react/jsx-filename-extension": "off",
        "react/prop-types": "off",
        "react-hooks/exhaustive-deps": "off",
        "react/function-component-definition": [
            "error",
            {
                "namedComponents": ["function-declaration", "arrow-function"],
                "unnamedComponents": "arrow-function"
            }
        ],
        "no-nested-ternary": "off",
        "no-useless-constructor": "off",
        "import/no-unresolved": "error",
        "@typescript-eslint/ban-ts-ignore": "off",
        "@typescript-eslint/ban-ts-nocheck": "off",
        "@typescript-eslint/ban-ts-comment": "off",
        "@typescript-eslint/explicit-module-boundary-types": "off",
        "@typescript-eslint/no-empty-function": "off",
        "@typescript-eslint/no-unused-vars": "error",
        "simple-import-sort/imports": [
            "error",
            {
                "groups": [
                    ["^react"],
                    ["^[a-zA-z0-9]"],
                    ["^@?\\w"],
                    ["^@appType"],
                    ["^\\u0000"],
                    ["^\\.\\.(?!/?$)"],
                    ["^\\.\\./?$"],
                    ["^\\./(?=.*/)(?!/?$)"],
                    ["^\\.(?!/?$)"],
                    ["^\\./?$"]
                ]
            }
        ],
        "@typescript-eslint/explicit-function-return-type": [
            "off",
            {
                "allowExpressions": true,
                "allowTypedFunctionExpressions": true
            }
        ],
        "simple-import-sort/exports": "error",
        "import/order": [
            "error",
            {
                "groups": ["builtin", "external"]
            }
        ],
        "import/no-extraneous-dependencies": [
            "error",
            {
                "devDependencies": ["**/*.test.tsx", "**/*.spec.tsx", "**/*.test.ts", "**/*.spec.ts"]
            }
        ],
        "import/extensions": [
            "error",
            "ignorePackages",
            {
                "": "never",
                "js": "never",
                "jsx": "never",
                "ts": "never",
                "tsx": "never"
            }
        ],
        "@typescript-eslint/explicit-module-boundary-type": "off",
        "no-undef": "off",
        "no-use-before-define": "off",
        "react/jsx-props-no-spreading": "off",
        "import/no-cycle": "off",
        "import/prefer-default-export": "off",
        "no-param-reassign": [
            "error",
            {
                "props": true,
                "ignorePropertyModificationsFor": ["state", "accum", "acc"]
            }
        ],
        "react/no-danger": "off",
        "jsx-a11y/label-has-associated-control": "off",
        "camelcase": [
            "warn",
            {
                "ignoreDestructuring": true,
                "properties": "never",
                "allow": [
                    "campaign_form",
                    "campaign_form_uuids",
                    "is_allow_children",
                    "is_allow_options",
                    "is_allow_sites",
                    "is_predefined",
                    "question_label",
                    "question_uuid",
                    "study_uuids"
                ]
            }
        ],
        "react/jsx-no-useless-fragment": [
            "warn",
            {
                "allowExpressions": true
            }
        ],
        "react/require-default-props": "off",
        "react/no-unknown-property": [
            2,
            {
                "ignore": ["jsx", "global"]
            }
        ],
        "jsx-a11y/control-has-associated-label": "off"
    },
    "ignorePatterns": [
        "**/stories/**",
        "**/__tests__/**",
        "**/__mocks__/**",
        "**/__fixtures__/**",
        "**/node_modules/**",
        "**/vendor/**"
    ],
    "settings": {
        "react": {
            "version": "detect"
        },
        "import/extensions": [".js", ".jsx", ".ts", ".tsx", ".d.ts"],
        "import/parsers": {
            "@typescript-eslint/parser": [".ts", ".tsx"]
        },
        "import/resolver": {
            // use <root>/tsconfig.json
            "typescript": {},
            "node": {
                // "paths": ["resources/js"],
                "extensions": [".js", ".jsx", ".ts", ".tsx", ".d.ts"],
                "moduleDirectory": ["resources/js", "node_modules"]
            }
        }
    },
    "overrides": [
        {
            "files": ["**/*.js?(x)", "**/*.ts?(x)"],
            "rules": {
                "@typescript-eslint/explicit-module-boundary-types": "off",
                "@typescript-eslint/explicit-function-return-type": "off"
            }
        }
    ]
}
