# Key Principles

-   Use object-oriented programming with a focus on SOLID principles.
-   Prefer iteration and modularization over duplication.
-   Use descriptive variable and method names.
-   Favor dependency injection and service containers.
-   Use early returns whenever possible to make the code more readable.
-   Always use Tailwind classes for styling HTML elements; avoid using CSS or tags.
-   Use the cn utility instead of the tertiary operator in class tags whenever possible.
-   Use consts instead of functions, for example, “const toggle = () =>”. Also, define a type if possible.
-   Write functional tests using <PERSON>wright following the conventions established in existing tests

## UI and Styling

-   When creating spacing for flex or grid use Tailwindcss gap instead of space
-   Always use KeyValueGeneric instead of Record<string, any>
-   Sort objects by key, types and interfaces in ascending order
-   The usePage PageProps requires the generic to be a type. Don't change these types to interfaces
-   When using axios always use the useAxiosClient hook `const { axiosClient } = useAxiosClient();`
-   `axiosClient` is a function and should be used like this `axiosClient`
-   Always use Tailwindcss classes when styling. Avoid inline styles as much as possible.
-   Use gap instead of spacing TailwindCSS classes
-   Use absolute paths using aliases for imports instead of relative paths. All aliases start with @/
