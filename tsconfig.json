{
    "compilerOptions": {
        "allowJs": true,
        "module": "ESNext",
        "moduleResolution": "bundler",
        "jsx": "react-jsx",
        "strict": true,
        "isolatedModules": true,
        "target": "ESNext",
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true,
        "forceConsistentCasingInFileNames": true,
        "noEmit": true,
        "paths": {
            "@/pw/tests/*": ["./tests/Playwright/*"],
            "@/*": ["./resources/js/*"]
        },
        "typeRoots": ["node_modules/@types"],
        "skipLibCheck": true
        // "types": ["**/*.d.ts"]
    },
    "exclude": ["node_modules", "node_modules/**/*.d.ts", "vendor"],
    "include": [
        "resources/js/**/*.ts",
        "resources/js/**/*.tsx",
        "resources/js/**/*.d.ts",
        "tests/Playwright/**/*.ts",
        "tests/Playwright/**/*.d.ts",
        "declaration.d.ts"
    ]
}
