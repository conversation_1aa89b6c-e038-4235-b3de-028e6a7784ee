{"name": "1nhealth", "author": "1nHealth", "license": "ISC", "version": "4.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "dev-embed": "tsc && vite build --config vite.config.embed.js --mode development --watch", "dev-embed-orig": "tsc && vite build --config vite.config.embed-orig.js --mode development --watch", "dev-calendar": "tsc && vite build --config vite.config.embed.calendar.js --mode development --watch", "dev-build": "tsc && vite build --mode development --watch --minify false", "build:vite:main": "vite build --config vite.config.js", "build:vite:embed": "vite build --config vite.config.embed.js", "build:vite:calendar": "vite build --config vite.config.embed.calendar.js", "build": "tsc && (npm run build:vite:main & npm run build:vite:embed & npm run build:vite:calendar && wait)", "lint": "eslint ./resources/js --fix", "ts-lint": "tsc --noEmit", "pre-commit": "lint-staged", "prepare": "node -e \"if (process.env.NODE_ENV !== 'production'){process.exit(1)} \" || husky install", "docker:up": "docker compose --profile local up -d", "docker:stop": "docker compose --profile local stop", "test:docker:up": "APP_ENV=testing docker compose --profile testing up -d", "test:docker:stop": "docker compose --profile testing stop", "pw:docker:up": "APP_ENV=playwright docker compose --profile testing up -d", "pw:clear-config": "docker compose exec core_laravel_tenancy sh -c \"php artisan config:clear\"", "pw:build": "tsc && (npm run build:vite:main -- --mode testing & npm run build:vite:embed & npm run build:vite:calendar && wait)", "pw:build:watch": "tsc && (npm run build:vite:main -- --mode testing --watch --minify false & npm run build:vite:embed & npm run build:vite:calendar && wait)", "pw:start": "npm run pw:docker:up && npm run pw:clear-config && npm run pw:build:watch", "pw:stop": "npm run test:docker:stop", "pw:wait": "echo 'Waiting for services...' && sleep 10", "pw:test": "npx playwright test --project=functional-chromium", "pw:test:ui": "npx playwright test --project=functional-chromium --ui", "pw:test:debug": "npx playwright test --project=functional-chromium --debug", "pw:test:headed": "npx playwright test --project=functional-chromium --headed", "pw:test:visual": "npx playwright test --project=visual-regression", "pw:test:visual:ui": "npx playwright test --project=visual-regression --ui", "pw:test:visual:ui:update": "npx playwright test --project=visual-regression --update-snapshots --ui", "pw:test:visual:update": "npx playwright test --project=visual-regression --update-snapshots", "pw:report": "npx playwright show-report"}, "lint-staged": {"*.{ts,tsx}": ["bash -c tsc --noEmit"], "*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "**/*.php": "./vendor/bin/pint"}, "devDependencies": {"@commitlint/cli": "^17.6.6", "@commitlint/config-conventional": "^17.6.6", "@testomatio/reporter": "^1.6.14", "@types/deep-equal": "^1.0.4", "@types/js-cookie": "^3.0.6", "@types/react-highlight-words": "^0.20.0", "@types/react-is": "^18.2.1", "@types/react-scroll": "^1.8.7", "cypress": "^13.15.0", "husky": "^8.0.3", "lodash": "^4.17.19", "monocart-reporter": "^2.9.15", "postcss": "^8.4.18", "postcss-nesting": "^12.0.1", "prettier": "^3.0.0", "react": "19.1.0", "react-dom": "19.1.0", "typescript": "5.1.6", "typescript-plugin-css-modules": "^5.0.1"}, "dependencies": {"@algolia/autocomplete-core": "^1.18.1", "@dagrejs/dagre": "^1.1.4", "@dnd-kit/core": "^6.0.8", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.1", "@editorjs/checklist": "^1.5.0", "@editorjs/delimiter": "^1.3.0", "@editorjs/editorjs": "^2.28.0", "@editorjs/header": "^2.7.0", "@editorjs/link": "^2.5.0", "@editorjs/list": "^1.8.0", "@editorjs/paragraph": "^2.10.0", "@floating-ui/react": "^0.25.3", "@fortawesome/fontawesome-svg-core": "6.4.0", "@fortawesome/free-brands-svg-icons": "6.4.0", "@fortawesome/pro-duotone-svg-icons": "6.4.0", "@fortawesome/pro-light-svg-icons": "6.4.0", "@fortawesome/pro-regular-svg-icons": "6.4.0", "@fortawesome/pro-solid-svg-icons": "6.4.0", "@fortawesome/pro-thin-svg-icons": "6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@fullstory/browser": "^2.0.6", "@googlemaps/js-api-loader": "^1.16.2", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.10.0", "@iframe-resizer/child": "^5.5.5", "@iframe-resizer/parent": "^5.5.5", "@inertiajs/core": "^1.0.11", "@inertiajs/react": "^2.0.4", "@nivo/core": "^0.98.0", "@nivo/funnel": "^0.98.0", "@nivo/legends": "^0.98.0", "@playwright/test": "^1.46.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-use-controllable-state": "^1.1.0", "@react-email/components": "^0.0.41", "@react-email/render": "^1.0.4", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-query": "^5.63.0", "@tanstack/react-table": "^8.21.3", "@tiptap-pro/extension-unique-id": "^2.18.0-beta.13", "@tiptap/core": "^2.11.4", "@tiptap/extension-blockquote": "^2.11.5", "@tiptap/extension-bold": "^2.11.4", "@tiptap/extension-bullet-list": "^2.11.5", "@tiptap/extension-character-count": "^2.11.4", "@tiptap/extension-code": "^2.11.7", "@tiptap/extension-code-block": "^2.11.7", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-document": "^2.11.4", "@tiptap/extension-dropcursor": "^2.11.4", "@tiptap/extension-focus": "^2.11.4", "@tiptap/extension-font-family": "^2.11.4", "@tiptap/extension-gapcursor": "^2.11.4", "@tiptap/extension-hard-break": "^2.11.4", "@tiptap/extension-heading": "^2.11.4", "@tiptap/extension-highlight": "^2.11.5", "@tiptap/extension-history": "^2.11.4", "@tiptap/extension-horizontal-rule": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-italic": "^2.11.4", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-list-item": "^2.11.4", "@tiptap/extension-mention": "^2.11.5", "@tiptap/extension-ordered-list": "^2.11.5", "@tiptap/extension-paragraph": "^2.11.4", "@tiptap/extension-placeholder": "^2.11.4", "@tiptap/extension-strike": "^2.11.4", "@tiptap/extension-subscript": "^2.11.5", "@tiptap/extension-superscript": "^2.11.5", "@tiptap/extension-table": "^2.11.5", "@tiptap/extension-table-cell": "^2.11.5", "@tiptap/extension-table-header": "^2.11.5", "@tiptap/extension-table-row": "^2.11.5", "@tiptap/extension-task-item": "^2.12.0", "@tiptap/extension-task-list": "^2.12.0", "@tiptap/extension-text": "^2.11.4", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.11.4", "@tiptap/extension-underline": "^2.11.4", "@tiptap/pm": "^2.11.3", "@tiptap/react": "^2.11.3", "@tiptap/suggestion": "^2.11.5", "@types/google.maps": "^3.53.6", "@types/jquery": "^3.5.16", "@types/lodash": "^4.14.195", "@types/markdown-it": "^14.1.2", "@types/md5": "^2.3.2", "@types/node": "^20.5.9", "@types/numeral": "^2.0.2", "@types/react": "^18.2.14", "@types/react-dom": "^18.0.10", "@types/uuid": "^9.0.2", "@vitejs/plugin-react": "^4.3.4", "@welldone-software/why-did-you-render": "^10.0.1", "@xyflow/react": "^12.4.2", "ag-grid-community": "^33.3.0", "ag-grid-enterprise": "^33.3.0", "ag-grid-react": "^33.3.0", "allure-js-commons": "^3.2.0", "allure-playwright": "^3.2.0", "antd": "^5.7.0", "axios": "^1.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "collect.js": "^4.36.1", "date-fns": "^3.6.0", "dayjs": "^1.11.9", "deep-equal": "^2.2.3", "docx": "^9.3.0", "dotenv": "^16.4.5", "easy-peasy": "^6.0.1", "echo-drag-handle-plugin": "^0.0.2", "editorjs-html": "^3.4.3", "embla-carousel-react": "^8.6.0", "eslint": "^8.44.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.0.0", "eslint-config-react-app": "^7.0.1", "eslint-import-resolver-typescript": "^3.5.5", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.2.2", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-webpack-plugin": "^4.0.1", "flexsearch": "^0.8.158", "framer-motion": "^12.9.2", "highcharts": "^11.1.0", "highcharts-react-official": "^3.2.0", "highcharts-rounded-corners": "^1.0.6", "input-otp": "^1.4.2", "jquery": "^3.7.0", "js-cookie": "^3.0.5", "laravel-echo": "^1.15.2", "laravel-vite-plugin": "^1.2.0", "libphonenumber-js": "^1.10.44", "lint-staged": "^16.0.0", "lucide-react": "^0.525.0", "mammoth": "^1.9.0", "markdown-it": "^14.1.0", "material-ripple-effects": "^2.0.1", "md5": "^2.3.0", "mermaid": "^11.6.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "next-themes": "^0.4.6", "numeral": "^2.0.6", "playwright": "^1.55.0", "postcss-import": "^15.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "prism-code-editor-lightweight": "^0.0.3", "prosemirror-docx": "^0.4.1", "prosemirror-markdown": "^1.13.2", "public-ip": "^6.0.1", "pusher-js": "^8.3.0", "query-string": "^8.1.0", "rc-field-form": "^1.37.0", "rc-segmented": "^2.3.0", "re-resizable": "^6.10.3", "react-calendly": "^4.3.0", "react-colorful": "^5.6.1", "react-day-picker": "^9.7.0", "react-email": "^4.0.15", "react-email-editor": "1.7.8", "react-highlight-words": "^0.21.0", "react-hook-form": "^7.61.1", "react-idle-timer": "^5.7.2", "react-image-crop": "^11.0.7", "react-intersection-observer": "^9.14.1", "react-is": "^18.2.0", "react-phone-number-input": "^3.3.6", "react-resizable-panels": "^3.0.2", "react-scroll": "^1.8.9", "react-select": "^5.7.4", "react-select-async-paginate": "^0.7.3", "reactjs-signal": "^1.1.1", "recharts": "^2.15.3", "scroll-into-view-if-needed": "^3.1.0", "shiki": "^3.2.2", "sonner": "^2.0.3", "styled-jsx": "^5.1.2", "styled-jsx-plugin-postcss": "^4.0.1", "svg64": "^2.0.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "tippy.js": "^6.3.7", "uuid": "^9.0.0", "valibot": "^1.0.0-rc.3", "vaul": "^1.1.2", "vite": "^6.0.11", "vite-plugin-babel-macros": "^1.0.6", "vite-plugin-checker": "^0.8.0", "vite-plugin-eslint": "^1.8.1", "ziggy-js": "^2.5.2", "zod": "^3.25.7", "zod-to-json-schema": "^3.24.1"}, "optionalDependencies": {"@rollup/rollup-linux-arm64-musl": "4.40.0", "@rollup/rollup-linux-x64-gnu": "4.40.0", "@rollup/rollup-linux-x64-musl": "4.40.0"}}