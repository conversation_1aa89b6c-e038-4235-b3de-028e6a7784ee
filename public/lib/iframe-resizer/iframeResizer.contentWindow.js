/*!
 *  @preserve
 *  
 *  @module      iframe-resizer/child 5.5.5 (umd) - 2025-09-01
 *
 *  @license     GPL-3.0 for non-commercial use only.
 *               For commercial use, you must purchase a license from
 *               https://iframe-resizer.com/pricing
 * 
 *  @description Keep same and cross domain iFrames sized to their content 
 *
 *  <AUTHOR> <<EMAIL>>
 * 
 *  @see         {@link https://iframe-resizer.com}
 * 
 *  @copyright  (c) 2013 - 2025, <PERSON>. All rights reserved.
 */


!function(e){"function"==typeof define&&define.amd?define(e):e()}(function(){"use strict";const e="font-weight: normal;",t="font-weight: bold;",n="font-style: italic;",o=e+n,r="default",i=Object.freeze({assert:!0,error:!0,warn:!0}),a={expand:!1,defaultEvent:void 0,event:void 0,label:"AutoConsoleGroup",showTime:!0},s={profile:0,profileEnd:0,timeStamp:0,trace:0},c=Object.assign(console);const{fromEntries:l,keys:u}=Object,d=e=>[e,c[e]],f=e=>t=>[t,function(n){e[t]=n}],m=(e,t)=>l(u(e).map(t));const p=!(typeof window>"u"||"function"!=typeof window.matchMedia)&&window.matchMedia("(prefers-color-scheme: dark)").matches,h=p?"color: #A9C7FB;":"color: #135CD2;",g=p?"color: #E3E3E3;":"color: #1F1F1F;",y="5.5.5",b="iframeResizer",v="beforeUnload",w="init",z="message",$="pageInfo",S="parentInfo",O="scrollToOffset",E="title",M=10,k="data-iframe-size",j="data-iframe-overflowed",x="data-iframe-ignore",T="height",A="width",C="readystatechange",I="bottom",N="right",P="autoResizeEnabled",R=Symbol("sizeChanged"),B="manualResize",q="parentResize",L={[B]:1,[q]:1},D="setOffsetSize",F="resizeObserver",W="overflowObserver",V="mutationObserver",H="visibilityObserver",U="[iFrameSizer]",Z=new Set(["head","body","meta","base","title","script","link","style","map","area","option","optgroup","template","track","wbr","nobr"]),J=(e,t,n,o)=>e.addEventListener(t,n,o||!1),_=e=>{if(!e)return"";let t=-559038744,n=1103547984;for(let o,r=0;r<e.length;r++)o=e.codePointAt(r),t=Math.imul(t^o,2246822519),n=Math.imul(n^o,3266489917);return t^=Math.imul(t^n>>>15,1935289751),n^=Math.imul(n^t>>>15,3405138345),t^=n>>>16,n^=t>>>16,(2097152*(n>>>0)+(t>>>11)).toString(36)},Q=e=>e.replace(/[A-Za-z]/g,e=>String.fromCodePoint((e<="Z"?90:122)>=(e=e.codePointAt(0)+19)?e:e-26)),G=["spjluzl","rlf","clyzpvu"],Y=["<yi>Puchspk Spjluzl Rlf</><iy><iy>","<yi>Tpzzpun Spjluzl Rlf</><iy><iy>","Aopz spiyhyf pz hchpshisl dpao ivao Jvttlyjphs huk Vwlu-Zvbyjl spjluzlz.<iy><iy><i>Jvttlyjphs Spjluzl</><iy>Mvy jvttlyjphs bzl, <p>pmyhtl-ylzpgly</> ylxbpylz h svd jvza vul aptl spjluzl mll. Mvy tvyl pumvythapvu cpzpa <b>oaawz://pmyhtl-ylzpgly.jvt/wypjpun</>.<iy><iy><i>Vwlu Zvbyjl Spjluzl</><iy>Pm fvb hyl bzpun aopz spiyhyf pu h uvu-jvttlyjphs vwlu zvbyjl wyvqlja aolu fvb jhu bzl pa mvy myll bukly aol alytz vm aol NWS C3 Spjluzl. Av jvumpyt fvb hjjlwa aolzl alytz, wslhzl zla aol <i>spjluzl</> rlf pu <p>pmyhtl-ylzpgly</> vwapvuz av <i>NWSc3</>.<iy><iy>Mvy tvyl pumvythapvu wslhzl zll: <b>oaawz://pmyhtl-ylzpgly.jvt/nws</>","<i>NWSc3 Spjluzl Clyzpvu</><iy><iy>Aopz clyzpvu vm <p>pmyhtl-ylzpgly</> pz ilpun bzlk bukly aol alytz vm aol <i>NWS C3</> spjluzl. Aopz spjluzl hssvdz fvb av bzl <p>pmyhtl-ylzpgly</> pu Vwlu Zvbyjl wyvqljaz, iba pa ylxbpylz fvby wyvqlja av il wbispj, wyvcpkl haaypibapvu huk il spjluzlk bukly clyzpvu 3 vy shaly vm aol NUB Nlulyhs Wbispj Spjluzl.<iy><iy>Pm fvb hyl bzpun aopz spiyhyf pu h uvu-vwlu zvbyjl wyvqlja vy dlizpal, fvb dpss ullk av wbyjohzl h svd jvza vul aptl jvttlyjphs spjluzl.<iy><iy>Mvy tvyl pumvythapvu cpzpa <b>oaawz://pmyhtl-ylzpgly.jvt/wypjpun</>.","<iy><yi>Zvsv spjluzl kvlz uva zbwwvya jyvzz-kvthpu</><iy><iy>Av bzl <p>pmyhtl-ylzpgly</> dpao jyvzz kvthpu pmyhtlz fvb ullk lpaoly aol Wyvmlzzpvuhs vy Ibzpulzz spjluzlz. Mvy klahpsz vu bwnyhkl wypjpun <NAME_EMAIL>."],X=["NWSc3","zvsv","wyv","ibzpulzz","vlt"],K=Object.fromEntries(["2cgs7fdf4xb","1c9ctcccr4z","1q2pc4eebgb","ueokt0969w","w2zxchhgqz","1umuxblj2e5"].map((e,t)=>[e,Math.max(0,t-1)])),ee=e=>Q(Y[e]),te=e=>{const t=e[Q(G[0])]||e[Q(G[1])]||e[Q(G[2])];if(!t)return-1;const n=t.split("-");let o=function(e=""){let t=-2;const n=_(Q(e));return n in K&&(t=K[n]),t}(n[0]);return 0===o||(e=>e[2]===_(e[0]+e[1]))(n)||(o=-2),o},ne=(e,...t)=>setTimeout(()=>e(...t),0),oe=e=>{let t=!1;return function(){return t?void 0:(t=!0,Reflect.apply(e,this,arguments))}},re=e=>e,ie=e=>Math.round(1e3*e)/1e3,ae=e=>e.charAt(0).toUpperCase()+e.slice(1),se=e=>""!=`${e}`&&void 0!==e;const ce=(e,t,n)=>{if(typeof e!==t)throw new TypeError(`${n} is not a ${ae(t)}`)};let le=!0,ue=b;const de=(fe=function(n={}){const l={},u={},p=[],h={...a,expand:!n.collapsed||a.expanded,...n};let g="";function y(){p.length=0,g=""}function b(){delete h.event,y()}const v=()=>!!p.some(([e])=>e in i)||!!h.expand;function w(){if(0!==p.length){c[v()?"group":"groupCollapsed"](`%c${h.label}%c ${(e=>{const t=e.event||e.defaultEvent;return t?`${t}`:""})(h)} %c${h.showTime?g:""}`,e,t,o);for(const[e,...t]of p)c.assert(e in c,`Unknown console method: ${e}`),e in c&&c[e](...t);c.groupEnd(),b()}else b()}function z(){""===g&&(g=function(){const e=new Date,t=(t,n)=>e[t]().toString().padStart(n,"0");return`@ ${t("getHours",2)}:${t("getMinutes",2)}:${t("getSeconds",2)}.${t("getMilliseconds",3)}`}())}function $(e,...t){0===p.length&&(z(),queueMicrotask(()=>queueMicrotask(w))),p.push([e,...t])}function S(e=r,...t){l[e]?$("log",`${e}: ${performance.now()-l[e]} ms`,...t):$("timeLog",e,...t)}return{...m(h,f(h)),...m(console,e=>[e,(...t)=>$(e,...t)]),...m(s,d),assert:function(e,...t){!0!==e&&$("assert",e,...t)},count:function(e=r){u[e]?u[e]+=1:u[e]=1,$("log",`${e}: ${u[e]}`)},countReset:function(e=r){delete u[e]},endAutoGroup:w,errorBoundary:e=>(...t)=>{let n;try{n=e(...t)}catch(e){if(!Error.prototype.isPrototypeOf(e))throw e;$("error",e),w()}return n},event:function(e){z(),h.event=e},purge:y,time:function(e=r){z(),l[e]=performance.now()},timeEnd:function(e=r){S(e),delete l[e]},timeLog:S,touch:z}},fe?.__esModule?fe.default:fe);var fe;const me=de({label:`${b}(child)`,expand:!1});var pe;const he=(pe="log",(...e)=>!le||me[pe](...e));const{assert:ge,endAutoGroup:ye,error:be,errorBoundary:ve,event:we,label:ze,purge:$e,warn:Se}=me,Oe=e=>{return me.warn((t=re,e=>window.chrome?t(e.replaceAll("<br>","\n").replaceAll("<rb>","[31;1m").replaceAll("</>","[m").replaceAll("<b>","[1m").replaceAll("<i>","[3m").replaceAll("<u>","[4m")):t(e.replaceAll("<br>","\n").replaceAll(/<[/a-z]+>/gi,"")))(e));var t},Ee=Oe,Me=(e=>(t,n="renamed to")=>(o,r,i="",a="")=>e(a,`<rb>Deprecated ${t}(${o.replace("()","")})</>\n\nThe <b>${o}</> ${t.toLowerCase()} has been ${n} <b>${r}</>. ${i}Use of the old ${t.toLowerCase()} will be removed in a future version of <i>iframe-resizer</>.`))((e,t)=>Oe(t)),ke=Me("Method"),je=Me("Method","replaced with"),xe=Me("Option"),Te=["min-height","min-width","max-height","max-width"],Ae=new Set,Ce=(e,t)=>window.getComputedStyle(e).getPropertyValue(t),Ie=(e,t)=>{return(n=Ce(e,t))&&"0px"!==n&&"auto"!==n&&"none"!==n;var n};function Ne({href:e}){Ae.has(e)||Ae.add(e)}const Pe=(e,t)=>function(e,t){const n=e.style[t];return n?{source:"an inline style attribute",value:n}:null}(e,t)||function(e,t){for(const n of document.styleSheets)try{for(const o of n.cssRules||[])if(o.selectorText&&e.matches(o.selectorText)){const e=o.style[t];if(e)return{source:"STYLE"===n.ownerNode.tagName?"an inline <style> block":`stylesheet (${n.href})`,value:e}}}catch(e){Ne(n)}return{source:"cross-origin stylesheet",value:Ce(e,t)}}(e,t),Re=(e,t)=>{const{source:n,value:o}=Pe(e,t),r=(e=>e.tagName?e.tagName.toLowerCase():"unknown")(e);Oe(`The <b>${t}</> CSS property is set to <b>${o}</> on the <b><${r}></> element via ${n}. This may cause issues with the correct operation of <i>iframe-resizer</>.\n\nIf you wish to restrict the size of the iframe, then you should set this property on the iframe element itself, not the content inside it.`)};function Be(){for(const e of[document.documentElement,document.body])for(const t of Te)Ie(e,t)&&Re(e,t)}const qe=e=>t=>void 0===t?void 0:e(t),Le=qe(e=>"true"===e),De=qe(Number),Fe=e=>e=>{e.size},We=((e="")=>t=>n=>{n.size>0&&be(`${t}Observer ${e}:`,...Array.from(n).flatMap(e=>["\n",e]))})("already attached"),Ve=e=>e=>{e.size},He=(t,n=!0)=>o=>{o>0&&he(`${n?"At":"De"}tached ${t}Observer ${n?"to":"from"} %c${o}%c element${1===o?"":"s"}`,h,e)},Ue=(e,t,n,o)=>{const r=Ve(e);return e=>{const i=new Set;let a=0;for(const o of e)n.has(o)&&(t.unobserve(o),n.delete(o),i.add(o),a+=1);r(i),o(a),i.clear()}},Ze=new Set,Je=new Set,_e=new Set,Qe=[],Ge={attributes:!0,attributeFilter:[x,k],attributeOldValue:!1,characterData:!1,characterDataOldValue:!1,childList:!0,subtree:!0};let Ye,Xe=1,Ke=!1,et=0;const tt=e=>{e.size},nt=e=>{e.size},ot=e=>{e.size},rt=e=>e.nodeType!==Node.ELEMENT_NODE||Z.has(e.tagName.toLowerCase());function it(e){const t=e.addedNodes;for(const e of t)rt(e)||Ze.add(e)}function at(e){const t=e.removedNodes;for(const e of t)rt(e)||(Ze.has(e)?(Ze.delete(e),_e.add(e)):Je.add(e))}const st=e=>{he("Mutations:",e);for(const t of e)it(t),at(t);tt(Ze),nt(Je),ot(_e),_e.clear()};const ct=e=>()=>{const t=performance.now(),n=t-et,o=16*Xe+++2;if(n>o&&n<200)return we("mutationThrottled"),he("Update delayed due to heavy workload on the callStack"),he(`EventLoop busy time: %c${ie(n)}ms %c> Max wait: %c${o-2}ms`,h,g,h),setTimeout(Ye,16*Xe),void(et=t);Xe=1,Qe.forEach(st),Qe.length=0,Ke=!1,Je.size,Ze.size,e({addedNodes:Ze,removedNodes:Je}),Ze.clear(),Je.clear()};function lt(e){Qe.push(e),Ke||(et=performance.now(),Ke=!0,requestAnimationFrame(Ye))}function ut(e){const t=new window.MutationObserver(lt),n=document.body||document.documentElement;return Ye=ct(e),t.observe(n,Ge),he("Attached MutationObserver to body"),{...t,disconnect:()=>{Ze.clear(),Je.clear(),Qe.length=0,t.disconnect(),he("Detached MutationObserver")}}}const dt="Overflow",ft=He(dt),mt=He(dt,!1),pt=Fe(dt),ht=We(dt),gt=e=>e.hidden||null===e.offsetParent||"none"===e.style.display,yt=(e,t)=>{const n=t.side,o={root:t.root,rootMargin:"0px",threshold:1},r=window?.requestAnimationFrame||re,i=(t=!1)=>e(t),a=(e,t)=>0===e||e>t[n],s=(e,t)=>e.toggleAttribute(j,t);const c=new IntersectionObserver(function(e){for(const t of e){const{boundingClientRect:e,rootBounds:o,target:r}=t;if(!o)continue;const i=e[n],c=a(i,o)&&!gt(r);s(r,c)}r(i)},o),l=new WeakSet;return{attachObservers:function(e){const t=new Set,n=new Set;let o=0;for(const r of e)r.nodeType===Node.ELEMENT_NODE&&(l.has(r)?t.add(r):(c.observe(r),l.add(r),n.add(r),o+=1));ht(t),pt(n),ft(o),n.clear(),t.clear()},detachObservers:Ue(dt,c,l,mt),disconnect:()=>{c.disconnect(),he("Detached OverflowObserver")}}},bt="--ifr-start",vt="--ifr-end",wt="--ifr-measure",zt=[];let $t,St={},Ot=0;function Et(){try{performance.clearMarks(bt),performance.clearMarks(vt),performance.clearMeasures(wt)}catch{}}function Mt(e){e.getEntries().forEach(e=>{if(e.name===vt)try{const{duration:t}=performance.measure(wt,bt,vt);St=e.detail,zt.push(t),zt.length>100&&zt.shift()}catch{}})}function kt(){he("Attached PerformanceObserver to page");const e=new PerformanceObserver(Mt);return e.observe({entryTypes:["mark"]}),$t=setInterval(()=>{if(zt.length<10)return;if(St.hasTags&&St.len<25)return;zt.sort();const e=Math.min(zt.reduce((e,t)=>e+t,0)/zt.length,zt[Math.floor(zt.length/2)]),t=ie(e);t>Ot&&(Ot=t,we("performanceObserver")),Et(),e<=4||(clearInterval($t),Oe(`<rb>Performance Warning</>\n\nCalculating the page size is taking an excessive amount of time (${ie(e)}ms).\n\nTo improve performance add the <b>data-iframe-size</> attribute to the ${St.Side.toLowerCase()} most element on the page. For more details see: <u>https://iframe-resizer.com/perf</>.`))},5e3),{disconnect:()=>{Et(),clearInterval($t),e.disconnect(),he("Detached PerformanceObserver")}}}const jt="Resize",xt=He(jt),Tt=He(jt,!1),At=Fe(jt),Ct=We(jt),It=new WeakSet,Nt=new Set,Pt=new Set;let Rt;function Bt(e){let t=0;for(const n of e){if(n.nodeType!==Node.ELEMENT_NODE)continue;const e=getComputedStyle(n)?.position;""!==e&&"static"!==e&&(It.has(n)?Nt.add(n):(Rt.observe(n),It.add(n),Pt.add(n),t+=1))}Ct(Nt),At(Pt),xt(t),Pt.clear(),Nt.clear()}function qt(e){const t=new IntersectionObserver(t=>e(t[0].isIntersecting),{threshold:0}),n=document.documentElement;return t.observe(n),he("Attached VisibilityObserver to page"),{disconnect:()=>{t.disconnect(),he("Detached VisibilityObserver")}}}const Lt=e=>(t,n)=>{if(n in t){if(typeof t[n]===e)return t[n];throw new TypeError(`${n} is not a ${e}.`)}},Dt=Lt("function"),Ft=Lt("number"),Wt=Lt("string");"undefined"!=typeof window&&function(){const o={height:()=>(Se("Custom height calculation function not defined"),Sn.auto()),width:()=>(Se("Custom width calculation function not defined"),On.auto())},r={bodyOffset:1,bodyScroll:1,offset:1,documentElementOffset:1,documentElementScroll:1,boundingClientRect:1,max:1,min:1,grow:1,lowestElement:1},i={},a="auto",s=[],c="scroll";let l,u,d,f,m,p,_,Y=!0,K="",ie=0,de="",fe="",pe=!1,Me=!0,Te=!1,Ae=!0,Ce=!1,Ie=!1,Ne=!0,Pe=!1,Re=1,qe=a,Fe="",We=!0,Ve={},He=!1,Ze=!1,Je=!1,_e=0,Qe=!1,Ge=0,Ye=0,Xe=new Set,Ke="",et="child",tt=!1,nt="",ot=[],rt=window.parent,it="*",at=0,st=!1,ct=1,lt=c,dt=window,ft=()=>{Se("onMessage function not defined")},mt=()=>{},pt=null,ht=null;function gt(e){var t;!function(e){Ke=e[0]??Ke,ie=De(e[1])??ie,Te=Le(e[2])??Te,Je=Le(e[3])??Je,Y=Le(e[6])??Y,de=e[7]??de,qe=e[8]??qe,K=e[9]??K,fe=e[10]??fe,at=De(e[11])??at,Ve.enable=Le(e[12])??!1,et=e[13]??et,lt=e[14]??lt,Qe=Le(e[15])??Qe,Ge=De(e[16])??Ge,Ye=De(e[17])??Ye,Me=Le(e[18])??Me,l=e[19]??l,p=e[20]??p,_e=De(e[21])??_e,Ze=Le(e[23])??Ze}(e),ue=(t={id:Ke,enabled:Je,expand:Ze}).id||b,me.label(`${ue}`),me.expand(t.expand),le=t.enabled,function(){function e(e){_=Dt(e,"onBeforeResize")??_,ft=Dt(e,"onMessage")??ft,mt=Dt(e,"onReady")??mt,"number"==typeof e?.offset&&(xe("offset","offsetSize"),Me&&(Ge=Ft(e,"offset")??Ge),Te&&(Ye=Ft(e,"offset")??Ye)),"number"==typeof e?.offsetSize&&(Me&&(Ge=Ft(e,"offsetSize")??Ge),Te&&(Ye=Ft(e,"offsetSize")??Ye)),u=Wt(e,Q(G[0]))??u,Fe=Wt(e,"ignoreSelector")??Fe,nt=Wt(e,"sizeSelector")??nt,it=Wt(e,"targetOrigin")??it,qe=e?.heightCalculationMethod||qe,lt=e?.widthCalculationMethod||lt}function t(e,t){return"function"==typeof e&&(Oe(`<rb>Deprecated Option(${t}CalculationMethod)</>\n\nThe use of <b>${t}CalculationMethod</> as a function is deprecated and will be removed in a future version of <i>iframe-resizer</>. Please use the new <b>onBeforeResize</> event handler instead.\n\nSee <u>https://iframe-resizer.com/api/child</> for more details.`),o[t]=e,e="custom"),e}if(1===_e)return;const n=window.iframeResizer||window.iFrameResizer;"object"==typeof n&&(e(n),qe=t(qe,T),lt=t(lt,A),he(`Set targetOrigin for parent: %c${it}`,h))}(),[Mt,At,Gt,Ot,xt,_t,Qt,Zt,Et,$t,pe?re:Be,Pt,tn,en,Kt,Lt,()=>Ct("background",K),()=>Ct("padding",fe),pe?re:Vt,Xt,Yt,pn,wt].forEach(e=>{try{e()}catch(e){if(_e<0)throw e;Oe("<rb>Error in setup function</>\n<i>iframe-resizer</> detected an error during setup.\n\nPlease report the following error message at <u>https://github.com/davidjbradshaw/iframe-resizer/issues</>"),be(e)}}),zt(oe(mt)),An(w,"Init message from host page",void 0,void 0,`${y}:${_e}`),document.title&&""!==document.title&&Nn(0,0,E,document.title)}function wt(){J(window,v.toLowerCase(),()=>{s.forEach(e=>e()),Nn(0,0,v)})}function zt(e){"complete"===document.readyState?ne(e):J(document,C,()=>zt(e))}function $t(){ot=document.querySelectorAll(`[${k}]`),Pe=ot.length>0}let St=0;function Ot(){const n=document.querySelectorAll(`*[${x}]`);return Ce=n.length>0,Ce&&n.length!==St&&(function(n){const o=1===n.length?"":"s";Se(`%c[${x}]%c found on %c${n.length}%c element${o}`,t,e,t,e)}(n),St=n.length),Ce}function Et(){"BackCompat"===document.compatMode&&Oe("<rb>Quirks Mode Detected</>\n\nThis iframe is running in the browser's legacy <b>Quirks Mode</>, this may cause issues with the correct operation of <i>iframe-resizer</>. It is recommended that you switch to the modern <b>Standards Mode</>.\n\nFor more information see <u>https://iframe-resizer.com/quirks-mode</>.\n")}function Mt(){p&&""!==p&&"false"!==p?p!==y&&Oe(`<b>Version mismatch</>\n\nThe parent and child pages are running different versions of <i>iframe resizer</>.\n\nParent page: ${p} - Child page: ${y}.\n`):Oe("<rb>Legacy version detected on parent page</>\n\nDetected legacy version of parent page script. It is recommended to update the parent page to use <b>@iframe-resizer/parent</>.\n\nSee <u>https://iframe-resizer.com/setup/</> for more details.\n")}function xt(){try{tt=1===_e||"iframeParentListener"in window.parent}catch(e){}}function At(){Te===Me&&(pe=!0)}function Ct(e,t){void 0!==t&&""!==t&&"null"!==t&&(document.body.style.setProperty(e,t),he(`Set body ${e}: %c${t}`,h))}function Nt(e,t,n){if(""!==n)for(const e of document.querySelectorAll(n))e.toggleAttribute(t,!0)}function Pt(){Nt(0,k,nt),Nt(0,x,Fe)}function Lt(){var e,t;void 0===de&&(de=`${ie}px`),Ct("margin",(e="margin",(t=de).includes("-")&&(Se(`Negative CSS value ignored for ${e}`),t=""),t))}function Vt(){const e=e=>e.style.setProperty(T,"auto","important");e(document.documentElement),e(document.body)}function Ht(e){({add(t){function n(){An(e.eventName,e.eventType)}i[t]=n,J(window,t,n,{passive:!0})},remove(e){const t=i[e];var n,o;delete i[e],n=e,o=t,window.removeEventListener(n,o,!1)}})[e.method](e.eventName)}function Ut(e){Ht({method:e,eventType:"After Print",eventName:"afterprint"}),Ht({method:e,eventType:"Before Print",eventName:"beforeprint"}),Ht({method:e,eventType:"Ready State Change",eventName:C})}function Zt(){let e=!1;const t=t=>document.querySelectorAll(`[${t}]`).forEach(n=>{e=!0,n.removeAttribute(t),n.toggleAttribute(k,!0)});t("data-iframe-height"),t("data-iframe-width"),e&&Oe("<rb>Deprecated Attributes</>\n          \nThe <b>data-iframe-height</> and <b>data-iframe-width</> attributes have been deprecated and replaced with the single <b>data-iframe-size</> attribute. Use of the old attributes will be removed in a future version of <i>iframe-resizer</>.")}function Jt(e,t,n){const{label:o}=n;return t!==e&&(e in n||(Se(`${e} is not a valid option for ${o}CalculationMethod.`),e=t),e in r)&&Oe(`<rb>Deprecated ${o}CalculationMethod (${e})</>\n\nThis version of <i>iframe-resizer</> can auto detect the most suitable ${o} calculation method. It is recommended that you ${p?"remove this option.":`set this option to <b>'auto'</> when using an older version of <i>iframe-resizer</> on the parent page. This can be done on the child page by adding the following code:\n          \nwindow.iframeResizer = {\n  license: 'xxxx',\n  ${o}CalculationMethod: 'auto',\n}\n`}\n`),e}function _t(){qe=Jt(qe,a,Sn)}function Qt(){lt=Jt(lt,c,On)}function Gt(){const t=_e,n=te({key:l}),o=te({key:u});if(_e=Math.max(n,o),_e<0){if(_e=Math.min(n,o),$e(),Oe(`${ee(_e+2)}${ee(2)}`),se(p))throw ee(_e+2).replace(/<\/?[a-z][^>]*>|<\/>/gi,"")}else(!se(p)||t>-1&&_e>t)&&(sessionStorage.getItem("ifr")!==y&&function(t,n){console.info(`${ue} %ciframe-resizer ${t}`,le||n<1?"font-weight: bold;":e)}(`v${y} (${(e=>Q(X[e]))(_e)})`,_e),_e<2&&Ee(ee(3)),sessionStorage.setItem("ifr",y))}function Yt(){Ut("add"),s.push(()=>Ut("remove"))}function Xt(){const e=document.createElement("div");e.style.clear="both",e.style.display="block",e.style.height="0",document.body.append(e)}function Kt(){function e(e){const t=e.getBoundingClientRect(),n={x:document.documentElement.scrollLeft,y:document.documentElement.scrollTop};return{x:parseInt(t.left,M)+parseInt(n.x,M),y:parseInt(t.top,M)+parseInt(n.y,M)}}function t(t){const n=t.split("#")[1]||t,o=decodeURIComponent(n),r=document.getElementById(o)||document.getElementsByName(o)[0];void 0===r?Nn(0,0,"inPageLink",`#${n}`):function(t){const n=e(t);Nn(n.y,n.x,O)}(r)}function n(){const{hash:e,href:n}=window.location;""!==e&&"#"!==e&&t(n)}Ve.enable&&(1===_e?Oe("In page linking requires a Professional or Business license. Please see <u>https://iframe-resizer.com/pricing</> for more details."):(function(){for(const e of document.querySelectorAll('a[href^="#"]'))"#"!==e.getAttribute("href")&&J(e,"click",n=>{n.preventDefault(),t(e.getAttribute("href"))})}(),J(window,"hashchange",n),setTimeout(n,128))),Ve={findTarget:t}}function en(){function e(e){Nn(0,0,e.type,`${e.screenY}:${e.screenX}`)}function t(t,n){J(window.document,t,e)}!0===Qe&&(t("mouseenter"),t("mouseleave"))}function tn(){1!==_e&&(dt.parentIframe=Object.freeze({autoResize:e=>(ce(e,"boolean","parentIframe.autoResize(enable) enable"),!1===Te&&!1===Me?(we(P),Oe("Auto Resize can not be changed when <b>direction</> is set to 'none'."),!1):(!0===e&&!1===Y?(Y=!0,queueMicrotask(()=>An(P,"Auto Resize enabled"))):!1===e&&!0===Y&&(Y=!1),Nn(0,0,"autoResize",JSON.stringify(Y)),Y)),close(){Nn(0,0,"close")},getId:()=>Ke,getOrigin:()=>(we("getOrigin"),ke("getOrigin()","getParentOrigin()"),d),getParentOrigin:()=>d,getPageInfo(e){if("function"==typeof e)return pt=e,Nn(0,0,$),void je("getPageInfo()","getParentProps()","See <u>https://iframe-resizer.com/upgrade</> for details. ");pt=null,Nn(0,0,"pageInfoStop")},getParentProps:e=>(ce(e,"function","parentIframe.getParentProps(callback) callback"),ht=e,Nn(0,0,S),()=>{ht=null,Nn(0,0,"parentInfoStop")}),getParentProperties(e){ke("getParentProperties()","getParentProps()"),this.getParentProps(e)},moveToAnchor(e){ce(e,"string","parentIframe.moveToAnchor(anchor) anchor"),Ve.findTarget(e)},reset(){!function(){const e=qe;qe=a,st||(st=!0,requestAnimationFrame(()=>{st=!1})),Cn("reset"),qe=e}()},setOffsetSize(e){ce(e,"number","parentIframe.setOffsetSize(offset) offset"),Ge=e,Ye=e,An(D,`parentIframe.setOffsetSize(${e})`)},scrollBy(e,t){ce(e,"number","parentIframe.scrollBy(x, y) x"),ce(t,"number","parentIframe.scrollBy(x, y) y"),Nn(t,e,"scrollBy")},scrollTo(e,t){ce(e,"number","parentIframe.scrollTo(x, y) x"),ce(t,"number","parentIframe.scrollTo(x, y) y"),Nn(t,e,"scrollTo")},scrollToOffset(e,t){ce(e,"number","parentIframe.scrollToOffset(x, y) x"),ce(t,"number","parentIframe.scrollToOffset(x, y) y"),Nn(t,e,O)},sendMessage(e,t){t&&ce(t,"string","parentIframe.sendMessage(msg, targetOrigin) targetOrigin"),Nn(0,0,z,JSON.stringify(e),t)},setHeightCalculationMethod(e){qe=e,_t()},setWidthCalculationMethod(e){lt=e,Qt()},setTargetOrigin(e){ce(e,"string","parentIframe.setTargetOrigin(targetOrigin) targetOrigin"),it=e},resize(e,t){void 0!==e&&ce(e,"number","parentIframe.resize(customHeight, customWidth) customHeight"),void 0!==t&&ce(t,"number","parentIframe.resize(customHeight, customWidth) customWidth"),An(B,`parentIframe.resize(${e||""}${t?`,${t}`:""})`,e,t)},size(e,t){ke("size()","resize()"),this.resize(e,t)}}),dt.parentIFrame=dt.parentIframe)}let nn=new Set;function on(){const e=document.querySelectorAll(`[${j}]`);Xe=function(e){const t=new Set,n=new Set;for(const o of e)o.closest(`[${x}]`)?n.add(o):t.add(o);return n.size>0&&queueMicrotask(()=>{we("overflowIgnored"),he("Ignoring elements with [data-iframe-ignore] > *:\n",n),ye()}),t}(e),Ie=Xe.size>0,"function"==typeof Set.prototype.symmetricDifference&&(Ne=Xe.symmetricDifference(nn).size>0),nn=Xe}function rn(){switch(on(),!0){case!Ne:return;case Xe.size>1:he("Overflowed Elements:",Xe);break;case Ie:break;default:he("No overflow detected")}An(W,"Overflow updated")}function an(e){const t={root:document.documentElement,side:Me?I:N};return f=yt(rn,t),f.attachObservers(e),f}function sn(e){if(!Array.isArray(e)||0===e.length)return;const t=e[0].target;An(F,`Element resized <${function(e){switch(!0){case!se(e):return"";case se(e.id):return`${e.nodeName}#${e.id}`;case se(e.name):return`${e.nodeName} (${e.name}`;case se(e.className):return`${e.nodeName}.${e.className}`;default:return e.nodeName}}(t)}>`)}function cn(e){return Rt=new ResizeObserver(sn),Rt.observe(document.body),It.add(document.body),he("Attached ResizeObserver to body"),m={attachObserverToNonStaticElements:Bt,detachObservers:Ue(jt,Rt,It,Tt),disconnect:()=>{Rt.disconnect(),he("Detached ResizeObserver")}},m.attachObserverToNonStaticElements(e),m}function ln(e){He=!e,An(H,"Visibility changed")}const un=e=>{const t=new Set;for(const n of e){t.add(n);for(const e of bn(n))t.add(e)}return he("Inspecting:\n",t),t},dn=e=>{if(0===e.size)return;we("addObservers");const t=un(e);f.attachObservers(t),m.attachObserverToNonStaticElements(t),ye()},fn=e=>{if(0===e.size)return;we("removeObservers");const t=un(e);f.detachObservers(t),m.detachObservers(t),ye()};function mn(e){!function({addedNodes:e,removedNodes:t}){we("contentMutated"),Pt(),$t(),on(),ye(),fn(t),dn(e)}(e),An(V,"Mutation Observed")}function pn(){const e=bn(document.documentElement);var t;t=[ut(mn),an(e),kt(),cn(e),qt(ln)],s.push(...t.map(e=>e.disconnect))}function hn(e){performance.mark(bt);const t=ae(e);let n=1,o=document.documentElement,r=Pe?0:document.documentElement.getBoundingClientRect().bottom;const i=Pe?ot:Ie?Array.from(Xe):bn(document.documentElement);for(const t of i)n=t.getBoundingClientRect()[e]+parseFloat(getComputedStyle(t).getPropertyValue(`margin-${e}`)),n>r&&(r=n,o=t);return he(`${t} position calculated from:`,o),he(`Checked %c${i.length}%c elements`,h,g),performance.mark(vt,{detail:{hasTags:Pe,len:i.length,logging:Je,Side:t}}),r}const gn=e=>[e.bodyOffset(),e.bodyScroll(),e.documentElementOffset(),e.documentElementScroll(),e.boundingClientRect()],yn=`* ${Array.from(Z).map(e=>`:not(${e})`).join("")}`,bn=e=>e.querySelectorAll(yn),vn={height:0,width:0},wn={height:0,width:0},zn=[h,g,h];function $n(e){function t(){return wn[o]=r,vn[o]=s,Math.max(r,1)}const n=e===Sn,o=e.label,r=e.boundingClientRect(),i=Math.ceil(r),a=Math.floor(r),s=(e=>e.documentElementScroll()+Math.max(0,e.getOffset()))(e),c=`HTML: %c${r}px %cPage: %c${s}px`;let l=0;switch(!0){case!e.enabled():return Math.max(s,1);case Pe:he("Found element with data-iframe-size attribute"),l=e.taggedElement();break;case!Ie&&Ae&&0===wn[o]&&0===vn[o]:he(`Initial page size values: ${c}`,...zn),l=t();break;case st&&r===wn[o]&&s===vn[o]:he(`Size unchanged: ${c}`,...zn),l=Math.max(r,s);break;case 0===r&&0!==s:he(`Page is hidden: ${c}`,...zn),l=s;break;case!Ie&&r!==wn[o]&&s<=vn[o]:he(`New <html> size: ${c} `,...zn),he(`Previous <html> size: %c${wn[o]}px`,h),l=t();break;case!n:l=e.taggedElement();break;case!Ie&&r<wn[o]:he(`<html> size decreased: ${c}`,...zn),l=t();break;case s===a||s===i:he(`<html> size equals page size: ${c}`,...zn),l=t();break;case r>s:he(`Page size < <html> size: ${c}`,...zn),l=t();break;case Ie:he("Found elements possibly overflowing <html> "),l=e.taggedElement();break;default:he(`Using <html> size: ${c}`,...zn),l=t()}return he(`Content ${o}: %c${l}px`,h),l+=function(e){const t=e.getOffset();return 0!==t&&he(`Page offsetSize: %c${t}px`,h),t}(e),l}const Sn={label:T,enabled:()=>Me,getOffset:()=>Ge,auto:()=>$n(Sn),bodyOffset:()=>{const{body:e}=document,t=getComputedStyle(e);return e.offsetHeight+parseInt(t.marginTop,M)+parseInt(t.marginBottom,M)},bodyScroll:()=>document.body.scrollHeight,offset:()=>Sn.bodyOffset(),custom:()=>o.height(),documentElementOffset:()=>document.documentElement.offsetHeight,documentElementScroll:()=>document.documentElement.scrollHeight,boundingClientRect:()=>Math.max(document.documentElement.getBoundingClientRect().bottom,document.body.getBoundingClientRect().bottom),max:()=>Math.max(...gn(Sn)),min:()=>Math.min(...gn(Sn)),grow:()=>Sn.max(),lowestElement:()=>hn(I),taggedElement:()=>hn(I)},On={label:A,enabled:()=>Te,getOffset:()=>Ye,auto:()=>$n(On),bodyScroll:()=>document.body.scrollWidth,bodyOffset:()=>document.body.offsetWidth,custom:()=>o.width(),documentElementScroll:()=>document.documentElement.scrollWidth,documentElementOffset:()=>document.documentElement.offsetWidth,boundingClientRect:()=>Math.max(document.documentElement.getBoundingClientRect().right,document.body.getBoundingClientRect().right),max:()=>Math.max(...gn(On)),min:()=>Math.min(...gn(On)),rightMostElement:()=>hn(N),scroll:()=>Math.max(On.bodyScroll(),On.documentElementScroll()),taggedElement:()=>hn(N)},En=(e,t)=>!(Math.abs(e-t)<=at);function Mn(e,t){const n=e[t](),o=e.enabled()&&void 0!==_?function(e){const t=_(e);if(void 0===t)throw new TypeError("No value returned from onBeforeResize(), expected a numeric value");if(Number.isNaN(t))throw new TypeError(`Invalid value returned from onBeforeResize(): ${t}, expected Number`);if(t<1)throw new RangeError(`Out of range value returned from onBeforeResize(): ${t}, must be at least 1`);return t}(n):n;return ge(o>=1,`New iframe ${e.label} is too small: ${o}, must be at least 1`),o}let kn=!1;const jn=oe(()=>Ee(ee(4)));let xn,Tn=!1;const An=ve((e,t,n,o,r)=>{switch(we(e),!0){case!0===He:if(!0===Tn)break;Tn=!0,kn=!1,cancelAnimationFrame(xn);break;case!0===kn&&e!==W:$e();break;case!Y&&!(e in L):he("Resizing disabled");break;default:Tn=!1,kn=!0,performance.now(),xn=requestAnimationFrame(()=>{kn=!1,we("requestAnimationFrame")}),function(e,t,n,o,r){const i=n??Mn(Sn,qe),a=o??Mn(On,lt);switch(Me&&En(Re,i)||Te&&En(ct,a)?R:e){case w:case P:case R:Re=i,ct=a;case D:In(Re,ct,e,r);break;case W:case V:case F:case H:$e();break;default:$e(),he("No change in content size detected")}}(e,0,n,o,r)}ye()});function Cn(e){Re=Sn[qe](),ct=On[lt](),Nn(Re,ct,e)}function In(e,t,o,r,i){_e<-1||(void 0!==i||(i=it),function(){const a=`${Ke}:${e}:${t}:${o}${void 0===r?"":`:${r}`}`;if(tt)try{window.parent.iframeParentListener(U+a)}catch(e){if(1!==_e)throw e;return void jn()}else rt.postMessage(U+a,i);he(`Sending message to parent page via ${tt?"sameOrigin":"postMessage"}: %c%c${a}`,n,h)}())}const Nn=ve((e,t,n,o,r)=>{we(n),In(e,t,n,o,r),ye()}),Pn=ve(function(e){we("onMessage");const{freeze:t}=Object,{parse:n}=JSON,o=e=>Nn(0,0,`${e}Stop`),r={init:function(){if("loading"===document.readyState)return;const t=e.data.slice(13).split(":");rt=e.source,d=e.origin,gt(t),Ae=!1,setTimeout(()=>{We=!1},128)},reset(){We||Cn("resetPage")},resize(){An(q,"Parent window requested size check")},moveToAnchor(){Ve.findTarget(a())},inPageLink(){this.moveToAnchor()},pageInfo(){const e=a();pt?ne(pt,n(e)):o($)},parentInfo(){const e=(r=a(),t(n(r)));var r;ht?ne(ht,e):o(S)},message(){const e=a();ne(ft,n(e))}},i=()=>e.data.split("]")[1].split(":")[0],a=()=>e.data.slice(e.data.indexOf(":")+1),s=()=>e.data.split(":")[2]in{true:1,false:1};function c(){const t=i();we(t),t in r?r[t]():"iframeResize"in window||void 0!==window.jQuery&&""in window.jQuery.prototype||s()||Se(`Unexpected message (${e.data})`)}U===`${e.data}`.slice(0,13)&&function(){if(!1!==Ae)return s()?(ze(i()),we(w),void r.init()):void 0;c()}()});let Rn=!1;const Bn=e=>e.postMessage("[iFrameResizerChild]Ready",window?.iframeResizer?.targetOrigin||"*");function qn(){if("loading"===document.readyState||!Ae||Rn)return;const{parent:e,top:t}=window;we("ready"),Bn(e),e!==t&&Bn(t),Rn=!0}"iframeChildListener"in window?Se("Already setup"):(window.iframeChildListener=e=>setTimeout(()=>Pn({data:e,sameOrigin:!0})),J(window,z,Pn),J(document,C,qn),setTimeout(qn))}()});
