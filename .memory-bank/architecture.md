# Architecture

## System Overview

1nData is a multi-tenant clinical research management platform built on a modern web stack. The architecture supports isolated tenant data, regional database separation, and comprehensive API-driven interactions.

## Multi-Tenancy Architecture

### Database Strategy

- **Approach**: Database-per-tenant isolation using Stancl's Tenancy package
- **Central Database**: Stores tenant metadata, users, permissions, and shared configuration
- **Tenant Databases**: Each tenant has an isolated database with prefix `tenant_core{tenant_id}`
- **Regional Separation**: Separate database clusters for US, UK, and EU regions

### Domain Strategy

- **Domain-based Identification**: Each tenant accesses via subdomain (e.g., `client.app.1ndata.com`)
- **Central Domains**: Admin and documentation portals on main domain
- **Document Portal**: Special tenant for internal documentation

## Application Architecture

### Backend (Laravel)

```
app/
├── ApiDataProvider/          # Data access layer for frontend
│   ├── Charts/              # Chart data providers
│   ├── Metrics/             # Metrics aggregation
│   └── Tables/              # Table data providers
├── Http/Controllers/         # Request handling
├── Models/                   # Eloquent models
│   ├── Central*/            # Central database models
│   └── Tenant models        # Per-tenant models
├── Modules/                  # Feature modules
│   ├── Automation/          # Workflow automation
│   ├── DocumentPortal/      # Document management
│   ├── EmbedForm/          # Public form embedding
│   ├── FormBuilder/        # Dynamic form creation
│   ├── ImportWizard/       # CSV data import
│   ├── MessageEditor/      # Email/SMS templates
│   ├── ReactEmail/         # React-based emails
│   └── Zoom/               # Video integration
└── Jobs/                    # Background processing
```

### Frontend (React + Inertia.js)

```
resources/js/
├── Pages/                   # Inertia page components
│   ├── Modules/            # Module-specific pages
│   ├── *Dashboard/         # Various dashboards
│   └── [Entity]*/          # CRUD pages
├── Components/             # Reusable components
├── Contexts/               # React contexts
├── Hooks/                  # Custom hooks
├── Store/                  # Easy-peasy state
└── Utils/                  # Utilities
```

## Key Design Patterns

### API Data Providers

- **Purpose**: Centralized data access layer between backend and frontend
- **Types**: Tables, Charts, Metrics
- **Features**: Filtering, sorting, aggregation, permissions

### Module System

- **Self-contained Features**: Each module has its own controllers, models, routes
- **Isolation**: Modules can be enabled/disabled per tenant
- **Examples**: FormBuilder, Automation, DocumentPortal

### Event-Driven Architecture

- **Activity Logging**: All changes tracked via Laravel-activitylog
- **Campaign Events**: Trigger automations based on patient actions
- **Queue Processing**: Laravel Horizon for background jobs

## Technology Stack Details

### Backend Technologies

- **Framework**: Laravel 12.x
- **PHP Version**: 8.4+
- **Queue System**: Redis + Laravel Horizon
- **Cache**: Redis with tenant-aware prefixing
- **File Storage**: Azure Blob Storage
- **Authentication**: Laravel Fortify + OAuth 2.0

### Frontend Technologies

- **UI Framework**: React 19 with TypeScript
- **Routing**: Inertia.js for SPA-like experience
- **State Management**: Easy-peasy
- **UI Components**: Tailwind CSS + ShadCN/ui
- **Rich Text**: TipTap editor
- **Data Grid**: AG-Grid Enterprise
- **Charts**: Highcharts, Nivo, Recharts

### Infrastructure

- **Databases**: MySQL 8.0 (multi-region)
- **Queue/Cache**: Redis
- **File Storage**: Azure Blob Storage
- **Email**: Postmark
- **SMS**: Twilio
- **Monitoring**: Sentry, Laravel Telescope

## Security Architecture

### Authentication & Authorization

- **Multi-factor Authentication**: Via Laravel Fortify
- **Role-Based Access Control**: Spatie Laravel-permission
- **API Authentication**: Laravel Passport
- **SSO Support**: OpenID Connect

### Data Security

- **Encryption**: Database field encryption for sensitive data
- **Tenant Isolation**: Complete database separation
- **Audit Trails**: Comprehensive activity logging
- **GDPR Compliance**: Regional data separation

## Integration Architecture

### Marketing Platforms

- Facebook Ads API
- Google Ads API
- Pinterest, Snapchat, Reddit, Quora APIs

### Communication Systems

- Zoom Contact Center
- Twilio SMS
- Calendly scheduling
- Postmark email

### Clinical Systems

- Obvio Health
- Castor EDC
- Custom API integrations

## Performance Considerations

### Caching Strategy

- **Redis Caching**: Tenant-aware cache prefixing
- **Query Caching**: For expensive aggregations
- **API Response Caching**: For dashboard metrics

### Queue Processing

- **Horizon Configuration**: Multiple queues by priority
- **Job Batching**: For bulk operations
- **Rate Limiting**: For external API calls

### Database Optimization

- **Indexing**: Comprehensive indexes on search/filter columns
- **Aggregation Tables**: Pre-computed metrics for dashboards
- **Archive Tables**: Historical data separation
