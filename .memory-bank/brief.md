# 1nData Core Laravel Tenancy Platform

## Project Overview

1nData is a comprehensive multi-tenant clinical research management platform built by 1nHealth, designed to streamline patient recruitment, data collection, and study management for clinical research organizations. The platform serves as a centralized hub for managing clinical trials, patient leads, and research workflows across multiple geographic regions.

## Main Objectives

- **Patient Recruitment Optimization**: Automate and enhance patient recruitment processes for clinical trials
- **Multi-Tenant Architecture**: Support multiple clients with isolated data and customizable workflows
- **Data Integration**: Seamlessly integrate with various marketing platforms and research tools
- **Workflow Automation**: Reduce manual processes through intelligent automation systems
- **Global Compliance**: Support multi-regional operations with US, UK, and EU data centers

## Key Features

### Core Modules

- **FormBuilder**: Dynamic form creation and management for patient screening
- **ImportWizard**: CSV data import with validation and mapping capabilities
- **DocumentPortal**: Centralized document management and distribution system
- **EmbedForm**: Public form embedding for patient recruitment
- **Automation**: Intelligent workflow automation for lead processing
- **MessageEditor**: Rich text editor for communication templates
- **ReactEmail**: Email template management and delivery system
- **Analytics & Visualization**: Comprehensive data visualization and reporting system including:
    - **Lead Dashboard**: Real-time charts for qualified/disqualified leads, lead stages, contact priorities, and disqualification reasons
    - **CPT Reporting**: Cost-per-trial analytics with metrics by site, ad platform, UTM tracking, gross profit projections, and budget analysis
    - **Study KPI Dashboard**: Key performance indicators with advanced filtering and date-based trend analysis
    - **Lead Funnel Analysis**: Conversion funnel visualization from initial contact to signed ICF (Informed Consent Form)
    - **Feasibility Reporting**: Comparative analysis and date-based metrics for study planning and execution

### Platform Integrations

- **Marketing Platforms**: Facebook, Google Ads, Pinterest, Snapchat, Reddit, Quora
- **Communication Tools**: Zoom Contact Center, Calendly, Twilio SMS
- **Research Systems**: Obvio, Castor EDC, various clinical trial platforms
- **File Storage**: Azure Blob Storage for document management

### Advanced Capabilities

- **Lead Management**: Comprehensive patient lead tracking and status workflows
- **Multi-Database Architecture**: Separate databases for US, UK, and EU regions
- **API Data Providers**: Robust data access layer with filtering and aggregation
- **Activity Logging**: Complete audit trail for compliance and monitoring
- **Role-Based Access Control**: Granular permissions system for different user types

## Technology Stack

### Backend

- **Framework**: Laravel 12 with PHP 8.4+
- **Database**: MySQL with multi-tenant architecture
- **Authentication**: Laravel Fortify with OAuth 2.0 support
- **Queue System**: Laravel Horizon for job processing
- **API Layer**: Inertia.js for seamless frontend integration

### Frontend

- **Framework**: React 19 with TypeScript
- **UI Library**: Tailwind CSS with ShadCN components
- **Rich Text Editor**: TipTap with custom extensions
- **State Management**: React hooks with context providers
- **Build Tool**: Vite for fast development and building

### Testing & DevOps

- **E2E Testing**: Playwright for comprehensive browser testing
- **Unit Testing**: PHPUnit for backend logic validation
- **Containerization**: Docker for consistent development environments
- **CI/CD**: Automated testing and deployment pipelines

## Architecture Highlights

- **Multi-Tenancy**: Supports isolated tenant databases with shared codebase
- **Modular Design**: Extensible module system for feature additions
- **Event-Driven**: Comprehensive event system for automation and integrations
- **Queue-Based Processing**: Asynchronous job processing for scalability
- **API-First**: RESTful API design with comprehensive data providers

## Significance

This platform represents a sophisticated solution for clinical research organizations, addressing the complex challenges of patient recruitment, data management, and regulatory compliance in the healthcare industry. By automating manual processes and providing deep integrations with marketing and research tools, it enables research teams to focus on advancing medical discoveries rather than administrative tasks.

The multi-tenant architecture allows the platform to serve multiple clients while maintaining strict data isolation and customization capabilities, making it suitable for enterprise-level clinical research operations across different geographic regions and regulatory environments.
