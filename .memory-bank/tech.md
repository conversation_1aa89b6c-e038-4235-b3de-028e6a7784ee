# Technology Stack

## Backend Stack

### Core Technologies

- **PHP**: 8.4+
- **Framework**: <PERSON><PERSON> 12.x
- **Database**: MySQL 8.0
- **Multi-tenancy**: Stancl/Tenancy (dev-master)

### Key Laravel Packages

- **Authentication**: <PERSON>vel Fortify, Laravel Passport
- **Authorization**: <PERSON><PERSON>-permission
- **Activity Logging**: <PERSON><PERSON>activitylog
- **Queue Management**: Laravel Horizon
- **API Documentation**: Laravel Telescope
- **File Storage**: Laravel Azure Storage

### External Services

- **Cache/Queue**: Redis
- **File Storage**: Azure Blob Storage
- **Email**: Postmark
- **SMS**: Twilio
- **Error Tracking**: Sentry
- **Real-time**: Pusher

## Frontend Stack

### Core Technologies

- **React**: 19.1.0
- **TypeScript**: 5.1.6
- **Build Tool**: Vite 6.x
- **SSR/Routing**: Inertia.js

### UI Framework

- **CSS Framework**: Tailwind CSS 4.x
- **Component Library**: ShadCN/ui (Radix UI based)
- **Icons**: Lucide React, FontAwesome Pro
- **Animations**: Framer Motion

### Data Management

- **State Management**: Easy-peasy 6.x
- **Data Fetching**: React Query (TanStack Query)
- **Form Handling**: React Hook Form
- **Validation**: Zod, Valibot

### Rich Content

- **Text Editor**: TipTap 2.x
- **Data Grid**: AG-Grid Enterprise
- **Charts**: Highcharts, Nivo, Recharts
- **Diagrams**: Mermaid, XYFlow

### Development Tools

- **Linting**: ESLint with Airbnb config
- **Formatting**: Prettier
- **Git Hooks**: Husky with lint-staged
- **Testing**: Playwright for E2E tests

## Development Environment

### Docker Setup

- **Profiles**: local, testing, playwright
- **Services**:
    - Laravel application container
    - MySQL containers (main, UK, EU)
    - Redis container
    - Mailpit for local email testing

### Local Development Commands

```bash
# Start development environment
npm run docker:up

# Run frontend dev server
npm run dev

# Run tests
npm run test:docker:up
npm run pw:test

# Code quality
npm run lint
npm run ts-lint
```

### Environment Configuration

- **Central Domains**: Configurable via CENTRAL_DOMAINS env
- **Multi-region Databases**: Separate configs for US, UK, EU
- **File Storage**: Azure containers for different environments
- **Feature Flags**: Environment-based feature toggles

## API Architecture

### Data Provider Pattern

- **TableApiDataProvider**: For paginated table data
- **ChartApiDataProvider**: For chart visualization data
- **MetricsApiDataProvider**: For aggregated metrics

### Module Structure

Each module contains:

- Controllers
- Models
- Routes
- Http Resources
- Data Transfer Objects
- Jobs/Commands
- Event Listeners

## Testing Infrastructure

### E2E Testing

- **Framework**: Playwright
- **Visual Regression**: Playwright visual tests
- **Test Organization**: Functional and visual test suites

### PHP Testing

- **Unit Tests**: PHPUnit
- **Database**: Separate test database
- **Factories**: Laravel factories for test data

## Build & Deployment

### Frontend Build

- Multiple Vite configs for different entry points:
    - Main application
    - Embed forms
    - Calendar widget

### Asset Management

- **CSS**: PostCSS with nesting
- **JS/TS**: Vite bundling with code splitting
- **Fonts**: Self-hosted FontAwesome Pro

### Performance Optimizations

- **Code Splitting**: Route-based lazy loading
- **Asset Optimization**: Vite minification and compression
- **Caching**: Redis-based caching with tenant isolation
- **Queue Processing**: Horizon for background jobs

## Security Measures

### Frontend Security

- **CSP Headers**: Content Security Policy
- **XSS Protection**: React's built-in escaping
- **CSRF**: Laravel CSRF tokens via Inertia

### Backend Security

- **Input Validation**: Laravel validation rules
- **SQL Injection**: Eloquent ORM protection
- **Rate Limiting**: API rate limiting
- **Encryption**: Field-level encryption for sensitive data

## Monitoring & Debugging

### Development Tools

- **Laravel Telescope**: API request monitoring
- **React DevTools**: Component debugging
- **Redux DevTools**: Easy-peasy state debugging

### Production Monitoring

- **Error Tracking**: Sentry integration
- **Performance**: FullStory for user session recording
- **Analytics**: Custom event tracking
- **Logging**: Structured logging with context
