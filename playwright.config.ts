/* eslint-disable import/order */
/* eslint-disable no-underscore-dangle */
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

import { defineConfig, devices, ReporterDescription } from '@playwright/test';

const __filename = fileURLToPath(import.meta.url); // get the resolved path to the file
const dirname = path.dirname(__filename); // get the name of the directory

// Read from ".env" file.
dotenv.config({ path: path.resolve(dirname, '.env.playwright') });

const reporters: ReporterDescription[] = [['dot'], ['html']];

if (!process.env.CI) {
    reporters.push(['list']);
    reporters.push([
        'monocart-reporter',
        {
            name: 'My Test Report',
            outputFile: './monocart-report/index.html',
            // custom columns
            columns: (defaultColumns: any[]) => {
                defaultColumns.push({
                    id: 'tenantUrl',
                    name: 'Tenant URL',
                    width: 200,
                });

                defaultColumns.push({
                    id: 'executionStartTime',
                    name: 'Execution Start Time',
                    width: 200,
                });

                defaultColumns.push({
                    id: 'executionEndTime',
                    name: 'Execution End Time',
                    width: 200,
                });

                defaultColumns.push({
                    id: 'parallelIndex',
                    name: 'Parallel Index',
                    width: 200,
                });

                defaultColumns.push({
                    id: 'workerIndex',
                    name: 'Worker Index',
                    width: 200,
                });

                return defaultColumns;
            },
        },
    ]);
}

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
    // timeout: 120_000,
    testDir: './tests/Playwright/e2e',
    /* Run tests in files in parallel */
    fullyParallel: true,
    /* Fail the build on CI if you accidentally left test.only in the source code. */
    forbidOnly: !!process.env.CI,
    /* Retry on CI only */
    retries: process.env.CI ? 0 : 0,
    /* Configure parallel workers */
    workers: process.env.CI ? 1 : 1,
    /* Reporter to use. See https://playwright.dev/docs/test-reporters */
    reporter: reporters,
    /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
    use: {
        /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
        trace: 'retain-on-failure',
        testIdAttribute: 'data-testid',
        screenshot: 'only-on-failure',
        video: 'retain-on-failure',
    },

    /* Configure snapshot settings for visual regression */
    expect: {
        /**
         * Maximum time expect() should wait for the condition to be met.
         * For example in `await expect(locator).toHaveText();`
         */
        timeout: 10000, // Default is 5000, increase if needed for rendering

        toHaveScreenshot: {
            /**
             * An acceptable ratio of pixels that are different to the
             * total amount of pixels, between 0 and 1. Default is 0.
             * Start potentially higher (e.g., 0.05) and tune down.
             */
            maxDiffPixelRatio: 0.05,
            /**
             * An acceptable amount of pixels that could be different. Can be used
             * instead of, or in addition to, maxDiffPixelRatio.
             */
            maxDiffPixels: 20,
            /**
             * Default threshold for pixel-level comparisons (0-1).
             * Lower is stricter. Start higher (e.g., 0.2) if needed, then refine.
             */
            threshold: 0.2,
            /**
             * Specify animations behavior. Prefer 'disabled' for stability.
             */
            animations: 'disabled',
            /**
             * Specify caret behavior. Prefer 'hide' for stability.
             */
            caret: 'hide',
            /**
             * Styles for snapshots
             */
            stylePath: './tests/Playwright/e2e/visual/snapshot.css',
        },
    },

    /* Configure projects for major browsers */
    projects: [
        {
            name: 'setup-global-test-environment',
            testMatch: '**/global.setup.ts',
        },

        // --- Visual Setup Project ---
        {
            name: 'visual-setup',
            testMatch: '**/visual.setup.ts',
            /**
             * dependencies ensures that the global test environment is fully set up
             * before running the visual database seeding. This guarantees a consistent
             * and reliable state for all visual tests, and prevents issues from running
             * visual setup in an uninitialized environment.
             */
            dependencies: ['setup-global-test-environment'],
            teardown: 'visual-teardown',
        },
        {
            name: 'visual-teardown',
            testMatch: '**/visual.teardown.ts',
            // dependencies: ['visual-setup'],
        },

        // --- Functional E2E Project ---
        {
            name: 'functional-chromium',
            use: {
                ...devices['Desktop Chrome'],
                deviceScaleFactor: undefined,
                viewport: null,
                launchOptions: {
                    args: ['--start-maximized'],
                },
            },
            testDir: './tests/Playwright/e2e/specs', // Option 1: Point only to specs
            dependencies: ['setup-global-test-environment'],
        },

        // --- Visual Regression Project ---
        {
            name: 'visual-regression',
            use: {
                ...devices['Desktop Chrome'],
                deviceScaleFactor: undefined,
                viewport: { width: 1600, height: 900 },
            },
            // Point *only* to the visual test directory
            testDir: './tests/Playwright/e2e/visual',
            snapshotDir: './tests/Playwright/e2e/visual/snapshots',
            /**
             * dependencies ensures that both the global and visual setup steps are
             * completed before running visual regression tests. This guarantees all
             * required data and environment state are present for reliable visual testing.
             */
            dependencies: ['setup-global-test-environment', 'visual-setup'],
        },
    ],
});
