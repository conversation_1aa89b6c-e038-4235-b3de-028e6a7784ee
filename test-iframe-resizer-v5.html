<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test iframe-resizer v5 Upgrade - FIXED</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        .instructions {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .embed-container {
            border: 1px solid #ccc;
            padding: 10px;
            margin: 10px 0;
            min-height: 200px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🧪 iframe-resizer v5 Testing Guide - FIXED SIZING ISSUES</h1>
    
    <div class="status success">
        <h3>✅ v5 Configuration Applied!</h3>
        <p><strong>Fixed the sizing issues with these changes:</strong></p>
        <ul>
            <li><code>direction: 'vertical'</code> - Replaces deprecated sizeHeight</li>
            <li><code>log: true</code> - Debug logging enabled</li>
            <li><code>offsetSize: 0</code> - Size adjustment option</li>
            <li>Event handlers for debugging</li>
        </ul>
    </div>
    
    <div class="instructions">
        <h3>📋 Testing Steps:</h3>
        <ol>
            <li>Make sure your Laravel app is running (php artisan serve)</li>
            <li>Replace <code>YOUR_DOMAIN</code> below with your actual domain (e.g., localhost:8000)</li>
            <li>Replace <code>FORM_ID</code> with an actual form public ID from your database</li>
            <li><strong>Open browser console (F12) to see iframe-resizer debug messages</strong></li>
            <li>Watch for "iframe-resizer ready" and "iframe resized" messages</li>
        </ol>
    </div>

    <!-- Test 1: Embedded Form -->
    <div class="test-section">
        <h2>🔧 Test 1: Embedded Form (UPDATED)</h2>
        <div class="instructions">
            <p><strong>What changed:</strong> Now uses <code>direction: 'vertical'</code> instead of deprecated options</p>
            <p><strong>Expected behavior:</strong> Better height calculation and automatic resizing</p>
        </div>
        
        <h3>Step 1: Get a Form ID</h3>
        <pre><code>php artisan tinker
App\Models\CampaignForm::whereNotNull('form_public')->first()?->form_public</code></pre>
        
        <h3>Step 2: Test the Embed</h3>
        <p>Replace <code>YOUR_DOMAIN</code> and <code>FORM_ID</code>:</p>
        <div class="embed-container">
            <!-- Replace YOUR_DOMAIN and FORM_ID -->
            <script id='1ndata-embed' src='http://YOUR_DOMAIN/embed.js' data-form-id='FORM_ID'></script>
        </div>
        
        <div class="status info">
            <strong>🔍 Debug Console Messages to Look For:</strong>
            <ul>
                <li>"iframe-resizer ready" - Initialization successful</li>
                <li>"iframe resized: {height: X, width: Y}" - Resize events</li>
                <li>No deprecation warnings about sizeHeight/sizeWidth</li>
            </ul>
        </div>
    </div>

    <!-- Test 2: Direct URL Testing -->
    <div class="test-section">
        <h2>🌐 Test 2: Direct URL Testing</h2>
        <div class="instructions">
            <p><strong>Quick test method:</strong> Test the embed pages directly</p>
        </div>
        
        <h3>Test URLs:</h3>
        <ul>
            <li><strong>Form:</strong> <code>http://YOUR_DOMAIN/embed-form/FORM_ID</code></li>
            <li><strong>Test Form:</strong> <code>http://YOUR_DOMAIN/admin/embed-form/test-forms</code></li>
        </ul>
        
        <div class="status warning">
            <strong>✅ What to check:</strong>
            <ul>
                <li>Forms load without being cut off</li>
                <li>Height adjusts properly when interacting with form elements</li>
                <li>Console shows iframe-resizer debug messages</li>
                <li>No red errors in console</li>
            </ul>
        </div>
    </div>

    <!-- Test 3: Troubleshooting -->
    <div class="test-section">
        <h2>🔧 Test 3: Troubleshooting</h2>
        
        <div class="status error">
            <h4>❌ If still having sizing issues:</h4>
            <ol>
                <li><strong>Check console for errors</strong> - Look for license, script loading, or calculation errors</li>
                <li><strong>Try different offsetSize values</strong> - Add padding: <code>offsetSize: 20</code></li>
                <li><strong>Check CSS conflicts</strong> - Ensure iframe container doesn't have fixed heights</li>
                <li><strong>Verify child script loading</strong> - Check if <code>/lib/iframe-resizer/iframeResizer.contentWindow.min.js</code> loads</li>
            </ol>
        </div>
        
        <div class="status success">
            <h4>✅ If working correctly, you should see:</h4>
            <ul>
                <li>Forms display at full height without scrollbars</li>
                <li>Height adjusts when form content changes</li>
                <li>Console shows successful initialization and resize events</li>
                <li>No deprecation warnings</li>
            </ul>
        </div>
    </div>

    <script>
        console.log('🧪 iframe-resizer v5 Test Page Loaded');
        console.log('📝 Key Changes Made:');
        console.log('- direction: "vertical" (replaces sizeHeight: true)');
        console.log('- log: true (for debugging)');
        console.log('- Added onReady and onResized event handlers');
        console.log('- offsetSize: 0 (adjustable for fine-tuning)');
        console.log('');
        console.log('🔍 Watch this console for iframe-resizer messages when testing!');
    </script>
</body>
</html>
