{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.4", "ext-intl": "*", "ext-json": "*", "ext-pdo": "*", "ashallendesign/short-url": "^8.3", "cmixin/business-day": "^1.16", "cmixin/business-time": "^1.12", "cviebrock/eloquent-sluggable": "^12.0", "facebook/php-business-sdk": "*", "firebase/php-jwt": "^6.10", "gmostafa/php-graphql-client": "1.13", "guzzlehttp/guzzle": "^7.2", "hidehalo/nanoid-php": "*", "inertiajs/inertia-laravel": "^2.0", "ipdata/api-client": "^0.1.1", "jeremy379/laravel-openid-connect": "^2.7.0", "justinrainbow/json-schema": "*", "kirschbaum-development/eloquent-power-joins": "^4.2", "laravel/fortify": "^1.17", "laravel/framework": "^12.0", "laravel/horizon": "^v5.31", "laravel/nightwatch": "^1.7", "laravel/passport": "^v12.4", "laravel/serializable-closure": "*", "laravel/telescope": "^v5.6.0", "laravel/tinker": "^v2.10.1", "league/html-to-markdown": "^5.1", "matthewbdaly/laravel-azure-storage": "*", "opcodesio/log-viewer": "^3.0", "owen-it/laravel-auditing": "^14.0", "propaganistas/laravel-phone": "^6.0", "pusher/pusher-php-server": "*", "rap2hpoutre/fast-excel": "*", "romanzipp/laravel-queue-monitor": "^5.4", "saloonphp/laravel-http-sender": "^3.0", "saloonphp/laravel-plugin": "^3.4", "saloonphp/pagination-plugin": "^2.2", "saloonphp/saloon": "^3.7", "sentry/sentry-laravel": "*", "shiftonelabs/laravel-cascade-deletes": "^2.0", "spatie/guzzle-rate-limiter-middleware": "*", "spatie/laravel-activitylog": "^4.7", "spatie/laravel-data": "^4.11", "spatie/laravel-enum": "^3.0", "spatie/laravel-permission": "^6.16", "spatie/laravel-schemaless-attributes": "^2.4", "spatie/laravel-webhook-client": "^3.2", "stancl/tenancy": "dev-master", "staudenmeir/eloquent-json-relations": "^v1.14", "symfony/http-client": "*", "symfony/postmark-mailer": "*", "tightenco/ziggy": "^2.3", "twilio/sdk": "*", "whichbrowser/parser": "^2.1", "wildbit/postmark-php": "*"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.9", "barryvdh/laravel-ide-helper": "^3.6", "fakerphp/faker": "^1.9.1", "friendsofphp/php-cs-fixer": "^3.86", "jasonmccreary/laravel-test-assertions": "^2.8.0", "kitloong/laravel-migrations-generator": "^7.0", "laravel/dusk": "^8.0", "laravel/pint": "^1.10", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^v8.0", "pestphp/pest": "^4.0.0", "pestphp/pest-plugin-browser": "^4.0", "pestphp/pest-plugin-faker": "^4.0", "pestphp/pest-plugin-laravel": "^4.0", "pestphp/pest-plugin-type-coverage": "^4.0", "reliese/laravel": "^1.2", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/array.php", "app/Helpers/debug.php", "app/Helpers/json.php", "app/Helpers/routes.php", "app/Helpers/string.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": ["laravel/passport"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}