services:
  # Laravel App
  core_laravel_tenancy:
    build:
      context: ./
      dockerfile: .docker/Dockerfile
    image: 1nd_core_app
    container_name: 1nd_core_app
    restart: unless-stopped
    working_dir: /var/www/html
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    ports:
      - 9090:80
      - 9003:9003
    profiles:
      - local
    volumes:
      - ./:/var/www/html
    networks:
      - landing
    depends_on:
      - core_db
      - core_eu_db
      - core_uk_db
      - core_redis

  # Mysql Services
  core_db:
    image: mysql:8.0.31
    restart: unless-stopped
    container_name: 1nd_core_app_db
    ports:
      - "8895:3306"
    profiles:
      - local
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ALLOW_EMPTY_PASSWORD: 1
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_TCP_PORT: 3306
    expose:
      # Opens database port on the container to other containers in this file.
      - 3306
    volumes:
      - 1nh_ap_db:/var/lib/mysql
      - ./.mysql:/etc/mysql/conf.d:ro
    networks:
      - landing

  # Mysql Services (UK)
  core_uk_db:
    image: mysql:8.0.31
    restart: unless-stopped
    container_name: 1nd_core_app_uk_db
    ports:
      - "8898:3306"
    profiles:
      - local
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ALLOW_EMPTY_PASSWORD: 1
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_TCP_PORT: 3306
    depends_on:
      - core_db
    expose:
      # Opens database port on the container to other containers in this file.
      - 3306
    volumes:
      - uk_app_db:/var/lib/mysql
      - ./.mysql:/etc/mysql/conf.d:ro
    networks:
      - landing

  # Mysql Services (UK)
  core_eu_db:
    image: mysql:8.0.31
    restart: unless-stopped
    container_name: 1nd_core_app_eu_db
    ports:
      - "8901:3306"
    profiles:
      - local
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ALLOW_EMPTY_PASSWORD: 1
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_TCP_PORT: 3306
    depends_on:
      - core_db
    expose:
      # Opens database port on the container to other containers in this file.
      - 3306
    volumes:
      - eu_app_db:/var/lib/mysql
      - ./.mysql:/etc/mysql/conf.d:ro
    networks:
      - landing

  # Redis Services
  core_redis:
    image: redis:alpine
    container_name: 1nd_core_app_redis
    ports:
      - "6382:6379"
    profiles:
      - local
    networks:
      - landing

  mailpit:
    image: axllent/mailpit
    container_name: mailpit
    restart: unless-stopped
    volumes:
      - ./data:/data
    ports:
      - 8025:8025
      - 1025:1025
    profiles:
      - local
    environment:
      MP_MAX_MESSAGES: 5000
      MP_DATA_FILE: /data/mailpit.db
      MP_SMTP_AUTH_ACCEPT_ANY: 1
      MP_SMTP_AUTH_ALLOW_INSECURE: 1
    networks:
      - landing

  #######################################
  # Testing Services                    #
  #######################################

  test_core_laravel_tenancy:
    build:
      context: ./
      dockerfile: .docker/Dockerfile
    environment:
      APP_ENV: ${APP_ENV:-testing}
    image: test_1nd_core_app
    container_name: test_1nd_core_app
    restart: unless-stopped
    working_dir: /var/www/html
    extra_hosts:
      - 'host.docker.internal:host-gateway'
    ports:
      - 9091:80
      - 9092:9003
    profiles:
      - testing
    volumes:
      - ./:/var/www/html
    networks:
      - landing
    depends_on:
      - test_core_db
      - test_eu_db
      - test_uk_db
      - test_core_redis

  # Test Mysql Services
  test_core_db:
    image: mysql:8.0.31
    restart: unless-stopped
    container_name: test_1nd_core_app_db
    ports:
      - "8899:3306"
    profiles:
      - testing
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ALLOW_EMPTY_PASSWORD: 1
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_TCP_PORT: 3306
    expose:
      - 3306
    volumes:
      - ./.mysql:/etc/mysql/conf.d:ro
    networks:
      - landing

  # Test Mysql Services (UK)
  test_uk_db:
    image: mysql:8.0.31
    restart: unless-stopped
    container_name: test_1nd_core_app_uk_db
    ports:
      - "8900:3306"
    profiles:
      - testing
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ALLOW_EMPTY_PASSWORD: 1
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_TCP_PORT: 3306
    expose:
      - 3306
    volumes:
      - ./.mysql:/etc/mysql/conf.d:ro
    networks:
      - landing

  # Test Mysql Services (UK)
  test_eu_db:
    image: mysql:8.0.31
    restart: unless-stopped
    container_name: test_1nd_core_app_eu_db
    ports:
      - "8902:3306"
    profiles:
      - testing
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ALLOW_EMPTY_PASSWORD: 1
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_TCP_PORT: 3306
    expose:
      - 3306
    volumes:
      - ./.mysql:/etc/mysql/conf.d:ro
    networks:
      - landing

  test_core_redis:
    image: redis:alpine
    container_name: test_1nd_core_app_redis
    profiles:
      - testing
    ports:
      - "6383:6379"
    networks:
      - landing

networks:
  landing:
    driver: bridge
    name: 1ndata-network

volumes:
  1nh_ap_db:
  eu_app_db:
  uk_app_db:

