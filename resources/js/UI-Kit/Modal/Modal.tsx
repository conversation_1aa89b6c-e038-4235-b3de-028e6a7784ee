import { Fragment, PropsWithChildren, useId } from 'react';

import { twMerge } from 'tailwind-merge';

import { Dialog, Transition } from '@headlessui/react';

import ModalProvider, { useModalContext } from '@/Contexts/ModalContext';
import { cn } from '@/lib/utils';
import { ModalContent, Modal<PERSON>ooter, ModalHeader, ModalProps, ModalWrapper } from '@/UI-Kit/Modal/types';

function Modal({ isOpen = false, children }: PropsWithChildren<ModalProps>) {
    return <ModalProvider isOpen={isOpen}>{children}</ModalProvider>;
}

function Trigger({ children }: PropsWithChildren) {
    return children;
}

function Wrapper({ children, size = 'md', useBackdrop = true }: PropsWithChildren<ModalWrapper>) {
    const { showModal } = useModalContext();
    const modalId = useId();

    return (
        <Transition.Root show={showModal} as={Fragment}>
            <Dialog
                as="div"
                id="headless-modal"
                className="animate-fade-in fixed inset-0 z-30 overflow-y-auto"
                onClose={() => {}} // This prevents modal from closing on click outside
                key={modalId}
                aria-modal="true"
            >
                <div className="flex min-h-screen w-full items-end justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
                    <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0"
                        enterTo="opacity-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100"
                        leaveTo="opacity-0"
                    >
                        <div
                            className={cn('fixed inset-0 transition-opacity', {
                                'bg-black/50 backdrop-blur-xs': useBackdrop,
                            })}
                            aria-hidden="true"
                        />
                    </Transition.Child>

                    {/* This element is to trick the browser into centering the modal contents. */}
                    <span className="hidden sm:inline-block sm:h-screen sm:align-middle" aria-hidden="true">
                        &#8203;
                    </span>
                    <Transition.Child
                        as={Fragment}
                        enter="ease-out duration-300"
                        enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                        enterTo="opacity-100 translate-y-0 sm:scale-100"
                        leave="ease-in duration-200"
                        leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                        leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                    >
                        <div
                            className={cn(
                                // Removing overflow hidden to allow React Select to break out
                                'relative inline-block w-full transform rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:align-middle',
                                {
                                    'sm:max-w-sm': size === 'sm',
                                    'sm:max-w-md': size === 'md',
                                    'sm:max-w-lg': size === 'lg',
                                    'sm:max-w-xl': size === 'xl',
                                    'sm:max-w-2xl': size === '2xl',
                                    'sm:max-w-3xl': size === '3xl',
                                }
                            )}
                        >
                            {children}
                        </div>
                    </Transition.Child>
                </div>
            </Dialog>
        </Transition.Root>
    );
}

function Header({ className, titleClass, children }: PropsWithChildren<ModalHeader>) {
    return (
        <div className={twMerge('border-b px-4 py-3', className)}>
            <Dialog.Title as="h3" className={twMerge('mb-0 text-lg leading-6 font-semibold text-black', titleClass)}>
                {children}
            </Dialog.Title>
        </div>
    );
}

function Content({ className, children }: PropsWithChildren<ModalContent>) {
    return <div className={twMerge('p-4 sm:p-6', className)}>{children}</div>;
}

function Footer({ className, children }: PropsWithChildren<ModalFooter>) {
    return <div className={twMerge('border-t px-4 py-3', className)}>{children}</div>;
}

Modal.Trigger = Trigger;
Modal.Wrapper = Wrapper;
Modal.Header = Header;
Modal.Content = Content;
Modal.Footer = Footer;

export default Modal;
