/* eslint-disable react/button-has-type */
import { ButtonHTMLAttributes } from 'react';

/**
 * TODO: Replace usage with <PERSON><PERSON> from UI-Kit
 * @deprecated
 */
export default function SecondaryButton({
    type = 'button',
    className = '',
    disabled,
    children,
    ...props
}: ButtonHTMLAttributes<HTMLButtonElement>) {
    return (
        <button
            {...props}
            type={type}
            className={`inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-xs font-semibold tracking-widest text-gray-700 uppercase shadow-xs transition duration-150 ease-in-out hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none disabled:opacity-25 ${
                disabled && 'opacity-25'
            } ${className}`}
            disabled={disabled}
        >
            {children}
        </button>
    );
}
