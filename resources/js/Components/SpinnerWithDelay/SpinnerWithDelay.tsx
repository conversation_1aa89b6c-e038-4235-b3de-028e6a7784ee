import { useEffect, useState } from 'react';

import { useStoreState } from '@/store/hooks';
import Spinner from '@/UI-Kit/Spinner';

const SpinnerWithDelay = () => {
    const isAppLoading = useStoreState(state => state.appSettingsModel.isAppLoading);
    const appLoadingMessage = useStoreState(state => state.appSettingsModel.appLoadingMessage);
    const delay = 200; // 200ms
    const [showLoadingIndicator, setLoadingIndicatorVisibility] = useState(false);

    useEffect(() => {
        const timer = setTimeout(() => setLoadingIndicatorVisibility(true), delay);

        // this will clear Timeout when component unmount like in willComponentUnmount
        return () => {
            clearTimeout(timer);
        };
    });

    return showLoadingIndicator && isAppLoading ? <Spinner tip={appLoadingMessage ?? undefined} fullScreen /> : null;
};

export default SpinnerWithDelay;
