import { useMemo } from 'react';

import { v4 as uuid } from 'uuid';

import { marketingMessages } from './MarketingMessages';

const marketingImages = {
    peopleRunning: '/images/people-running.jpeg',
    peopleHighFive: '/images/people-high-five.jpeg',
    groupMeeting1: '/images/group-meeting-1.jpeg',
    groupMeeting2: '/images/group-meeting-2.jpeg',
    personOnPhone: '/images/person-on-phone.jpeg',
    personOnLaptop1: '/images/person-on-laptop-1.jpeg',
    personOnLaptop2: '/images/person-on-laptop-2.jpeg',
    coupleOnPhone: '/images/couple-on-phone.jpeg',
    personHoldBinoculars: '/images/person-hold-binoculars.jpeg',
    phoneCloseUp: '/images/phone-close-up.jpeg',
    threePeopleOnPhone: 'images/three-people-on-phone.jpeg',
    personWithGlasses: '/images/person-with-glasses.jpeg',
} as const;

const images = Object.values(marketingImages);

/**
 * Generate stable random values that persist across component remounts.
 * This prevents issues during development when hot reloads cause rapid remounts,
 * which can result in duplicate marketing messages or inconsistent layouts.
 * In production, these values are calculated once per page load and remain stable
 * throughout the user's session, ensuring exactly 3 images and 1 marketing message.
 */
const stableRandomMarketingIndex = Math.floor(Math.random() * 4);
const stableRandomMessage = marketingMessages[Math.floor(Math.random() * marketingMessages.length)];

const stableRandomizedImages = (() => {
    const shuffled = [...images].sort(() => Math.random() - 0.5);
    return shuffled.slice(0, 3);
})();

const AuthMarketingPanel = () => {
    const gridItems = useMemo(() => {
        const items: React.ReactElement[] = [];
        let imageIndex = 0;

        for (let i = 0; i < 4; i += 1) {
            if (i === stableRandomMarketingIndex) {
                items.push(
                    <div key={uuid()} className="bg-black-500 flex h-full w-full items-center p-6 text-white">
                        {stableRandomMessage}
                    </div>
                );
            } else {
                items.push(
                    <div key={uuid()} className="h-full w-full">
                        <img src={stableRandomizedImages[imageIndex]} alt="" className="h-full w-full object-cover" />
                    </div>
                );
                imageIndex += 1;
            }
        }

        return items;
    }, []); // No dependencies needed since everything is stable

    return (
        <div className="hidden h-screen min-h-[700px] w-full grid-cols-2 grid-rows-2 lg:grid lg:w-1/2">{gridItems}</div>
    );
};

export { AuthMarketingPanel };
