import * as React from 'react';
import { PropsWithChildren, useCallback, useEffect, useRef } from 'react';

import { ChevronLeft, ChevronRight } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button } from '@/UI-Kit/Shadcn/button';
import { ScrollArea } from '@/UI-Kit/Shadcn/scroll-area';

const SCROLL_AMOUNT = 200;

const VerticalScrollContainer = ({
    children,
    scrollAmount = SCROLL_AMOUNT,
}: PropsWithChildren<{ scrollAmount?: number }>) => {
    const scrollAreaRef = useRef<HTMLDivElement>(null);
    const initialScrollContainer = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');
    const [showLeftShadow, setShowLeftShadow] = React.useState(false);
    const [showRightShadow, setShowRightShadow] = React.useState(false);
    const needScrollButtons = initialScrollContainer
        ? initialScrollContainer?.scrollWidth > initialScrollContainer?.clientWidth
        : false;

    const handleScroll = useCallback(() => {
        const scrollContainer = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');

        if (!scrollContainer) {
            return;
        }

        const { scrollLeft, scrollWidth, clientWidth } = scrollContainer;
        setShowLeftShadow(scrollLeft > 0);
        setShowRightShadow(scrollLeft + clientWidth < scrollWidth - 1);
    }, []);

    const scrollHorizontal = (direction: 'left' | 'right') => {
        const scrollContainer = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');

        if (!scrollContainer) {
            return;
        }

        scrollContainer.scrollBy({
            left: direction === 'left' ? -scrollAmount : scrollAmount,
            behavior: 'smooth',
        });
    };

    useEffect(() => {
        const scrollContainer = scrollAreaRef.current?.querySelector('[data-radix-scroll-area-viewport]');

        if (!scrollContainer) {
            return;
        }

        handleScroll();

        scrollContainer.addEventListener('scroll', handleScroll);

        // eslint-disable-next-line consistent-return
        return () => scrollContainer.removeEventListener('scroll', handleScroll);
    }, [handleScroll]);

    return (
        <div className="group relative max-w-max">
            {needScrollButtons && (
                <Button
                    type="button"
                    variant="transparent"
                    size="icon"
                    disabled={!showLeftShadow}
                    className={cn(
                        'absolute top-1/2 -left-2 z-10 h-9 w-6 -translate-y-[calc(50%+6px)] disabled:opacity-30',
                        {
                            'shadow-[10px_0_20px_-5px] shadow-gray-900/13': showLeftShadow,
                        }
                    )}
                    onClick={() => scrollHorizontal('left')}
                    aria-label="Scroll left"
                >
                    <ChevronLeft className="size-6" strokeWidth={1.5} />
                </Button>
            )}

            <ScrollArea
                ref={scrollAreaRef}
                className={cn('w-full max-w-full', {
                    'mx-4 max-w-[calc(100%-36px)] pb-3': needScrollButtons,
                })}
                orientation="horizontal"
            >
                {children}
            </ScrollArea>

            {needScrollButtons && (
                <Button
                    type="button"
                    variant="transparent"
                    size="icon"
                    disabled={!showRightShadow}
                    className={cn(
                        'absolute top-1/2 -right-2 z-10 h-6 w-6 -translate-y-[calc(50%+6px)] disabled:opacity-30',
                        {
                            'shadow-[-10px_0_20px_-5px] shadow-gray-900/13': showRightShadow,
                        }
                    )}
                    onClick={() => scrollHorizontal('right')}
                    aria-label="Scroll right"
                >
                    <ChevronRight className="size-6" strokeWidth={1.5} />
                </Button>
            )}
        </div>
    );
};

export default VerticalScrollContainer;
