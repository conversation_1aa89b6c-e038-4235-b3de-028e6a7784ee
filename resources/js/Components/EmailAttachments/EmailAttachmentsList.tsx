import { useMemo } from 'react';

import { ArrowDownToLine, Trash2 } from 'lucide-react';

import { getFileTypeIcon } from '@/Components/TipTapEditor/extensions/Attachment/components/NodeViewAttachment/FileIcon';
import { normalizeFileSize } from '@/Components/TipTapEditor/utils/file';
import { FileUpload } from '@/types/file-upload-data';
import { Button } from '@/UI-Kit/Shadcn/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/UI-Kit/Shadcn/card';

interface EmailAttachmentsListProps {
    attachments: FileUpload[];
    onRemoveAttachment?: (attachmentId: number) => void;
    readonly?: boolean;
}

export const EmailAttachmentsList = ({
    attachments,
    onRemoveAttachment,
    readonly = false,
}: EmailAttachmentsListProps) => {
    const completedAttachments = useMemo(
        () => attachments.filter(attachment => attachment.status === 2),
        [attachments]
    );

    if (completedAttachments.length === 0) {
        return null;
    }

    return (
        <Card className="mt-4">
            <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium text-gray-700">
                    Email Attachments ({completedAttachments.length})
                </CardTitle>
            </CardHeader>
            <CardContent className="flex flex-col gap-y-4 p-4">
                {completedAttachments.map(attachment => (
                    <div
                        key={attachment.id}
                        className="flex items-center justify-between rounded-md border p-3 hover:bg-gray-50"
                    >
                        <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0 [&>svg]:size-6">{getFileTypeIcon(attachment.mime || '')}</div>
                            <div className="min-w-0 flex-1">
                                <p className="truncate text-sm font-medium text-gray-900">
                                    {attachment.params?.originalName || attachment.name}
                                </p>
                                <p className="text-xs text-gray-500">
                                    {normalizeFileSize(attachment.size || 0)}
                                    {attachment.ext && ` • ${attachment.ext.toUpperCase()}`}
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-2">
                            {attachment.params?.url && (
                                <Button variant="outline" size="sm" asChild className="text-xs">
                                    <a href={attachment.params.url} target="_blank" rel="noopener noreferrer">
                                        <ArrowDownToLine className="size-4" />
                                    </a>
                                </Button>
                            )}
                            {!readonly && onRemoveAttachment && (
                                <Button
                                    variant="destructive"
                                    size="sm"
                                    onClick={() => onRemoveAttachment(attachment.id)}
                                    className="text-xs"
                                >
                                    <Trash2 className="size-4" />
                                </Button>
                            )}
                        </div>
                    </div>
                ))}
            </CardContent>
        </Card>
    );
};
