import { ReactNode } from 'react';

type ConditionalWrapperProps = {
    children: ReactNode;
    condition?: boolean;
    defaultWrapper?: (children: ReactNode) => ReactNode;
    wrapper: (children: ReactNode) => ReactNode;
};

const ConditionalWrapper = ({ condition, defaultWrapper, wrapper, children }: ConditionalWrapperProps) => {
    if (condition) {
        return wrapper(children);
    }

    if (defaultWrapper) {
        return defaultWrapper(children);
    }

    return children;
};

export default ConditionalWrapper;
