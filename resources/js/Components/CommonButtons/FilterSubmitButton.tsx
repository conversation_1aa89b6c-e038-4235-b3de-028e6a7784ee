import { FilterIcon } from 'lucide-react';

import { useFilterFormContext } from '@/Components/Filters/context/FilterFormContext';
import { Button } from '@/UI-Kit/Shadcn/button';

/**
 * Get filter submit button for list page filter forms
 *
 * @returns
 */
const FilterSubmitButton = ({
    size = 'sm',
    ...props
}: { size?: 'default' | 'sm' | 'lg' | 'icon' | 'icon-sm' | 'xs' | 'xxs' } & React.ComponentProps<'button'>) => {
    const { processing } = useFilterFormContext();

    return (
        <Button
            id="filter-btn"
            type="submit"
            variant="success"
            className="gap-x-1.5"
            disabled={processing}
            size={size}
            {...props}
            data-ondata="filter-submit-button"
        >
            <FilterIcon size={17} />
            <div className="font-medium">Filter</div>
        </Button>
    );
};

export default FilterSubmitButton;
