import type { JS<PERSON> } from 'react';

import { cn } from '@/lib/utils';
import { PageSizeChangerProps } from '@/types/table-pagination';
import Button from '@/UI-Kit/Button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/UI-Kit/Shadcn/dropdown-menu';
import { ScrollArea } from '@/UI-Kit/Shadcn/scroll-area';

const PageSizeChanger = ({
    pageSizeOptions,
    currentPageSize,
    onPageSizeOptionClick,
    isStacked,
}: PageSizeChangerProps) => {
    const getMenuItems = (): JSX.Element => {
        if (!pageSizeOptions || pageSizeOptions.length === 0) return <div />;

        return (
            <>
                {pageSizeOptions.map((pageSize, index) => (
                    <DropdownMenuItem
                        key={`dropdown-item-${index.toString()}`}
                        onClick={() => onPageSizeOptionClick(+pageSize)}
                    >
                        {`${pageSize} / page`}
                    </DropdownMenuItem>
                ))}
            </>
        );
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button
                    color="gray"
                    buttonType="outline"
                    type="button"
                    className={cn('bg-white text-xs text-black hover:bg-white focus:bg-white')}
                    size="sm"
                >
                    {`${currentPageSize} / page`}
                </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent className="w-24" align={isStacked ? 'start' : 'end'}>
                <ScrollArea className="h-52">{getMenuItems()}</ScrollArea>
            </DropdownMenuContent>
        </DropdownMenu>
    );
};

export default PageSizeChanger;
