// eslint-disable-next-line import/prefer-default-export
export const theme = {
    ellipsisClass: 'relative inline-flex items-center text-base text-gray-500 font-extrabold',
    prevSmallClass: 'text-sm text-gray-700 ml-4',
    nextSmallClass: 'text-sm text-green-600 mr-4 font-extrabold',
    textClass: 'inline-block text-sm text-black font-medium',
    pageInputClass:
        'shadow-xs block text-sm border-gray-200 border rounded-md text-gray-700 text-center w-12 px-2 py-1.5',
    baseButton:
        'btn-link btn-inline-flex px-1 text-xs font-bold hover:bg-transparent focus:bg-transparent focus:ring-0 focus:ring-offset-0',
    baseColors: 'text-green-500 font-extrabold',
    currentColors: 'border-gray-200 text-gray-700 pointer-events-none',
    disabledClass: 'disabled:opacity-50 cursor-not-allowed text-gray-400!',
    numberClass: 'inline-flex items-center justify-center w-[17px] h-[17px] tracking-[-.10em]',
};
