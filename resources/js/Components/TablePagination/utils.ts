const makeArray = (start: number, end: number): number[] =>
    Array(end - start + 1)
        .fill(0, 0, end)
        .map((_, index) => start + index);

// eslint-disable-next-line import/prefer-default-export,consistent-return
export const makePageArray = (pageCount: number, current: number, maxSegmentList?: number): number[] | undefined => {
    // len <=7 - show all pages
    if (pageCount <= 7 || (maxSegmentList && pageCount <= maxSegmentList)) {
        return Array.from({ length: pageCount }, (ele, index) => index + 1);
    }

    // len <=15 - ####...####
    if (pageCount <= 15) {
        const segmentLimit = maxSegmentList ?? 4;
        if (current <= segmentLimit) {
            const arrLastNum = current === segmentLimit ? segmentLimit + 1 : segmentLimit;
            return [...makeArray(1, arrLastNum), -9, pageCount];
        }
        const arrFirstNum =
            current === pageCount - segmentLimit ? pageCount - segmentLimit - 1 : pageCount - segmentLimit;
        return [1, -9, ...makeArray(arrFirstNum, pageCount)];
    }

    // #...#####...#
    if (pageCount > 15) {
        const segmentLimit = maxSegmentList ?? 5;
        if (current <= segmentLimit) {
            const arrLastNum = current === segmentLimit ? segmentLimit + 1 : segmentLimit;
            return [...makeArray(1, arrLastNum), -9, pageCount];
        }
        if (current >= segmentLimit && current <= pageCount - segmentLimit) {
            return [1, -9, ...makeArray(current - 2, current + 2), -9, pageCount];
        }
        const arrFirstNum =
            current === pageCount - segmentLimit ? pageCount - segmentLimit - 1 : pageCount - segmentLimit;

        return [1, -9, ...makeArray(arrFirstNum, pageCount)];
    }
};
