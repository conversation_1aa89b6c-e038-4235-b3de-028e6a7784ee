import { theme } from '@/Components/TablePagination/config';
import { QuickJumperProps } from '@/types/table-pagination';

const QuickJumper = ({ onInputChange, onKeyDownInput }: QuickJumperProps) => (
    <div className="inline-flex items-center justify-center space-x-2 text-gray-700">
        <span className={theme.textClass}>Go to</span>
        <div>
            <input
                type="text"
                name="goTo"
                id="goTo"
                onChange={onInputChange}
                onKeyDown={onKeyDownInput}
                className={theme.pageInputClass}
            />
        </div>
        <span className={theme.textClass}>Page</span>
    </div>
);

export default QuickJumper;
