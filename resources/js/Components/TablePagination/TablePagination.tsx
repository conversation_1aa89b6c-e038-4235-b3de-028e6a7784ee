import { ChangeEvent, type JSX, KeyboardEvent, useCallback, useEffect, useRef, useState } from 'react';

import numeral from 'numeral';

import { theme } from '@/Components/TablePagination/config';
import PageChanger from '@/Components/TablePagination/PageChanger';
import PageSizeChanger from '@/Components/TablePagination/PageSizeChanger';
import QuickJumper from '@/Components/TablePagination/QuickJumper';
import useOnPaging from '@/hooks/TablePagination/useOnPaging';
import { cn } from '@/lib/utils';
import { TablePaginateProps } from '@/types/table-pagination';

const TablePagination = ({
    className = '',
    current,
    hideOnSinglePage,
    pageSize,
    pageSizeOptions,
    showQuickJumper,
    showPageChanger,
    showSizeChanger,
    total,
    onChange,
    isBottom,
    isStacked = false,
    maxSegmentList,
}: TablePaginateProps): JSX.Element | null => {
    const [goToPage, setGoToPage] = useState<number>(0);
    const [pageCount, setPageCount] = useState<number>(0);
    const { onPaging } = useOnPaging();

    const pagerRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        if (total && pageSize) {
            const newPageCount = Math.ceil(total / pageSize); // = last page
            setPageCount(newPageCount);
        }
    }, [total, pageSize]);

    const changePage = useCallback(
        (pageNum: number, pgSize = pageSize, isNewPageLimit = false) => {
            if (typeof onChange !== 'undefined') {
                onChange(pageNum, pgSize, isNewPageLimit);
            } else {
                onPaging({ page: pageNum, didLimitChange: isNewPageLimit, pageSize: pgSize });
            }
        },
        [onChange, isBottom, pageSize]
    );

    const onPageNumClick = useCallback(
        (pageNum: number, e: React.MouseEvent) => {
            e.stopPropagation();
            changePage(pageNum, pageSize);
        },
        [pageSize, changePage]
    );

    const onInputChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        if (value.match(/^\d+$/)) {
            setGoToPage(+value);
        }
    }, []);

    const onKeyDownInput = useCallback(
        (e: KeyboardEvent<HTMLInputElement>) => {
            if (goToPage < 1 || goToPage > pageCount || goToPage === current) return;

            if (e.key === 'Enter' || e.key === 'Tab') {
                changePage(goToPage);
            }
        },
        [changePage, current, goToPage, pageCount]
    );

    const onPageSizeOptionClick = useCallback(
        (size: number) => {
            const oldPageSize = pageSize ? +pageSize : 20;

            if (oldPageSize !== size) {
                changePage(1, size, true);
            }
        },
        [changePage]
    );

    const onNavButtonClick = (direction: string) => {
        if (direction === 'back' && current && current > 1) {
            changePage(current - 1);
            return;
        }
        if (direction === 'next' && current && current < pageCount) {
            changePage(current + 1);
        }
    };

    if (hideOnSinglePage || !current || !total || !pageSize) return null;

    return (
        <div key={isBottom ? 'bottom' : 'top'} ref={pagerRef} className={cn(className)}>
            <div className="flex items-center justify-between border-gray-200">
                <div className="flex flex-1 justify-between sm:hidden">
                    <button
                        type="button"
                        onClick={() => onNavButtonClick('back')}
                        className={cn(theme.prevSmallClass, current === 1 ? theme.disabledClass : '')}
                    >
                        Previous
                    </button>
                    <button
                        type="button"
                        onClick={() => onNavButtonClick('back')}
                        className={cn(theme.nextSmallClass, current === pageCount ? theme.disabledClass : '')}
                    >
                        Next
                    </button>
                </div>
                <div
                    className={cn({
                        'hidden items-center space-x-4 text-gray-900 sm:flex sm:flex-1 sm:flex-wrap sm:justify-end md:flex-row':
                            !isStacked,
                        'flex flex-wrap gap-y-3': isStacked,
                    })}
                >
                    <div data-testid="pagination-records" aria-label="Pagination Records">
                        <p className="text-sm">
                            <span className={theme.textClass}>
                                {current === 1 ? 1 : (current - 1) * +pageSize + 1} -{' '}
                                {current * +pageSize < total ? current * +pageSize : total} of{' '}
                                {numeral(total).format('0,0')} records
                            </span>
                        </p>
                    </div>

                    {showPageChanger && (
                        <PageChanger
                            current={current}
                            pageCount={pageCount}
                            onPageNumClick={onPageNumClick}
                            isStacked={isStacked}
                            maxSegmentList={maxSegmentList}
                        />
                    )}

                    {showSizeChanger && (
                        <PageSizeChanger
                            pageSizeOptions={pageSizeOptions}
                            currentPageSize={pageSize}
                            onPageSizeOptionClick={onPageSizeOptionClick}
                            isStacked={isStacked}
                        />
                    )}
                    {/* changed this to ! for now... ask miguel if anything */}
                    {!showQuickJumper && <QuickJumper onInputChange={onInputChange} onKeyDownInput={onKeyDownInput} />}
                </div>
            </div>
        </div>
    );
};

export default TablePagination;
