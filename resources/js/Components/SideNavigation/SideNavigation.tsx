import { Fragment, useMemo } from 'react';

import { useRoute } from 'ziggy-js';

import { usePage } from '@inertiajs/react';

import '@/Components/SideNavigation/side-navigation.css';

import getMenuItems from '@/Components/SideNavigation/menuItems';
import { SideMenuItem } from '@/Components/SideNavigation/Partials/SideMenuItem';
import useCheckMenuItemPermission from '@/hooks/SideNavigation/useCheckMenuItemPermission';
import getActiveMenuItem from '@/hooks/SideNavigation/useGetActiveMenuItem';
import { cn } from '@/lib/utils';
import { useStoreState } from '@/store/hooks';
import { PageProps } from '@/types/general';

const SideNavigation = ({ mobileSidebarOpen }: { mobileSidebarOpen?: boolean }) => {
    const { auth, docPortalAdminUrl } = usePage<PageProps & { docPortalAdminUrl: string }>().props;
    const { url } = usePage();
    const route = useRoute();
    const collapsedSidebar = useStoreState(state => state.appSettingsModel.collapsedSidebar);
    const checkMenuItemPermission = useCheckMenuItemPermission();

    const menuItems = useMemo(() => getMenuItems({ route, docPortalAdminUrl }), [route]);
    const activeMenuItem = getActiveMenuItem(url); // Get the active tab based on the current URL

    return (
        <nav
            className={cn('flex flex-1 flex-col space-y-3', {
                'collapsed-sidebar': collapsedSidebar && !mobileSidebarOpen,
            })}
            aria-label="Sidebar"
        >
            {menuItems.map(item => {
                if (checkMenuItemPermission(item.permissions, auth.can)) {
                    return (
                        <SideMenuItem
                            {...item}
                            key={`app-menu-item-${item.menuKey}`}
                            current={activeMenuItem === item.menuKey}
                            mobileSidebarOpen={mobileSidebarOpen}
                        />
                    );
                }

                return <Fragment key={`app-menu-item-${item.menuKey}`} />;
            })}
        </nav>
    );
};

export default SideNavigation;
