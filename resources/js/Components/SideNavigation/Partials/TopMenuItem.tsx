import { Fragment, ReactNode } from 'react';

import { isEmpty } from 'lodash';

import { duotone } from '@fortawesome/fontawesome-svg-core/import.macro';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Link, usePage } from '@inertiajs/react';

import ConditionalWrapper from '@/Components/ConditionalWrapper';
import useTenantDomainRoute from '@/hooks/useTenantDomainRoute';
import { cn } from '@/lib/utils';
import { useStoreState } from '@/store/hooks';
import { PageProps } from '@/types/general';
import { TopMenuItemProps } from '@/types/sidebar-navigation';
import { Tooltip, TooltipContent, TooltipPortal, TooltipProvider, TooltipTrigger } from '@/UI-Kit/Shadcn/tooltip';

// eslint-disable-next-line import/prefer-default-export
export const TopMenuItem = ({
    childrenItems,
    className,
    current,
    icon,
    isExternalLink,
    isChild,
    link,
    menuKey,
    mobileSidebarOpen,
    title,
}: TopMenuItemProps) => {
    const { currentTenant } = usePage<PageProps>().props;
    let topMenuItem = <Fragment key={`top-menu-item-${menuKey}`} />;
    const collapsedSidebar = useStoreState(state => state.appSettingsModel.collapsedSidebar);
    const { tenantDomainRoute } = useTenantDomainRoute();
    const usingCurrentTenant = !isEmpty(currentTenant);
    const routeLink = isExternalLink ? link : tenantDomainRoute(link as string);

    if (childrenItems.length === 0 && !isChild && link) {
        const getTooltip = (children: ReactNode) => (
            <TooltipProvider key="show-filter-btn">
                <Tooltip>
                    <TooltipTrigger asChild>{children}</TooltipTrigger>
                    <TooltipPortal>
                        <TooltipContent className="z-9999 text-center" side="right">
                            {title}
                        </TooltipContent>
                    </TooltipPortal>
                </Tooltip>
            </TooltipProvider>
        );

        const linkElement = (
            <ConditionalWrapper condition={collapsedSidebar} wrapper={(children: ReactNode) => getTooltip(children)}>
                {!isExternalLink && usingCurrentTenant ? (
                    <Link
                        href={routeLink as string}
                        key={`parent-link-${menuKey}`}
                        className={cn(`main-parent-link no-anchor-style group`, {
                            'current-link': current,
                            'w-full': !collapsedSidebar,
                        })}
                    >
                        {icon}

                        <span className="menu-title flex-1">{title}</span>
                    </Link>
                ) : (
                    <a
                        href={routeLink}
                        key={`parent-link-${menuKey}`}
                        target={isExternalLink ? '_blank' : undefined}
                        rel="noreferrer"
                        className={cn(`main-parent-link no-anchor-style group`, {
                            'current-link': current,
                            'w-full': !collapsedSidebar,
                        })}
                    >
                        {icon}
                        <span className="menu-title flex-1">{title}</span>
                        {!collapsedSidebar && (
                            <FontAwesomeIcon
                                icon={duotone('external-link')}
                                className={cn('side-menu-open-close-icon')}
                            />
                        )}
                    </a>
                )}
            </ConditionalWrapper>
        );

        topMenuItem = (
            <div
                className={cn('flex', { 'justify-center': collapsedSidebar && !mobileSidebarOpen }, className)}
                key={`top-menu-item-${menuKey}`}
            >
                {linkElement}
            </div>
        );
    }

    return topMenuItem;
};
