import { light } from '@fortawesome/fontawesome-svg-core/import.macro';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Disclosure } from '@headlessui/react';

import { cn } from '@/lib/utils';
import { DisclosureButtonProps } from '@/types/sidebar-navigation';

// eslint-disable-next-line import/prefer-default-export
export const DisclosureButton = ({ current, level, menuKey, open, title, icon }: DisclosureButtonProps) => (
    <Disclosure.Button
        key={`sub-menu-link-${menuKey}`}
        className={cn(`link-has-children group`, {
            'current-link': current,
            'main-parent-link': level === 1,
            'sub-menu-parent-link': level > 1,
            'menu-open': open,
        })}
    >
        {icon}
        <span className="flex-1">{title}</span>
        <FontAwesomeIcon icon={light('chevron-right')} className={cn('side-menu-open-close-icon')} />
    </Disclosure.Button>
);
