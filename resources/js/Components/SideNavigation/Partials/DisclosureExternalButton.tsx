import { duotone } from '@fortawesome/fontawesome-svg-core/import.macro';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Disclosure } from '@headlessui/react';

import { cn } from '@/lib/utils';
import { DisclosureExternalButtonProps } from '@/types/sidebar-navigation';

// eslint-disable-next-line import/prefer-default-export
export const DisclosureExternalButton = ({
    isCurrent,
    isExternalLink,
    link,
    menuKey,
    title,
}: DisclosureExternalButtonProps) => (
    <Disclosure.Button
        key={`sub-menu-external-link-${menuKey}`}
        as="a"
        href={link}
        target={isExternalLink ? '_blank' : '_self'}
        className={cn(`child-menu-link group`, {
            'current-link': isCurrent,
        })}
    >
        <span className="flex-1">{title}</span>
        <FontAwesomeIcon icon={duotone('external-link')} className={cn('side-menu-open-close-icon')} />
    </Disclosure.Button>
);
