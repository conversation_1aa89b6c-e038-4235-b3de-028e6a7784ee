import { Disclosure } from '@headlessui/react';
import { Link } from '@inertiajs/react';

import { cn } from '@/lib/utils';
import { DisclosureExternalButtonProps } from '@/types/sidebar-navigation';

// eslint-disable-next-line import/prefer-default-export
export const DisclosureLinkButton = ({
    isCurrent,
    isExternalLink,
    link,
    menuKey,
    title,
}: DisclosureExternalButtonProps) => (
    <Disclosure.Button
        key={`sub-menu-link-${menuKey}`}
        as={Link}
        href={link}
        target={isExternalLink ? '_blank' : '_self'}
        className={cn(`child-menu-link group`, {
            'current-link': isCurrent,
        })}
    >
        <span className="flex-1">{title}</span>
    </Disclosure.Button>
);
