import { Fragment } from 'react';

import { Disclosure, Transition } from '@headlessui/react';
import { usePage } from '@inertiajs/react';

import { DisclosureExternalButton } from '@/Components/SideNavigation/Partials/DisclosureExternalButton';
import { DisclosureLinkButton } from '@/Components/SideNavigation/Partials/DisclosureLinkButton';
import { SideMenuItem } from '@/Components/SideNavigation/Partials/SideMenuItem';
import useCheckMenuItemPermission from '@/hooks/SideNavigation/useCheckMenuItemPermission';
import useGetCurrentMenuItem from '@/hooks/SideNavigation/useGetCurrentMenuItem';
import { ANY_TODO, PageProps } from '@/types/general';
import { DisclosurePanelProps } from '@/types/sidebar-navigation';

// eslint-disable-next-line import/prefer-default-export
export const DisclosurePanel = ({ childrenItems, menuKey }: DisclosurePanelProps) => {
    const { auth } = usePage<PageProps>().props;
    const checkMenuItemPermission = useCheckMenuItemPermission();
    const getCurrentMenuItem = useGetCurrentMenuItem();

    return (
        <Transition
            key={`menu-panel-transition-${menuKey}`}
            enter="transition duration-700 ease-out"
            enterFrom="transform scale-95 opacity-0"
            enterTo="transform scale-100 opacity-100"
            leave="transition duration-500 ease-out"
            leaveFrom="transform scale-100 opacity-100"
            leaveTo="transform scale-95 opacity-0"
            unmount={false}
        >
            <Disclosure.Panel key={`menu-panel-${menuKey}`} unmount={false} className="space-y-2 pl-3">
                {childrenItems.map((subItem: ANY_TODO) => {
                    if (checkMenuItemPermission(subItem.permissions, auth.can)) {
                        const isCurrent = getCurrentMenuItem(subItem.currentKeys);

                        // eslint-disable-next-line no-nested-ternary
                        return subItem.childrenItems && subItem.childrenItems.length > 0 ? (
                            <SideMenuItem
                                {...subItem}
                                isChild
                                current={isCurrent}
                                key={`app-menu-sub-item-${subItem.menuKey}`}
                            />
                        ) : subItem.isExternalLink ? (
                            <DisclosureExternalButton
                                key={`menu-panel-external-button-${subItem.menuKey}`}
                                isCurrent={isCurrent}
                                isExternalLink={subItem.isExternalLink}
                                link={subItem.link}
                                menuKey={subItem.menuKey}
                                title={subItem.title}
                            />
                        ) : (
                            <DisclosureLinkButton
                                key={`menu-panel-link-button-${subItem.menuKey}`}
                                isCurrent={isCurrent}
                                isExternalLink={subItem.isExternalLink}
                                link={subItem.link}
                                menuKey={subItem.menuKey}
                                title={subItem.title}
                            />
                        );
                    }

                    return <Fragment key={`app-menu-sub-item-${subItem.menuKey}`} />;
                })}
            </Disclosure.Panel>
        </Transition>
    );
};
