import { Fragment } from 'react';

import { SubMenuItem } from '@/Components/SideNavigation/Partials/SubMenuItem';
import { TopMenuItem } from '@/Components/SideNavigation/Partials/TopMenuItem';
import { SideMenuItemProps } from '@/types/sidebar-navigation';

// eslint-disable-next-line import/prefer-default-export
export const SideMenuItem = ({
    childrenItems = [],
    className = '',
    current,
    menuKey,
    mobileSidebarOpen,
    icon,
    isChild = false,
    isExternalLink = false,
    level,
    link,
    title,
}: SideMenuItemProps) => (
    <Fragment key={`outmost-${menuKey}`}>
        <TopMenuItem
            key={`outmost-top-menu-item-${menuKey}`}
            childrenItems={childrenItems}
            className={className}
            current={current}
            icon={icon}
            isChild={isChild}
            isExternalLink={isExternalLink}
            link={link}
            menuKey={menuKey}
            mobileSidebarOpen={mobileSidebarOpen}
            title={title}
        />
        <SubMenuItem
            key={`outmost-sub-menu-item-${menuKey}`}
            childrenItems={childrenItems}
            current={current}
            icon={icon}
            level={level}
            menuKey={menuKey}
            title={title}
        />
    </Fragment>
);
