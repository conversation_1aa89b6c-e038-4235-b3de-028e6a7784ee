@reference '../../../css/app.css';

.main-parent-link,
.sub-menu-parent-link,
.child-menu-link {
    @apply inline-flex items-center gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold text-white hover:bg-gray-900/40 hover:text-emerald-500;
}

.main-parent-link.link-has-children,
.sub-menu-parent-link.link-has-children {
    @apply text-left;
}

.main-parent-link.current-link {
    @apply bg-emerald-500 text-black;
}

.sub-menu-parent-link.current-link {
    @apply bg-emerald-200 text-gray-900 shadow-inner;
}

.child-menu-link.current-link {
    @apply font-bold text-emerald-500;
}

.main-parent-link .side-menu-item-icon {
    @apply h-4 w-4 flex-shrink-0 transition-all duration-150 ease-in-out;
}

.side-menu-open-close-icon {
    @apply mr-1 ml-3 h-3 w-3 flex-shrink-0 transform transition-colors duration-150 ease-in-out;
}

.main-parent-link.menu-open .side-menu-open-close-icon,
.sub-menu-parent-link.menu-open .side-menu-open-close-icon {
    @apply rotate-90;
}

.collapsed-sidebar {
    .menu-title {
        @apply hidden;
    }

    .side-menu-item-icon {
        @apply m-auto h-4.5 w-4.5;
    }
}

/* .main-parent-link.current-link.menu-open .side-menu-open-close-icon {
        @apply text-white;
    } */
