import { icon } from '@fortawesome/fontawesome-svg-core/import.macro';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { ANY_TODO } from '@/types/general';
import { MenuItem } from '@/types/sidebar-navigation';

// This function accepts a route function so we don't depend on global route
const getMenuItems = ({ route, docPortalAdminUrl }: { route: ANY_TODO; docPortalAdminUrl: string }): MenuItem[] => [
    {
        title: 'Studies',
        level: 1,
        menuKey: 'studies',
        link: 'studies.index',
        icon: (
            <FontAwesomeIcon icon={icon({ name: 'files-medical', style: 'regular' })} className="side-menu-item-icon" />
        ),
        currentKeys: ['Studies/'],
        permissions: [['viewStudy'], ['createStudy']],
    },
    {
        title: 'Campaign Forms',
        level: 1,
        menuKey: 'campaign-forms',
        link: 'campaign-forms.index',
        icon: <FontAwesomeIcon icon={icon({ name: 'pen-field', style: 'regular' })} className="side-menu-item-icon" />,
        currentKeys: ['CampaignForms/'],
        permissions: [['viewCampaignForm'], ['createCampaignForm']],
    },
    {
        title: 'Referrals',
        level: 1,
        menuKey: 'referrals',
        link: 'referrals.index',
        icon: (
            <FontAwesomeIcon
                icon={icon({ name: 'universal-access', style: 'regular' })}
                className="side-menu-item-icon"
            />
        ),
        currentKeys: ['Referrals/'],
        permissions: [['viewReferral']],
    },
    {
        title: 'Referral Central',
        level: 1,
        menuKey: 'referral-central',
        link: 'referral-central.work-queue',
        icon: <FontAwesomeIcon icon={icon({ name: 'users', style: 'regular' })} className="side-menu-item-icon" />,
        currentKeys: ['ReferralCentral/'],
        permissions: [['viewReferralCentral']],
    },
    {
        title: 'Referral Dashboard',
        level: 1,
        menuKey: 'referral-dashboard',
        link: 'referrals-dashboard.index',
        icon: <FontAwesomeIcon icon={icon({ name: 'gauge-max', style: 'regular' })} className="side-menu-item-icon" />,
        currentKeys: ['ReferralDashboard/'],
        permissions: [['viewReferralDashboard']],
    },
    {
        title: 'Funnel Dashboard',
        level: 1,
        menuKey: 'funnel-dashboard',
        link: 'funnel-dashboard.index',
        icon: (
            <FontAwesomeIcon icon={icon({ name: 'filter-list', style: 'regular' })} className="side-menu-item-icon" />
        ),
        currentKeys: ['FunnelDashboard/'],
        permissions: [['viewFunnelDashboard']],
    },
    {
        title: 'CPT Dashboard',
        level: 1,
        menuKey: 'cpt-dashboard',
        link: 'cpt-report.index',
        icon: (
            <FontAwesomeIcon
                icon={icon({ name: 'chart-tree-map', style: 'regular' })}
                className="side-menu-item-icon"
            />
        ),
        currentKeys: ['CPTDashboard/'],
        permissions: [['viewCptDashboard']],
    },
    {
        title: 'Site Performance',
        level: 1,
        menuKey: 'site-performance-dashboard',
        link: 'site-performance.index',
        icon: (
            <FontAwesomeIcon icon={icon({ name: 'chart-column', style: 'regular' })} className="side-menu-item-icon" />
        ),
        currentKeys: ['SitePerformanceDashboard/'],
        permissions: [['viewSitePerformanceDashboard']],
    },
    {
        title: 'Study KPIs',
        level: 1,
        menuKey: 'study-kpi-dashboard',
        link: 'study-kpi-dashboard.index',
        icon: <FontAwesomeIcon icon={icon({ name: 'table-list', style: 'regular' })} className="side-menu-item-icon" />,
        currentKeys: ['FunnelDashboard/'],
        permissions: [['viewStudyKpiDashboard']],
    },
    {
        title: 'Campaigns',
        level: 1,
        menuKey: 'campaigns',
        link: 'campaign-dashboard.index',
        icon: (
            <FontAwesomeIcon
                icon={icon({ name: 'chart-mixed-up-circle-dollar', style: 'regular' })}
                className="side-menu-item-icon"
            />
        ),
        currentKeys: ['Campaigns/'],
        permissions: [['viewCampaignDashboard']],
    },
    {
        title: 'Feasibility',
        level: 1,
        menuKey: 'feasibility-reports',
        link: 'admin.feasibility-reports.index',
        icon: <FontAwesomeIcon icon={icon({ name: 'list-check', style: 'regular' })} className="side-menu-item-icon" />,
        currentKeys: ['FeasibilityReports/'],
        permissions: [['viewFeasibilityReport']],
    },
    {
        title: 'Exports',
        level: 1,
        menuKey: 'exports',
        link: 'exports.index',
        icon: (
            <FontAwesomeIcon
                icon={icon({ name: 'cloud-arrow-down', style: 'regular' })}
                className="side-menu-item-icon"
            />
        ),
        currentKeys: ['Exports/'],
        permissions: [['viewExport']],
    },
    {
        title: 'Doc Portal Admin',
        level: 1,
        menuKey: 'document-portal',
        isExternalLink: true,
        link: `${docPortalAdminUrl}${route('document-portal.admin.index', {}, false)}`,
        icon: <FontAwesomeIcon icon={icon({ name: 'file-pdf', style: 'regular' })} className="side-menu-item-icon" />,
        currentKeys: ['DocumentPortal/'],
        permissions: [['manageDocuments']],
    },
    {
        title: 'Settings',
        level: 1,
        menuKey: 'settings',
        link: 'admin.settings.index',
        icon: <FontAwesomeIcon icon={icon({ name: 'cog', style: 'regular' })} className="side-menu-item-icon" />,
        currentKeys: [
            'Settings/',
            'CampaignEvents/',
            'Clients/',
            'Edit/',
            'ContactTypes/',
            'Countries/',
            'FacilityTypes/',
            'Integrations/',
            'LeadStatuses/',
            'Mappings/',
            'PermissionGroups/',
            'PermissionToRole/',
            'PlatformCredentials/',
            'Roles/',
            'SiteLocations/',
            'States/',
            'TherapeuticAreas/',
            'Users/',
        ],
        permissions: [['viewSettings']],
    },
];

export default getMenuItems;
