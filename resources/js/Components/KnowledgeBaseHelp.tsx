import React from 'react';

import { LightbulbIcon } from 'lucide-react';

import { But<PERSON> } from '@/UI-Kit/Shadcn/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/UI-Kit/Shadcn/dropdown-menu';

export interface KnowledgeBaseLink {
    title: string;
    url: string;
}

export interface KnowledgeBaseHelpProps {
    links: KnowledgeBaseLink[];
}

const KnowledgeBaseHelp: React.FC<KnowledgeBaseHelpProps> = ({ links = [] }) => (
    <div className="fixed right-6 bottom-6 z-50">
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="default" size="sm" className="rounded-full bg-black shadow-lg">
                    <LightbulbIcon className="mr-1 h-5 w-5 text-white" /> Help
                    <span className="sr-only">Knowledge Base</span>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Knowledge Base</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {links.map(link => (
                    <DropdownMenuItem key={link.title.toLowerCase().replace(/\s+/g, '-')} asChild>
                        <a
                            href={link.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-black-500! cursor-pointer font-normal!"
                        >
                            {link.title}
                        </a>
                    </DropdownMenuItem>
                ))}
            </DropdownMenuContent>
        </DropdownMenu>
    </div>
);

export default KnowledgeBaseHelp;
