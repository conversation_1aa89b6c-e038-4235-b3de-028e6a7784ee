import { Pencil, RefreshCcwDot, Trash2 } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { usePage } from '@inertiajs/react';

import RowActionsDropdownTrigger from '@/Components/CommonButtons/RowActionsDropdownTrigger';
import ConfirmDialog from '@/Components/Modals/ConfirmDialog';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import { useModalContext } from '@/Contexts/ModalContext';
import EditExportForm from '@/Pages/Exports/Partials/EditExportForm';
import { ActionsRendererProps } from '@/types/ag-grid';
import { PageProps } from '@/types/general';
import Modal from '@/UI-Kit/Modal';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem } from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess } from '@/utils/auth';

const ExportRowActions = ({ data }: ActionsRendererProps) => {
    const route = useRoute();
    const { auth } = usePage<PageProps>().props;
    const { setShowModal, setModalData, setModalComponent } = useModalContext();
    const hasActions = canAccess(['reprocessExport', 'deleteExport'], auth.can);

    return (
        <>
            {hasActions && (
                <DropdownMenu modal={false}>
                    <RowActionsDropdownTrigger />

                    <DropdownMenuContent align="end">
                        {canAccess(['reprocessExport'], auth.can) && data.status !== '2' && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="reprocess-export-button"
                                    key="reprocess-export"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    confirmData={data}
                                                    color="green"
                                                    content="Are you sure you want to reprocess these records?"
                                                    dialogType="confirm"
                                                    endpoint={route('exports.reprocess', data.uuid)}
                                                    iconProps={{ name: 'RefreshCcwDot' }}
                                                    okText="Reprocess Export"
                                                    title="Reprocess Export"
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <RefreshCcwDot className="h-4 w-4!" /> Reprocess Export
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}

                        {(data.user_uuid === auth.user.uuid || canAccess(['viewAllExports'], auth.can)) && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="edit-export"
                                    key="edit-export"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper title="Rename Export">
                                                <EditExportForm />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Pencil size={16} />
                                    Rename Export
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}

                        {canAccess(['deleteExport'], auth.can) && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="delete-export-button"
                                    key="delete-export"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    confirmData={data}
                                                    color="red"
                                                    content="Are you sure you want to delete this export?"
                                                    dialogType="delete"
                                                    endpoint={route('exports.destroy', { export: data.uuid })}
                                                    iconProps={{ name: 'Trash2' }}
                                                    okText="Delete Export"
                                                    title="Delete Export"
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Trash2 className="h-4 w-4!" /> Delete Export
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

export default ExportRowActions;
