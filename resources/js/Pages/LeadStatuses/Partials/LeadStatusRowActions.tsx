import { Pencil, Trash2 } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { usePage } from '@inertiajs/react';

import RowActionsDropdownTrigger from '@/Components/CommonButtons/RowActionsDropdownTrigger';
import ConfirmDialog from '@/Components/Modals/ConfirmDialog';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import { useModalContext } from '@/Contexts/ModalContext';
import EditLeadStatusForm from '@/Pages/LeadStatuses/Partials/Form/EditLeadStatusForm';
import { ActionsRendererProps } from '@/types/ag-grid';
import { PageProps } from '@/types/general';
import Modal from '@/UI-Kit/Modal';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem } from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess } from '@/utils/auth';

const LeadStatusRowActions = ({ data }: ActionsRendererProps) => {
    const route = useRoute();
    const { auth } = usePage<PageProps>().props;
    const { setShowModal, setModalData, setModalComponent } = useModalContext();
    const hasActions = canAccess(['editReferralStatus', 'deleteReferralStatus'], auth.can);

    return (
        <>
            {hasActions && (
                <DropdownMenu modal={false}>
                    <RowActionsDropdownTrigger />

                    <DropdownMenuContent align="end">
                        {canAccess(['editReferralStatus'], auth.can) && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="edit-lead-status"
                                    key="edit-lead-status"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper title="Edit Lead Status">
                                                <EditLeadStatusForm />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Pencil size={16} />
                                    Edit Lead Status
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}

                        {canAccess(['deleteReferralStatus'], auth.can) && !data.is_default && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="delete-lead-status-button"
                                    key="delete-lead-status"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    confirmData={data}
                                                    color="red"
                                                    content="Are you sure you want to delete this lead status?"
                                                    dialogType="delete"
                                                    endpoint={route('admin.lead-statuses.destroy', {
                                                        leadStatus: data.uuid,
                                                    })}
                                                    iconProps={{ name: 'Trash2' }}
                                                    okText="Delete Lead Status"
                                                    title="Delete Lead Status"
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Trash2 className="h-4 w-4!" /> Delete Lead Status
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

export default LeadStatusRowActions;
