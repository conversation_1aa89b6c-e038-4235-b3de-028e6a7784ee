import { useCallback } from 'react';

import { Form } from 'antd';

import { router, usePage } from '@inertiajs/react';

import { useReferralBulkActionsContext } from '@/Contexts/ReferralBulkActionsContext';
import useBulkUpdateSite from '@/hooks/Referrals/Bulk/useBulkUpdateSite';
import useHandleFormSubmit from '@/hooks/useHandleFormSubmit';
import BulkUpdateSiteForm from '@/Pages/Referrals/Partials/Form/Bulk/BulkUpdateSiteForm';
import { useStoreActions, useStoreState } from '@/store/hooks';
import { PageProps, UUID } from '@/types/general';
import { ReferralBulkUpdateSite } from '@/types/referral-edit';
import { Button } from '@/UI-Kit/Shadcn/button';
import { ScrollArea } from '@/UI-Kit/Shadcn/scroll-area';
import { Sheet, SheetContent, SheetFooter, She<PERSON>Header, SheetTitle } from '@/UI-Kit/Shadcn/sheet';

const BulkUpdateSiteSheet = () => {
    const { studyFilterUUID } = usePage<PageProps<{ studyFilterUUID: UUID }>>().props;
    const [form] = Form.useForm();
    const setIsFormDirty = useStoreActions(actions => actions.appSettingsModel.setIsFormDirty);
    const isFormDirty = useStoreState(state => state.appSettingsModel.isFormDirty);
    const clearIsDirty = useCallback(() => setIsFormDirty(false), []);
    const { bulkUpdateSite } = useBulkUpdateSite();

    const { isSheetOpen, setIsSheetOpen, setShowBulkSite } = useReferralBulkActionsContext();

    const closeSheet = () => {
        setShowBulkSite(false);
        form.resetFields();
        clearIsDirty();
        setIsSheetOpen(false);
    };

    const handleValuesChange = () => {
        if (!isFormDirty) {
            setIsFormDirty(true);
        }
    };

    const { isFormSubmitting, handleFormSubmit } = useHandleFormSubmit(async (formData: ReferralBulkUpdateSite) => {
        clearIsDirty();

        await bulkUpdateSite(formData, form);

        setTimeout(() => {
            closeSheet();
            router.reload();
        }, 1000);
    });

    return (
        <Sheet
            open={isSheetOpen}
            onOpenChange={(isOpen: boolean) => {
                if (!isOpen) {
                    closeSheet();
                }
            }}
        >
            <SheetContent size="2xl">
                <div className="flex h-full flex-col">
                    <SheetHeader>
                        <SheetTitle>Bulk Update Site</SheetTitle>
                    </SheetHeader>

                    <Form
                        form={form}
                        className="flex h-full flex-col"
                        name="bulk-assign-site-form"
                        layout="vertical"
                        initialValues={{
                            study_uuid: studyFilterUUID,
                            referral_sites: [],
                        }}
                        onFinish={handleFormSubmit}
                        onFinishFailed={({ errorFields }) => {
                            clearIsDirty();
                            console.log('onFinishFailed', errorFields);
                        }}
                        onValuesChange={handleValuesChange}
                        onError={e => console.log('onError', e)}
                    >
                        <ScrollArea className="flex h-full flex-col px-4">
                            <BulkUpdateSiteForm />
                        </ScrollArea>

                        <SheetFooter className="pt-6">
                            <div className="flex justify-end gap-2">
                                <Button type="button" variant="outline" onClick={closeSheet} className="w-24">
                                    Cancel
                                </Button>
                                <Button type="submit" variant="success" disabled={isFormSubmitting} className="w-24">
                                    Save
                                </Button>
                            </div>
                        </SheetFooter>
                    </Form>
                </div>
            </SheetContent>
        </Sheet>
    );
};

export default BulkUpdateSiteSheet;
