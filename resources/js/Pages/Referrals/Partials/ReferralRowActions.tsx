import { FileText, Pencil, Trash2 } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { usePage } from '@inertiajs/react';

import RowActionsDropdownTrigger from '@/Components/CommonButtons/RowActionsDropdownTrigger';
import ConfirmDialog from '@/Components/Modals/ConfirmDialog';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import { useModalContext } from '@/Contexts/ModalContext';
import { ActionsRendererProps } from '@/types/ag-grid';
import { PageProps } from '@/types/general';
import Modal from '@/UI-Kit/Modal';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLinkItem,
} from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess, canAccessAll } from '@/utils/auth';

const ReferralRowActions = ({ data }: ActionsRendererProps) => {
    const route = useRoute();
    const { auth } = usePage<PageProps>().props;
    const { setShowModal, setModalData, setModalComponent } = useModalContext();
    const canEdit = canAccessAll(['editReferralPhi', 'editReferralPii'], auth.can);
    const canView = canAccessAll(['viewReferralPhi', 'viewReferralPii'], auth.can);
    const canDelete = canAccessAll(['deleteReferralPhi', 'deleteReferralPii'], auth.can);

    const hasActions = canEdit || canView || canDelete;

    return (
        <>
            {hasActions && (
                <DropdownMenu modal={false}>
                    <RowActionsDropdownTrigger />

                    <DropdownMenuContent align="end">
                        {canEdit && (
                            <DropdownMenuLinkItem
                                data-testid="edit-referral"
                                key="edit-referral"
                                href={route('admin.referrals.edit', data.uuid)}
                            >
                                <Pencil size={16} /> Edit Referral
                            </DropdownMenuLinkItem>
                        )}

                        {canView && (
                            <DropdownMenuLinkItem
                                data-testid="manage-referral"
                                key="read-referral"
                                href={route('referrals.show', data.uuid)}
                            >
                                <FileText className="h-4 w-4!" /> Manage Referral
                            </DropdownMenuLinkItem>
                        )}

                        {canDelete && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="delete-referral"
                                    key="delete-referral"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    confirmData={data}
                                                    color="red"
                                                    content="Are you sure you want to delete this referral?"
                                                    dialogType="delete"
                                                    endpoint={route('admin.referrals.destroy', { lead: data.uuid })}
                                                    iconProps={{ name: 'Trash2' }}
                                                    okText="Delete Referral"
                                                    title="Delete Referral"
                                                    showPermanentDelete={canAccess(['forceDeleteReferral'], auth.can)}
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Trash2 className="h-4 w-4!" /> Delete Referral
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

export default ReferralRowActions;
