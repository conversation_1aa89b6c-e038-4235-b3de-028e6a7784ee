import { type JSX, useEffect } from 'react';

import { Form, Input } from 'antd';

import { usePage } from '@inertiajs/react';

import { useAgGridTableContext } from '@/Contexts/AgGridTableContext';
import LeadStatusSelect from '@/Pages/Referrals/Partials/Form/LeadStatusSelect';
import { PageProps, UUID } from '@/types/general';
import { Alert, AlertDescription } from '@/UI-Kit/Shadcn/alert';
import { Separator } from '@/UI-Kit/Shadcn/separator';
import { canAccess } from '@/utils/auth';

const BulkUpdateStatusForm = (): JSX.Element => {
    const { auth, studyFilterUUID, workflowFilterUUID } =
        usePage<PageProps<{ studyFilterUUID: UUID; workflowFilterUUID: UUID }>>().props;
    const form = Form.useFormInstance();
    const { agTableState } = useAgGridTableContext();
    const selectedRows = agTableState?.selectedRows ?? [];
    const bulkStatus = Form.useWatch('bulk-status');
    const canTransitionToAnyStatus = canAccess(['transitionToAnyStatus'], auth.can);

    useEffect(() => {
        const newReferrals = selectedRows.map(referral => ({
            referral: referral.uuid,
            referral_name: referral.name,
            current_status: referral.lead_status.dropdown.label,
            status: bulkStatus || referral.lead_status.dropdown,
        }));

        form.setFieldsValue({ referral_statuses: [...newReferrals] });
    }, [JSON.stringify(bulkStatus)]);

    /**
     * Show each referral status select if:
     *    - The auth user can transition to any status and there is no workflow filter selected
     *    - Or, the auth user can transition to any status and there is a bulk status selected
     *    - Or, the auth user cannot transition to any status
     */
    const showReferralStatuses =
        (canTransitionToAnyStatus && !workflowFilterUUID) ||
        (canTransitionToAnyStatus && bulkStatus) ||
        !canTransitionToAnyStatus;

    return (
        <div className="relative">
            <div>
                <Form.Item name="study_uuid" key="study_uuid" hidden>
                    <Input />
                </Form.Item>

                {canTransitionToAnyStatus && (
                    <>
                        <div className="w-full px-2 pt-2">
                            {workflowFilterUUID ? (
                                <LeadStatusSelect
                                    fieldName="bulk-status"
                                    fieldLabel="Select Status"
                                    studyUUID={studyFilterUUID}
                                    leadUUID={selectedRows[0].uuid}
                                    hasSubFields={false}
                                />
                            ) : (
                                <Alert variant="warning">
                                    <AlertDescription>
                                        You must update each status individually because you have not selected a
                                        workflow filter. To select a status for all referrals, add a workflow filter to
                                        your search.
                                    </AlertDescription>
                                </Alert>
                            )}
                        </div>

                        <Separator className="mt-8" />
                    </>
                )}

                {showReferralStatuses && (
                    <div className="mt-8 flex flex-wrap">
                        <Form.List name="referral_statuses">
                            {fields => (
                                <>
                                    {fields.map(field => {
                                        const referralUUID = form.getFieldValue([
                                            'referral_statuses',
                                            field.name,
                                            'referral',
                                        ]);
                                        const referralName = form.getFieldValue([
                                            'referral_statuses',
                                            field.name,
                                            'referral_name',
                                        ]);
                                        const currentStatus = form.getFieldValue([
                                            'referral_statuses',
                                            field.name,
                                            'current_status',
                                        ]);

                                        return (
                                            <div key={`field-${field.key}`} className="mb-4 w-full px-2 sm:w-6/12">
                                                <Form.Item
                                                    key={`referral-${field.key}`}
                                                    name={[field.name, 'referral']}
                                                    hidden
                                                >
                                                    <Input />
                                                </Form.Item>

                                                <LeadStatusSelect
                                                    fieldName={[field.name, 'status']}
                                                    fieldLabel={`${referralName} - Current: ${currentStatus}`}
                                                    studyUUID={studyFilterUUID}
                                                    leadUUID={referralUUID}
                                                    fieldWatcherName={['referral_statuses', field.name, 'status']}
                                                />
                                            </div>
                                        );
                                    })}
                                </>
                            )}
                        </Form.List>
                    </div>
                )}
            </div>
        </div>
    );
};

export default BulkUpdateStatusForm;
