import { DatePicker, Form } from 'antd';
import { useRoute } from 'ziggy-js';

import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';

type AppointmentDateFieldsProps = {
    containerClassNames: string;
    usePortal: boolean;
    fieldPrefix?: string | Array<string | number>;
};

const AppointmentDateFields = ({ containerClassNames, usePortal, fieldPrefix }: AppointmentDateFieldsProps) => {
    const route = useRoute();

    const getFieldName = (fieldName: string) => {
        if (fieldPrefix) {
            return Array.isArray(fieldPrefix) ? [...fieldPrefix, fieldName] : [fieldPrefix, fieldName];
        }
        return fieldName;
    };

    return (
        <>
            <div className={`mb-3 w-full ${containerClassNames}`}>
                <Form.Item
                    name={getFieldName('appointment_date')}
                    label="Appointment Date"
                    rules={[{ required: true, message: 'Please choose a date' }]}
                >
                    <DatePicker
                        className="w-full"
                        format="MM/DD/YYYY h:mm A"
                        showTime={{
                            format: 'h:mm',
                            use12Hours: true,
                            minuteStep: 5,
                        }}
                    />
                </Form.Item>
            </div>
            <div className={`w-full ${containerClassNames}`}>
                <Form.Item shouldUpdate noStyle>
                    {({ getFieldError }) => (
                        <Form.Item
                            name={getFieldName('appointment_type')}
                            label="Appointment Type"
                            rules={[
                                {
                                    required: true,
                                    message: 'Please select a type',
                                },
                            ]}
                        >
                            <ComboSelect
                                async
                                endpoint={route('appointment-types-for-select')}
                                placeholder="Select a type"
                                hasError={!!getFieldError(getFieldName('appointment_type')).length}
                                usePortal={usePortal}
                            />
                        </Form.Item>
                    )}
                </Form.Item>
            </div>
        </>
    );
};

export default AppointmentDateFields;
