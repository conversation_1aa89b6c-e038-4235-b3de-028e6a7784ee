import { useEffect } from 'react';

import { hasIn } from 'lodash';
import md5 from 'md5';

import { FULLSTORY_MASK_CLASS } from '@/constants/fullstory';
import useExecutions from '@/hooks/Referrals/useExecutions';
import { cn } from '@/lib/utils';
import Execution from '@/Pages/Referrals/Partials/Manage/Chatbot/Execution';
import { ANY_TODO } from '@/types/general';
import { ReferralData } from '@/types/referral-data';
import { Alert, AlertDescription, AlertTitle } from '@/UI-Kit/Shadcn/alert';
import { ScrollArea } from '@/UI-Kit/Shadcn/scroll-area';
import { getRandomInt } from '@/utils/helpers';

type ExecutionsProps = {
    compact?: boolean;
    leadData: ReferralData;
    refresh: boolean;
    setRefresh: (refresh: boolean) => void;
};

const Executions = ({ leadData, refresh, setRefresh, compact }: ExecutionsProps) => {
    const { data } = useExecutions(leadData.uuid);
    const strPhoneStatus = data && data?.phoneStatus ? md5(JSON.stringify(data?.phoneStatus)) : '';

    useEffect(() => {
        if (
            hasIn(leadData, 'phoneStatus') &&
            hasIn(data, 'phoneStatus') &&
            md5(JSON.stringify(leadData.phoneStatus)) !== md5(JSON.stringify(data?.phoneStatus))
        ) {
            setRefresh(!refresh);
        }
    }, [strPhoneStatus]);

    return (
        <ScrollArea
            className={cn('chat-bot-cont h-96', FULLSTORY_MASK_CLASS, { 'h-80': compact })}
            viewportClassName={cn(
                { 'flex items-center justify-center': !data || data.executions?.length === 0 },
                FULLSTORY_MASK_CLASS
            )}
        >
            {data && data.executions?.length > 0 ? (
                data.executions.map((execution: ANY_TODO) => (
                    <div key={`chat-bot-cont-${getRandomInt()}`}>
                        <Execution leadData={leadData} executionId={execution.sid} />
                    </div>
                ))
            ) : (
                <div className="mx-7">
                    <Alert variant="info">
                        <AlertTitle>No Automated Chatbot</AlertTitle>
                        <AlertDescription>
                            An automated chatbot flow was not enabled for the campaign form this lead submitted.
                        </AlertDescription>
                    </Alert>
                </div>
            )}
        </ScrollArea>
    );
};

export default Executions;
