import { Pencil, Trash2 } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { usePage } from '@inertiajs/react';

import RowActionsDropdownTrigger from '@/Components/CommonButtons/RowActionsDropdownTrigger';
import ConfirmDialog from '@/Components/Modals/ConfirmDialog';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import { useManageReferralContext } from '@/Contexts/ManageReferralContext';
import { useModalContext } from '@/Contexts/ModalContext';
import NoteForm from '@/Pages/Referrals/Partials/Manage/NoteForm';
import { ActionsRendererProps } from '@/types/ag-grid';
import { PageProps } from '@/types/general';
import Modal from '@/UI-Kit/Modal';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem } from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess } from '@/utils/auth';

const LeadCorrespondenceRowActions = ({ data }: ActionsRendererProps) => {
    const route = useRoute();
    const { auth } = usePage<PageProps>().props;
    const { setShowModal, setModalData, setModalComponent } = useModalContext();
    const { setRefreshLeadStatusHistoryTable } = useManageReferralContext();
    const hasActions =
        canAccess(['editReferralNote', 'deleteReferralNote'], auth.can) && data.user_uuid === auth.user.uuid;

    return (
        <>
            {hasActions && (
                <DropdownMenu modal={false}>
                    <RowActionsDropdownTrigger />

                    <DropdownMenuContent align="end">
                        {canAccess(['editReferralNote'], auth.can) && data.user_uuid === auth.user.uuid && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="edit-note"
                                    key="edit-note"
                                    onClick={() => {
                                        setModalData({ data, isEdit: true });
                                        setModalComponent(
                                            <ModalContentWrapper title="Edit Note" size="3xl">
                                                <NoteForm
                                                    onFinish={() => {
                                                        setRefreshLeadStatusHistoryTable(true);
                                                    }}
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Pencil size={16} />
                                    Edit Note
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}

                        {data.user_uuid === auth.user.uuid && canAccess(['deleteReferralNote'], auth.can) && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="delete-note-button"
                                    key="delete-note"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    confirmData={data}
                                                    color="red"
                                                    content="Are you sure you want to delete this note?"
                                                    dialogType="delete"
                                                    endpoint={route('lead-correspondences.destroy', {
                                                        leadCorrespondence: data.uuid,
                                                    })}
                                                    iconProps={{ name: 'Trash2' }}
                                                    okText="Delete Note"
                                                    title="Delete Note"
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Trash2 className="h-4 w-4!" /> Delete Note
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

export default LeadCorrespondenceRowActions;
