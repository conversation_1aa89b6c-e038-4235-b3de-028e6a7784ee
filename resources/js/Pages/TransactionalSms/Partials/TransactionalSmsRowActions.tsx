import { ClipboardPaste, Pencil, Trash2 } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { usePage } from '@inertiajs/react';

import RowActionsDropdownTrigger from '@/Components/CommonButtons/RowActionsDropdownTrigger';
import ConfirmDialog from '@/Components/Modals/ConfirmDialog';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import { useModalContext } from '@/Contexts/ModalContext';
import { ActionsRendererProps } from '@/types/ag-grid';
import { PageProps } from '@/types/general';
import Modal from '@/UI-Kit/Modal';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLinkItem,
} from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess } from '@/utils/auth';

const TransactionalSmsRowActions = ({ data }: ActionsRendererProps) => {
    const route = useRoute();
    const { auth } = usePage<PageProps>().props;
    const { setShowModal, setModalData, setModalComponent } = useModalContext();
    const hasActions = canAccess(['editTransactionalSms', 'createTransactionalSms'], auth.can);

    return (
        <>
            {hasActions && (
                <DropdownMenu modal={false}>
                    <RowActionsDropdownTrigger />

                    <DropdownMenuContent align="end">
                        {canAccess(['editTransactionalSms'], auth.can) && (
                            <DropdownMenuLinkItem
                                key="edit-sms-action"
                                href={route('transactional-sms.edit', data.uuid)}
                            >
                                <Pencil size={16} />
                                Edit SMS
                            </DropdownMenuLinkItem>
                        )}

                        {canAccess(['createTransactionalSms'], auth.can) && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="duplicate-transactional-sms"
                                    key="duplicate-transactional-sms"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    confirmData={data}
                                                    color="red"
                                                    content="Are you sure you want to duplicate this transactional SMS?"
                                                    dialogType="confirm"
                                                    endpoint={route('transactional-sms.duplicate', {
                                                        transactional_sms: data.uuid,
                                                    })}
                                                    iconProps={{ name: 'Copy' }}
                                                    okText="Duplicate SMS"
                                                    title="Duplicate SMS"
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <ClipboardPaste className="h-4 w-4!" /> Duplicate SMS
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}

                        {canAccess(['deleteTransactionalSms'], auth.can) && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="delete-sms-button"
                                    key="delete-sms-action"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    confirmData={data}
                                                    color="red"
                                                    content="Are you sure you want to delete this transactional SMS?"
                                                    dialogType="delete"
                                                    endpoint={route('transactional-sms.destroy', {
                                                        transactional_sms: data.uuid,
                                                    })}
                                                    iconProps={{ name: 'Trash2' }}
                                                    title="Delete SMS"
                                                    okText="Delete SMS"
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Trash2 className="h-4 w-4!" /> Delete SMS
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

export default TransactionalSmsRowActions;
