import { Pencil, Trash2 } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { usePage } from '@inertiajs/react';

import RowActionsDropdownTrigger from '@/Components/CommonButtons/RowActionsDropdownTrigger';
import ConfirmDialog from '@/Components/Modals/ConfirmDialog';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import { useModalContext } from '@/Contexts/ModalContext';
import EditSiteStatusForm from '@/Pages/SiteStatuses/Partials/Form/EditSiteStatusForm';
import { ActionsRendererProps } from '@/types/ag-grid';
import { PageProps } from '@/types/general';
import Modal from '@/UI-Kit/Modal';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem } from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess } from '@/utils/auth';

const SiteStatusRowActions = ({ data }: ActionsRendererProps) => {
    const route = useRoute();
    const { auth } = usePage<PageProps>().props;
    const { setShowModal, setModalData, setModalComponent } = useModalContext();
    const hasActions = canAccess(['editSiteStatus', 'deleteSiteStatus'], auth.can);

    return (
        <>
            {hasActions && (
                <DropdownMenu modal={false}>
                    <RowActionsDropdownTrigger />

                    <DropdownMenuContent align="end">
                        {canAccess(['editSiteStatus'], auth.can) && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="edit-site-status"
                                    key="edit-site-status"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper title="Edit Site Status">
                                                <EditSiteStatusForm />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Pencil size={16} />
                                    Edit Site Status
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}

                        {canAccess(['deleteSiteStatus'], auth.can) && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="delete-site-status-button"
                                    key="delete-site-status"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    confirmData={data}
                                                    color="red"
                                                    content="Are you sure you want to delete this site status?"
                                                    dialogType="delete"
                                                    endpoint={route('admin.site-statuses.destroy', {
                                                        siteStatus: data.uuid,
                                                    })}
                                                    iconProps={{ name: 'Trash2' }}
                                                    okText="Delete Site Status"
                                                    title="Delete Site Status"
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Trash2 className="h-4 w-4!" /> Delete Site Status
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

export default SiteStatusRowActions;
