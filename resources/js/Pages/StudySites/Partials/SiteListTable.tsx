import { CirclePlus, SlidersHorizontal } from 'lucide-react';

import { usePage } from '@inertiajs/react';

import AgGridTable from '@/Components/AgGrid/AgGridTable';
import BottomTablePagination from '@/Components/AgGrid/Partials/BottomTablePagination';
import TopTablePagination from '@/Components/AgGrid/Partials/TopTablePagination';
import ExportDropdownItem from '@/Components/CommonButtons/ExportDropdownMenuItem';
import TableAndPageActionsDropdownTrigger from '@/Components/CommonButtons/TableAndPageActionsDropdownTrigger';
import { useAgGridTableContext } from '@/Contexts/AgGridTableContext';
import { useSiteBulkActionsContext } from '@/Contexts/SiteBulkActionsContext';
import { useSiteFormContext } from '@/Contexts/SiteFormContext';
import useSiteListGridOptions from '@/hooks/AgGrid/gridOptions/useSiteListGridOptions';
import { PageProps } from '@/types/general';
import { StudyData } from '@/types/study-data';
import { ManageStudy } from '@/types/study-manage';
import { Card, CardAction, CardContent, CardHeader, CardTitle } from '@/UI-Kit/Shadcn/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem } from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess } from '@/utils/auth';

const SiteListTable = () => {
    const { auth, entity } = usePage<PageProps & { entity: StudyData }>().props;
    const { siteListGridOptions } = useSiteListGridOptions();

    return (
        <>
            {canAccess(['viewSite'], auth.can) && (
                <Card>
                    <CardHeader>
                        <CardTitle>Sites</CardTitle>
                        <CardAction>
                            <BulkActions />
                            <HeaderActions />
                        </CardAction>
                    </CardHeader>
                    <TopTablePagination />
                    <CardContent variant="noPadding">
                        <AgGridTable
                            customAgTableState={{
                                addToQueryString: { study: entity.uuid },
                                editUrl: 'admin.sites.update',
                            }}
                            customGridOptions={{ ...siteListGridOptions(), domLayout: 'autoHeight' }}
                        />
                    </CardContent>
                    <BottomTablePagination />
                </Card>
            )}
        </>
    );
};

const BulkActions = () => {
    const { auth } = usePage<PageProps & { entity: StudyData }>().props;
    const { setIsSheetOpen, setShowBulkStatus } = useSiteBulkActionsContext();
    const { agTableState } = useAgGridTableContext();
    const selectedKeys = agTableState?.selectedRows?.map(row => row.uuid) || [];
    const canEditSites = canAccess(['editSite'], auth.can);

    return (
        <>
            {canEditSites && (
                <DropdownMenu>
                    <TableAndPageActionsDropdownTrigger
                        dropdownLabel="Bulk Actions"
                        disabled={selectedKeys.length === 0 || !canEditSites}
                        buttonProps={{ buttonType: 'outline', color: 'gray' }}
                    />
                    <DropdownMenuContent align="start" side="bottom">
                        <DropdownMenuItem
                            disabled={selectedKeys.length === 0}
                            onClick={() => {
                                setShowBulkStatus(true);
                                setIsSheetOpen(true);
                            }}
                        >
                            <SlidersHorizontal className="h-4 !w-4" /> Update Status
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

const HeaderActions = () => {
    const { auth, exportKeys } = usePage<PageProps<{ exportKeys: ManageStudy['exportKeys'] }>>().props;
    const { agTableState } = useAgGridTableContext();
    const { setIsEdit, setOpen, setSite } = useSiteFormContext();
    const canExportSites = canAccess(['exportSite'], auth.can);
    const canCreateSites = canAccess(['createSite'], auth.can);
    const tableHasData = agTableState && !agTableState.isTableEmpty;
    const hasActions = canCreateSites || (tableHasData && canExportSites);

    return (
        <>
            {hasActions && (
                <DropdownMenu modal={false}>
                    <TableAndPageActionsDropdownTrigger />

                    <DropdownMenuContent align="start" side="left">
                        {canCreateSites && (
                            <DropdownMenuItem
                                onClick={() => {
                                    setSite(undefined);
                                    setIsEdit(false);
                                    setOpen(true);
                                }}
                            >
                                <CirclePlus className="h-4! w-4!" /> Add Site
                            </DropdownMenuItem>
                        )}

                        {agTableState && !agTableState.isTableEmpty && canExportSites && (
                            <ExportDropdownItem
                                exportKey={exportKeys.siteExportKey}
                                label="Export Sites"
                                moduleName={agTableState.moduleName}
                                requestOptions={agTableState.addToQueryString}
                            />
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

export default SiteListTable;
