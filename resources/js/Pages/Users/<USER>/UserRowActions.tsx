import { FileText, Pencil, Refresh<PERSON>cw, Repeat, Trash2 } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { router, usePage } from '@inertiajs/react';

import RowActionsDropdownTrigger from '@/Components/CommonButtons/RowActionsDropdownTrigger';
import ConfirmDialog from '@/Components/Modals/ConfirmDialog';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import { useModalContext } from '@/Contexts/ModalContext';
import { ActionsRendererProps } from '@/types/ag-grid';
import { PageProps } from '@/types/general';
import Modal from '@/UI-Kit/Modal';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLinkItem,
    DropdownMenuSeparator,
} from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess } from '@/utils/auth';

const UserRowActions = ({ data }: ActionsRendererProps) => {
    const route = useRoute();
    const { auth } = usePage<PageProps>().props;
    const hasActions = canAccess(
        ['editUser', 'viewUser', 'restoreUser', 'deleteUser', 'loginAsUser', 'manageEmailSuppressionList'],
        auth.can
    );
    const { setShowModal, setModalData, setModalComponent } = useModalContext();
    const canDisableTwoFactorAuth = canAccess(['disableTwoFactorAuth'], auth.can) && data.twoFactorEnabled;
    const canDeleteUser = canAccess(['deleteUser'], auth.can) && data.isActive;

    return (
        <>
            {hasActions && (
                <DropdownMenu modal={false}>
                    <RowActionsDropdownTrigger />

                    <DropdownMenuContent align="end">
                        {canAccess(['editUser'], auth.can) && data.isActive && (
                            <DropdownMenuLinkItem
                                key="edit-user"
                                data-testid="edit-user"
                                href={route('admin.users.edit', data.uuid)}
                            >
                                <Pencil size={16} /> Edit User
                            </DropdownMenuLinkItem>
                        )}

                        {canAccess(['viewUser'], auth.can) && data.isActive && (
                            <DropdownMenuLinkItem
                                data-testid="manage-user-button"
                                key="manage-user"
                                href={route('admin.users.show', data.uuid)}
                            >
                                <FileText className="h-4 w-4!" /> Manage User
                            </DropdownMenuLinkItem>
                        )}

                        {canAccess(['restoreUser'], auth.can) && !data.isActive && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="restore-user-button"
                                    key="restore-user"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    content="Are you sure you want to reactivate this user?"
                                                    dialogType="confirm"
                                                    endpoint={route('admin.users.reactivate', {
                                                        userWithTrashed: data.uuid,
                                                    })}
                                                    iconProps={{ name: 'RefreshCcw' }}
                                                    okText="Reactivate User"
                                                    title="Reactivate User"
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <RefreshCcw className="h-4 w-4!" /> Reactivate User
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}

                        {canAccess(['manageEmailSuppressionList'], auth.can) && data.email_suppression === true && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    key="remove-suppression"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    content="Are you sure you want to remove this user from the suppression list?"
                                                    dialogType="confirm"
                                                    endpoint={route('admin.users.remove-suppression', {
                                                        user: data.uuid,
                                                    })}
                                                    iconProps={{ name: 'Trash2' }}
                                                    okText="Remove"
                                                    title="Remove from Suppression List"
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Trash2 className="h-4 w-4!" /> Remove from Suppression List
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}

                        {canAccess(['loginAsUser'], auth.can) && data.uuid !== auth.user.uuid && data.isActive && (
                            <DropdownMenuItem
                                data-testid="impersonate-user-button"
                                key="impersonate-user"
                                onClick={() => router.post(route('admin.user-impersonation.store', data.uuid))}
                            >
                                <Repeat className="h-4 w-4!" /> Login As
                            </DropdownMenuItem>
                        )}

                        {/* Only show the divider if the auth user can delete the user and disable 2FA */}
                        {canDisableTwoFactorAuth && canDeleteUser && <DropdownMenuSeparator />}

                        {canDisableTwoFactorAuth && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="disable-2fa-button"
                                    key="disable-2fa"
                                    className="text-red-500"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    color="red"
                                                    content="Are you sure you want to disable 2FA for this user?"
                                                    dialogType="delete"
                                                    endpoint={route('admin.two-factor.destroy', data.uuid)}
                                                    iconProps={{ name: 'RefreshCcw' }}
                                                    okText="Disable 2FA"
                                                    title="Disable 2FA"
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <RefreshCcw className="h-4 w-4!" /> Disable 2FA
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}

                        {canDeleteUser && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="delete-user-button"
                                    key="delete-user"
                                    className="text-red-500"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    confirmData={data}
                                                    color="red"
                                                    content="Are you sure you want to disable this user?"
                                                    dialogType="delete"
                                                    endpoint={route('admin.users.destroy', { user: data.uuid })}
                                                    iconProps={{ name: 'Trash2' }}
                                                    okText="Disable User"
                                                    title="Disable User"
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Trash2 className="h-4 w-4!" /> Disable User
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

export default UserRowActions;
