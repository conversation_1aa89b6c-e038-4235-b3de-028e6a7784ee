import { type JSX, useEffect, useState } from 'react';

import { Form, Input } from 'antd';
import { useRoute } from 'ziggy-js';

import { useFilterFormContext } from '@/Components/Filters/context/FilterFormContext';
import { useAgGridTableContext } from '@/Contexts/AgGridTableContext';
import useAxiosClient from '@/hooks/useAxiosClient';
import { ComboSelect } from '@/UI-Kit/Shadcn/combo-select';
import { ComboSelectOption, SingleMultiComboSelectOption } from '@/UI-Kit/Shadcn/types/combobox-select';

const BulkUpdateSitesForm = (): JSX.Element => {
    const route = useRoute();
    const { filterData } = useFilterFormContext();
    const studyUUID = filterData?.formValues?.study.value;
    const { axiosClient } = useAxiosClient();
    const [sitesOptions, setSitesOptions] = useState<ComboSelectOption[]>([]);
    const form = Form.useFormInstance();
    const { agTableState } = useAgGridTableContext();
    const selectedRows = agTableState?.selectedRows ?? [];
    const bulkSites = Form.useWatch('bulk-sites');

    useEffect(() => {
        async function getSitesOptions() {
            const { items }: { items: ComboSelectOption[] } = await axiosClient().get(route('sites-for-select'), {
                params: { studies: studyUUID, limit: 100000 },
            });

            setSitesOptions(items);
        }

        getSitesOptions();
    }, []);

    useEffect(() => {
        const newUsers = selectedRows.map(user => ({
            user: user.uuid,
            user_name: `${user.first_name} ${user.last_name}`,
            sites: bulkSites,
        }));

        form.setFieldsValue({ user_sites: [...newUsers] });
    }, [JSON.stringify(bulkSites)]);

    return (
        <div className="relative">
            <div>
                <Form.Item name="study_uuid" key="study_uuid" hidden>
                    <Input />
                </Form.Item>

                <Form.Item shouldUpdate noStyle>
                    {({ getFieldError }) => (
                        <div className="w-full px-2">
                            <Form.Item name="bulk-sites" label="Change all selections to sites:" style={{ zIndex: 2 }}>
                                <ComboSelect
                                    options={sitesOptions}
                                    isMulti
                                    isClearable
                                    placeholder="Select Sites"
                                    value={bulkSites as SingleMultiComboSelectOption}
                                    onChange={selected => form.setFieldsValue({ 'bulk-sites': selected })}
                                    hasError={!!getFieldError('bulk-sites').length}
                                />
                            </Form.Item>
                        </div>
                    )}
                </Form.Item>

                {form.getFieldValue('bulk-sites') && (
                    <>
                        <div className="px-2">
                            <hr className="mt-8 mb-6" />
                        </div>

                        <div className="flex flex-wrap">
                            <Form.List name="user_sites">
                                {fields => (
                                    <>
                                        {fields.map(field => {
                                            const userName = form.getFieldValue([
                                                'user_sites',
                                                field.name,
                                                'user_name',
                                            ]);

                                            return (
                                                <div key={`field-${field.key}`} className="w-full px-2 sm:w-6/12">
                                                    <Form.Item
                                                        key={`user-${field.key}`}
                                                        name={[field.name, 'user']}
                                                        hidden
                                                    >
                                                        <Input />
                                                    </Form.Item>

                                                    <Form.Item shouldUpdate noStyle>
                                                        {({ getFieldError }) => (
                                                            <Form.Item
                                                                key={`sites-${field.key}`}
                                                                name={[field.name, 'sites']}
                                                                label={`${userName}`}
                                                                style={{ zIndex: 2 }}
                                                            >
                                                                <ComboSelect
                                                                    options={sitesOptions}
                                                                    isMulti
                                                                    isClearable
                                                                    placeholder="Select Sites"
                                                                    value={form.getFieldValue([field.name, 'sites'])}
                                                                    onChange={selected =>
                                                                        form.setFieldsValue({
                                                                            [`user_sites.${field.name}.sites`]:
                                                                                selected,
                                                                        })
                                                                    }
                                                                    hasError={
                                                                        !!getFieldError([field.name, 'sites']).length
                                                                    }
                                                                />
                                                            </Form.Item>
                                                        )}
                                                    </Form.Item>
                                                </div>
                                            );
                                        })}
                                    </>
                                )}
                            </Form.List>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
};

export default BulkUpdateSitesForm;
