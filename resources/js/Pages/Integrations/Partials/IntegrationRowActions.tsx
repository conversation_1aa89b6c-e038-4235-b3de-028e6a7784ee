import { Pencil } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { usePage } from '@inertiajs/react';

import RowActionsDropdownTrigger from '@/Components/CommonButtons/RowActionsDropdownTrigger';
import { ActionsRendererProps } from '@/types/ag-grid';
import { PageProps } from '@/types/general';
import { DropdownMenu, DropdownMenuContent, DropdownMenuLinkItem } from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess } from '@/utils/auth';

const IntegrationRowActions = ({ data }: ActionsRendererProps) => {
    const route = useRoute();
    const { auth } = usePage<PageProps>().props;
    const hasActions = canAccess(['editIntegrationOption'], auth.can);

    return (
        <>
            {hasActions && (
                <DropdownMenu modal={false}>
                    <RowActionsDropdownTrigger />

                    <DropdownMenuContent align="end">
                        {canAccess(['editIntegrationOption'], auth.can) && (
                            <DropdownMenuLinkItem
                                data-testid="edit-integration"
                                key="edit-integration-action"
                                href={route('admin.integrations.edit', data.uuid)}
                            >
                                <Pencil size={16} /> Edit Integration
                            </DropdownMenuLinkItem>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

export default IntegrationRowActions;
