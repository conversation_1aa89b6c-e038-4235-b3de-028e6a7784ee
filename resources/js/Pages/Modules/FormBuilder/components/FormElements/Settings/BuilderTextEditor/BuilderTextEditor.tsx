import { useCallback, useMemo } from 'react';

import { debounce } from 'lodash';

import { Editor as CoreEditor } from '@tiptap/core';

import TipTapEditor from '@/Components/TipTapEditor';
import { sanitizeContent } from '@/Components/TipTapEditor/utils/sanitizeContent';
import { textEditorExtensions } from '@/Pages/Modules/FormBuilder/components/FormElements/Settings/BuilderTextEditor/extensions';
import { Prose } from '@/Pages/Modules/FormBuilder/components/FormElements/Settings/BuilderTextEditor/Prose';
import { ANY_TODO } from '@/types/general';
import { Card } from '@/UI-Kit/Shadcn/card';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/UI-Kit/Shadcn/form';

const BuilderTextEditor = ({ fieldName }: { fieldName: string }) => (
    <FormField
        name={fieldName}
        render={({ field }: ANY_TODO) => (
            <FormItem className="gap-y-3">
                <FormLabel className="col-span-12 font-semibold">Content</FormLabel>
                <FormMessage className="col-span-12 -mt-1 rounded-t-md bg-red-500 p-2 text-white" />
                <FormDescription className="col-span-12 text-black">
                    Enter the content that will be displayed to users when they view this form element.
                </FormDescription>
                <Card
                    className="group col-span-12 min-h-[200px] pt-1 focus-within:border-black"
                    data-testid="email-editor-container"
                >
                    <Prose className="form-builder-text-editor">
                        <FormControl>
                            <OptimizedTipTapEditor initialContent={field.value} onChange={field.onChange} />
                        </FormControl>
                    </Prose>
                </Card>
            </FormItem>
        )}
    />
);

const OptimizedTipTapEditor = ({
    initialContent,
    onChange,
}: {
    initialContent: ANY_TODO;
    onChange: (content: ANY_TODO) => void;
}) => {
    const staticContent = useMemo(() => initialContent, []);

    const debouncedOnChange = useCallback(
        debounce((content: ANY_TODO) => {
            onChange(content);
        }, 1000),
        [onChange]
    );

    return (
        <TipTapEditor
            content={staticContent}
            extensions={textEditorExtensions}
            countWrapperClass="rounded-b-lg"
            toolbarParentClass="top-[0px]"
            tighterLayout
            onChangeContent={(output: ANY_TODO, editor: CoreEditor) => {
                const isEmptyContent = editor.isEmpty;

                debouncedOnChange(isEmptyContent ? '' : sanitizeContent(output.json));
            }}
        />
    );
};

export default BuilderTextEditor;
