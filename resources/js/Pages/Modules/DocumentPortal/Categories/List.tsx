import { CirclePlus, MoreHorizontal } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { Head, usePage } from '@inertiajs/react';

import FilterFormProvider from '@/Components/Filters/context/FilterFormContext';
import FilterBy from '@/Components/Filters/Partials/FilterBy';
import FilterOpener from '@/Components/Filters/Partials/FilterOpener';
import SlideOverFilters from '@/Components/Filters/Partials/SlideOverFilters';
import ModalComponent from '@/Components/Modals/ModalComponent';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import AgGridTableProvider from '@/Contexts/AgGridTableContext';
import { useModalContext } from '@/Contexts/ModalContext';
import SlideOverFilterProvider from '@/Contexts/SlideOverFilterProvider';
import AdminLayout from '@/Pages/Modules/DocumentPortal/Admin/AdminLayout';
import CategoryListFilter from '@/Pages/Modules/DocumentPortal/Categories/components/CategoryListFilter';
import CategoryListTable from '@/Pages/Modules/DocumentPortal/Categories/components/CategoryListTable';
import CreateCategoryForm from '@/Pages/Modules/DocumentPortal/Categories/components/Form/CreateCategoryForm';
import Header from '@/Pages/Modules/DocumentPortal/Documents/components/Header';
import SidebarNav from '@/Pages/Modules/DocumentPortal/Documents/components/SidebarNav';
import { CurrentPortal } from '@/Pages/Modules/DocumentPortal/Portals/types/portals';
import { PageProps } from '@/types/general';
import Modal from '@/UI-Kit/Modal';
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from '@/UI-Kit/Shadcn/breadcrumb';
import { Button } from '@/UI-Kit/Shadcn/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess } from '@/utils/auth';

const List = () => {
    const { currentPortal } = usePage<PageProps & { currentPortal: CurrentPortal }>().props;
    const moduleName = 'agTableDocumentModel';

    return (
        <SlideOverFilterProvider>
            {({ setIsSheetOpen }) => (
                <AgGridTableProvider moduleName={moduleName}>
                    <Modal>
                        <AdminLayout
                            headValue="Categories"
                            headerComponent={
                                <Header
                                    breadcrumbs={<PageBreadcrumbs />}
                                    rightContent={
                                        <div className="flex items-center gap-x-2">
                                            <HeaderActions />
                                            <FilterOpener key="show-filter-btn" onClick={() => setIsSheetOpen(true)} />
                                        </div>
                                    }
                                />
                            }
                            sidebarComponent={<SidebarNav />}
                            showPortalList
                        >
                            <Head title={`${currentPortal.name} - Categories`} />
                            <FilterFormProvider close={() => setIsSheetOpen(false)}>
                                <div className="flex flex-col gap-y-6 px-8">
                                    {/* Active Filters */}
                                    <div className="flex items-center gap-x-2 px-1">
                                        <FilterBy />
                                    </div>

                                    {/* Table Section */}
                                    <CategoryListTable />

                                    <SlideOverFilters>
                                        <CategoryListFilter />
                                    </SlideOverFilters>
                                </div>
                            </FilterFormProvider>
                        </AdminLayout>
                        <ModalComponent />
                    </Modal>
                </AgGridTableProvider>
            )}
        </SlideOverFilterProvider>
    );
};

const PageBreadcrumbs = () => {
    const { currentPortal } = usePage<PageProps & { currentPortal: CurrentPortal }>().props;
    const route = useRoute();

    return (
        <Breadcrumb>
            <BreadcrumbList>
                <BreadcrumbItem>
                    <BreadcrumbLink href={route('document-portal.admin.index')}>Document Portal</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                    <BreadcrumbLink href={route('document-portal.admin.documents.index')}>
                        {currentPortal.name} - Documents
                    </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                    <BreadcrumbPage>Categories</BreadcrumbPage>
                </BreadcrumbItem>
            </BreadcrumbList>
        </Breadcrumb>
    );
};

const HeaderActions = () => {
    const { auth } = usePage<PageProps>().props;
    const { setShowModal, setModalComponent } = useModalContext();
    const hasActions = canAccess(['createDocumentCategory'], auth.can);

    if (!hasActions) {
        return null;
    }

    return (
        <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                    <MoreHorizontal className="h-4 w-4" />
                    <span className="sr-only">Open menu</span>
                </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent align="end">
                <Modal.Trigger>
                    <DropdownMenuItem
                        data-on-data="create-category"
                        key="create-category"
                        onClick={() => {
                            setModalComponent(
                                <ModalContentWrapper title="Create Category">
                                    <CreateCategoryForm />
                                </ModalContentWrapper>
                            );
                            setShowModal(true);
                        }}
                    >
                        <CirclePlus className="h-4 w-4!" />
                        Create Category
                    </DropdownMenuItem>
                </Modal.Trigger>
            </DropdownMenuContent>
        </DropdownMenu>
    );
};

export default List;
