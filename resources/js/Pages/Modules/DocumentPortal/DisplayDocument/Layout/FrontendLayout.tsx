import { useEffect } from 'react';

import { ThemeProvider } from 'next-themes';

import { usePage } from '@inertiajs/react';

import '@/Pages/Modules/DocumentPortal/DisplayDocument/Layout/styles/prism.css';
import '@/Pages/Modules/DocumentPortal/DisplayDocument/css/frontend.css';

import { cn } from '@/lib/utils';
import { Category } from '@/Pages/Modules/DocumentPortal/Categories/types/categories';
import { Hero } from '@/Pages/Modules/DocumentPortal/DisplayDocument/Layout/Hero';
import { PageHeader } from '@/Pages/Modules/DocumentPortal/DisplayDocument/Layout/PageHeader';
import { MenuSidebar } from '@/Pages/Modules/DocumentPortal/DisplayDocument/Layout/Sidebar/MenuSidebar';
import { OnThisPageSidebar } from '@/Pages/Modules/DocumentPortal/DisplayDocument/Layout/Sidebar/OnThisPageSidebar';
import { SearchData, setSearchIndex } from '@/Pages/Modules/DocumentPortal/DisplayDocument/lib/search';
import { type Document } from '@/Pages/Modules/DocumentPortal/Documents/types/documents';
import { ANY_TODO } from '@/types/general';
import { SidebarInset, SidebarProvider } from '@/UI-Kit/Shadcn/sidebar';

const FrontendLayout = ({ children }: ANY_TODO) => {
    const { searchData, document, category } = usePage<{
        document: Document;
        searchData: SearchData[];
        category?: Category;
    }>().props;

    const isHomepage = !document && !category;

    useEffect(() => {
        setSearchIndex(searchData);
    }, []);

    return (
        <ThemeProvider defaultTheme="dark" storageKey="doc-portal-theme" attribute="class" disableTransitionOnChange>
            <div className="bg-slate-50 [--header-height:calc(--spacing(16))] dark:bg-slate-900 dark:bg-[radial-gradient(ellipse_at_top,var(--tw-gradient-stops))] dark:from-slate-700 dark:from-0% dark:to-slate-900 dark:to-40%">
                <SidebarProvider
                    disableKeyboardShortcut
                    className="flex flex-col"
                    style={{ '--sidebar-width': '20rem' } as React.CSSProperties}
                >
                    <PageHeader />

                    <div className="flex flex-1">
                        {!isHomepage && <MenuSidebar />}

                        <SidebarInset
                            className={cn(
                                'gap-y-10 bg-slate-50 py-10 dark:bg-transparent dark:text-white',
                                isHomepage ? 'mx-auto max-w-7xl px-10 lg:px-40' : 'px-10 lg:px-20'
                            )}
                        >
                            {(isHomepage || category) && <Hero />}
                            {children}
                        </SidebarInset>

                        <OnThisPageSidebar />
                    </div>
                </SidebarProvider>
            </div>
        </ThemeProvider>
    );
};

export default FrontendLayout;
