import { useRoute } from 'ziggy-js';

import { Head, usePage } from '@inertiajs/react';

import ModalComponent from '@/Components/Modals/ModalComponent';
import TipTapEditorProvider from '@/Components/TipTapEditor/context/TipTapEditorContext';
import { sanitizeContent } from '@/Components/TipTapEditor/utils/sanitizeContent';
import FullPageLayout from '@/Layouts/FullPage/FullPageLayout';
import DocumentEditor from '@/Pages/Modules/DocumentPortal/Documents/components/DocumentEditor';
import DocumentFormWrapper from '@/Pages/Modules/DocumentPortal/Documents/components/DocumentFormWrapper';
import EditorHeader from '@/Pages/Modules/DocumentPortal/Documents/components/EditorHeader';
import EditorSidebar from '@/Pages/Modules/DocumentPortal/Documents/components/EditorSidebar';
import HeaderActions from '@/Pages/Modules/DocumentPortal/Documents/components/HeaderActions';
import { documentEditFormSchema } from '@/Pages/Modules/DocumentPortal/Documents/schemas/documents';
import {
    Document,
    DocumentEditFormValues,
    DocumentStatus,
} from '@/Pages/Modules/DocumentPortal/Documents/types/documents';
import { CurrentPortal } from '@/Pages/Modules/DocumentPortal/Portals/types/portals';
import { RoleWithAbilities } from '@/Pages/Modules/DocumentPortal/Portals/types/roles';
import { UserWithAbilities } from '@/Pages/Modules/DocumentPortal/Portals/types/users';
import { PageProps } from '@/types/general';
import Modal from '@/UI-Kit/Modal';
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from '@/UI-Kit/Shadcn/breadcrumb';
import { SidebarInset, SidebarProvider } from '@/UI-Kit/Shadcn/sidebar';
import { TooltipProvider } from '@/UI-Kit/Shadcn/tooltip';

const Edit = () => {
    const route = useRoute();

    const { currentPortal, document, statuses, rolesWithAbilities, usersWithAbilities } = usePage<
        PageProps & {
            currentPortal: CurrentPortal;
            document: Document;
            statuses: DocumentStatus[];
            rolesWithAbilities: RoleWithAbilities[];
            usersWithAbilities: UserWithAbilities[];
        }
    >().props;

    const editDefaultValues: DocumentEditFormValues = {
        category: {
            label: document.category.name,
            value: document.category.uuid,
        },
        html: document.html,
        json_doc: sanitizeContent(document.json_doc ?? ''),
        parent_document: document.parent?.uuid
            ? {
                  label: document.parent.title,
                  value: document.parent.uuid,
              }
            : undefined,
        roles: rolesWithAbilities ?? [],
        status: {
            label: statuses.find(status => status.value === document.status)?.label ?? document.status,
            value: document.status,
        },
        title: document.title,
        users: usersWithAbilities ?? [],
        uuid: document.uuid,
    };

    return (
        <FullPageLayout>
            <TipTapEditorProvider>
                <DocumentFormWrapper
                    schema={documentEditFormSchema}
                    defaultValues={editDefaultValues}
                    endpoint={route('document-portal.admin.documents.update', document.uuid)}
                >
                    <Head title={`Edit - ${document.title} - ${currentPortal.name}`} />
                    <div id="main-template" className="relative flex min-h-svh flex-col bg-white">
                        <SidebarProvider
                            disableKeyboardShortcut
                            style={
                                {
                                    '--sidebar-width': '25rem',
                                    '--sidebar-width-mobile': '25rem',
                                } as React.CSSProperties
                            }
                        >
                            <TooltipProvider>
                                <Modal>
                                    <SidebarInset>
                                        <EditorHeader
                                            breadcrumbs={<PageBreadcrumbs />}
                                            rightContent={<HeaderActions />}
                                        />
                                        <DocumentEditor />
                                    </SidebarInset>
                                    <EditorSidebar />
                                    <ModalComponent />
                                </Modal>
                            </TooltipProvider>
                        </SidebarProvider>
                    </div>
                </DocumentFormWrapper>
            </TipTapEditorProvider>
        </FullPageLayout>
    );
};

const PageBreadcrumbs = () => {
    const { document, currentPortal } = usePage<
        PageProps & {
            document: Document;
            currentPortal: CurrentPortal;
        }
    >().props;
    const route = useRoute();

    return (
        <Breadcrumb>
            <BreadcrumbList>
                <BreadcrumbItem>
                    <BreadcrumbLink href={route('document-portal.admin.index')}>Document Portal</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                    <BreadcrumbLink href={route('document-portal.admin.documents.index')}>
                        {`${currentPortal.name} - Documents`}
                    </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                    <BreadcrumbPage>{document.title}</BreadcrumbPage>
                </BreadcrumbItem>
            </BreadcrumbList>
        </Breadcrumb>
    );
};

export default Edit;
