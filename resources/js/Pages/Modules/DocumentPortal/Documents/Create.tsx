import { useRoute } from 'ziggy-js';

import { Head, usePage } from '@inertiajs/react';

import ModalComponent from '@/Components/Modals/ModalComponent';
import TipTapEditorProvider from '@/Components/TipTapEditor/context/TipTapEditorContext';
import usePreventNavigation from '@/hooks/usePreventNavigation';
import DocumentEditor from '@/Pages/Modules/DocumentPortal/Documents/components/DocumentEditor';
import DocumentFormWrapper from '@/Pages/Modules/DocumentPortal/Documents/components/DocumentFormWrapper';
import EditorHeader from '@/Pages/Modules/DocumentPortal/Documents/components/EditorHeader';
import EditorSidebar from '@/Pages/Modules/DocumentPortal/Documents/components/EditorSidebar';
import HeaderActions from '@/Pages/Modules/DocumentPortal/Documents/components/HeaderActions';
import { documentCreateFormSchema } from '@/Pages/Modules/DocumentPortal/Documents/schemas/documents';
import { DocumentCreateFormValues } from '@/Pages/Modules/DocumentPortal/Documents/types/documents';
import { CurrentPortal } from '@/Pages/Modules/DocumentPortal/Portals/types/portals';
import { RoleWithAbilities } from '@/Pages/Modules/DocumentPortal/Portals/types/roles';
import { UserWithAbilities } from '@/Pages/Modules/DocumentPortal/Portals/types/users';
import { PageProps } from '@/types/general';
import Modal from '@/UI-Kit/Modal';
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from '@/UI-Kit/Shadcn/breadcrumb';
import { SidebarInset, SidebarProvider } from '@/UI-Kit/Shadcn/sidebar';
import { TooltipProvider } from '@/UI-Kit/Shadcn/tooltip';

const Create = () => {
    usePreventNavigation();
    const route = useRoute();

    const { currentPortal, rolesWithAbilities, usersWithAbilities } = usePage<
        PageProps & {
            currentPortal: CurrentPortal;
            rolesWithAbilities: RoleWithAbilities[];
            usersWithAbilities: UserWithAbilities[];
        }
    >().props;

    const createDefaultValues: DocumentCreateFormValues = {
        category: { label: 'Uncategorized', value: '' },
        html: '',
        json_doc: {
            type: 'doc',
            content: [{ type: 'paragraph', content: [] }],
        },
        parent_document: undefined,
        roles: rolesWithAbilities ?? [],
        status: { label: 'Draft', value: 'draft' },
        title: '',
        users: usersWithAbilities ?? [],
    };

    return (
        <TipTapEditorProvider>
            <DocumentFormWrapper
                schema={documentCreateFormSchema}
                defaultValues={createDefaultValues}
                endpoint={route('document-portal.admin.documents.store')}
            >
                <Head title={`Create Document - ${currentPortal.name}`} />
                <div id="main-template" className="relative flex min-h-svh flex-col bg-white">
                    <SidebarProvider
                        disableKeyboardShortcut
                        style={
                            {
                                '--sidebar-width': '25rem',
                                '--sidebar-width-mobile': '25rem',
                            } as React.CSSProperties
                        }
                    >
                        <TooltipProvider>
                            <Modal>
                                <SidebarInset>
                                    <EditorHeader breadcrumbs={<PageBreadcrumbs />} rightContent={<HeaderActions />} />
                                    <DocumentEditor />
                                </SidebarInset>
                                <EditorSidebar />
                                <ModalComponent />
                            </Modal>
                        </TooltipProvider>
                    </SidebarProvider>
                </div>
            </DocumentFormWrapper>
        </TipTapEditorProvider>
    );
};

const PageBreadcrumbs = () => {
    const { currentPortal } = usePage<PageProps & { currentPortal: CurrentPortal }>().props;
    const route = useRoute();

    return (
        <Breadcrumb>
            <BreadcrumbList>
                <BreadcrumbItem>
                    <BreadcrumbLink href={route('document-portal.admin.index')}>Document Portal</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                    <BreadcrumbLink href={route('document-portal.admin.documents.index')}>
                        {`${currentPortal.name} - Documents`}
                    </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                    <BreadcrumbPage>Create Document</BreadcrumbPage>
                </BreadcrumbItem>
            </BreadcrumbList>
        </Breadcrumb>
    );
};

export default Create;
