import { Head, usePage } from '@inertiajs/react';

import FullPageLayout from '@/Layouts/FullPage';
import { cn } from '@/lib/utils';
import AdminSidebar from '@/Pages/Modules/DocumentPortal/Admin/components/AdminSidebar';
import { DocPageProps } from '@/Pages/Modules/DocumentPortal/Portals/types/general';
import { useStoreState } from '@/store/hooks';
import { SidebarInset, SidebarProvider } from '@/UI-Kit/Shadcn/sidebar';

const AdminLayout = ({
    children,
    headValue,
    headerComponent,
    sidebarComponent,
    sidebarInsertClassName,
    showPortalList,
}: {
    children: React.ReactNode;
    headValue: string;
    headerComponent: React.ReactNode;
    sidebarComponent: React.ReactNode;
    sidebarInsertClassName?: string;
    showPortalList?: boolean;
}) => {
    const { currentPortal } = usePage<DocPageProps>().props;
    const collapsedSidebar = useStoreState(state => state.appSettingsModel.collapsedSidebar);

    return (
        <FullPageLayout>
            <Head title={currentPortal ? `${headValue} - ${currentPortal.name}` : headValue} />
            <div id="main-template" className="admin-layout relative flex min-h-svh flex-col bg-white">
                <div className="bg-white">
                    <SidebarProvider
                        disableKeyboardShortcut
                        defaultOpen={!collapsedSidebar}
                        style={{
                            // @ts-ignore
                            '--sidebar-width-icon': '3.5rem',
                        }}
                    >
                        <AdminSidebar sidebarComponent={sidebarComponent} showPortalList={showPortalList} />

                        <SidebarInset
                            className={cn('flex w-full flex-col gap-y-8 bg-slate-50/70', sidebarInsertClassName)}
                        >
                            {headerComponent}

                            {children}
                        </SidebarInset>
                    </SidebarProvider>
                </div>
            </div>
        </FullPageLayout>
    );
};

export default AdminLayout;
