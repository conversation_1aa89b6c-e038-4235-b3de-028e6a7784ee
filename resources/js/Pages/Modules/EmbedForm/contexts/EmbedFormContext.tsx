import { createContext, ReactNode, useContext, useEffect, useMemo, useState } from 'react';

import { publicIpv4 } from 'public-ip';
import { toast } from 'sonner';
import { useRoute } from 'ziggy-js';

import { router, usePage } from '@inertiajs/react';

import useAxiosClient from '@/hooks/useAxiosClient';
import { EmbedFormSubmission } from '@/Pages/Modules/EmbedForm/types/embed-form';
import { FormElement } from '@/Pages/Modules/FormBuilder/types/form-elements';
import getCookieValue from '@/Pages/Modules/FormBuilder/utils/get-cookie-value';
import { useStoreActions } from '@/store/hooks';
import { ANY_TODO, PageProps } from '@/types/general';
import { ReferralData } from '@/types/referral-data';

type EmbedFormPageProps = {
    formElements: FormElement[];
    formId: string;
    referral?: ReferralData;
    selectedLanguage: string;
    settings: Record<string, ANY_TODO>;
    trackingId: string;
};

type EmbedFormContextProps = {
    formElements: FormElement[];
    isSubmittingInternally: boolean;
    settings: Record<string, ANY_TODO>;
    trackingId: string;
    onSubmit: (data: EmbedFormSubmission['answers'], isDraft?: boolean) => void;
};

const EmbedFormContext = createContext<EmbedFormContextProps>({
    formElements: [],
    isSubmittingInternally: false,
    settings: {},
    trackingId: '',
    onSubmit: () => {},
});

export const useEmbedFormContext = () => useContext(EmbedFormContext);

interface EmbedFormProviderProps {
    children: ReactNode;
    isSubmittingInternally?: boolean;
    isEdit?: boolean;
}

const EmbedFormProvider = ({ children, isSubmittingInternally = false, isEdit = false }: EmbedFormProviderProps) => {
    const { settings, trackingId, formElements, selectedLanguage, formId, referral } =
        usePage<PageProps<EmbedFormPageProps>>().props;
    const [elements, setElements] = useState<FormElement[]>(formElements);
    const route = useRoute();
    const { setErrorMessage, setIsFormSubmitting } = useStoreActions(actions => actions.embedFormModel);
    const [clientIp, setClientIp] = useState<string>('');
    const { axiosClient } = useAxiosClient();

    useEffect(() => {
        const fetchClientIp = async () => {
            const ip = await publicIpv4({ fallbackUrls: ['https://ifconfig.co/ip'] });
            setClientIp(ip);
        };
        fetchClientIp();
    }, []);

    const onSubmit = async (answers: EmbedFormSubmission['answers'], isDraft = false) => {
        const isTest = new URLSearchParams(window.location.search).get('test') === 'true';

        setIsFormSubmitting(true);

        const formData: EmbedFormSubmission = {
            answers,
            clientIp,
            clientUserAgent: navigator.userAgent,
            fbcCookie: getCookieValue('_fbc'),
            fbpCookie: getCookieValue('_fbp'),
            formId,
            lang: selectedLanguage,
            trackingId,
            is_test: isTest,
            isDraft,
        };

        if (isSubmittingInternally) {
            handleInternalSubmission(formData);
        } else {
            handlePublicSubmission(formData);
        }
    };

    const handleInternalSubmission = async (formData: EmbedFormSubmission) => {
        router.post(
            isEdit
                ? route('screener-responses.update', { leadLimitByUser: referral?.uuid ?? '' })
                : route('screener-responses.store', { leadLimitByUser: referral?.uuid ?? '' }),
            formData,
            {
                preserveScroll: true,
                preserveState: true,
                onSuccess: ({ props }: ANY_TODO) => {
                    const { message } = props.flash;

                    if (message?.original?.success) {
                        toast.success(message.original.message, {
                            description: message.original.description,
                        });
                    }

                    setIsFormSubmitting(false);
                },
                onError: (errors: ANY_TODO) => {
                    const description = Object.values(errors)
                        .map((error: ANY_TODO) => error)
                        .join('\n');

                    toast.error('An error has occurred', {
                        description,
                        duration: 100000,
                    });

                    setIsFormSubmitting(false);
                },
            }
        );
    };

    const handlePublicSubmission = async (formData: EmbedFormSubmission) => {
        await axiosClient()
            .post(route('public.embed-form.store'), formData)
            // eslint-disable-next-line consistent-return
            .then((data: ANY_TODO) => {
                if (!data.success) {
                    setErrorMessage(data.description);
                    setIsFormSubmitting(false);

                    return false;
                }

                // @ts-ignore - Required because typing for window doesn't recognize top
                window.top.location.href = data.redirect ?? 'https://1nhealth.com';
            })
            .catch(() => {
                setErrorMessage('There was an error submitting your form. Please try again.');
                setIsFormSubmitting(false);
            });
    };

    useEffect(() => {
        setElements(formElements);
    }, [formElements]);

    useEffect(() => {
        const script = document.createElement('script');
        script.src = '/lib/iframe-resizer/iframeResizer.contentWindow.min.js';
        script.async = true;
        document.body.appendChild(script);

        return (): void => {
            document.body.removeChild(script);
        };
    }, []);

    useEffect(() => {
        /** Handle custom font if specified */
        if (settings?.general?.customFont?.link) {
            const customFont = document.createElement('link');
            customFont.href = settings.general.customFont.link;
            customFont.rel = 'stylesheet';
            document.body.appendChild(customFont);
        }

        return () => {
            /** Cleanup custom font on unmount */
            if (settings?.general?.customFont?.link) {
                const customFont = document.querySelector(`link[href="${settings.general.customFont.link}"]`);

                if (customFont) {
                    document.body.removeChild(customFont);
                }
            }
        };
    }, [settings]);

    const value = useMemo(
        () => ({
            formElements: elements,
            isSubmittingInternally,
            settings,
            trackingId,
            onSubmit,
        }),
        [elements, isSubmittingInternally, settings, trackingId]
    );

    return <EmbedFormContext.Provider value={value}>{children}</EmbedFormContext.Provider>;
};

export default EmbedFormProvider;
