import { CirclePlus, Pencil } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { router, usePage } from '@inertiajs/react';

import RowActionsDropdownTrigger from '@/Components/CommonButtons/RowActionsDropdownTrigger';
import { ActionsRendererProps } from '@/types/ag-grid';
import { PageProps } from '@/types/general';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLinkItem,
} from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess } from '@/utils/auth';

const IntegrationOptionRowActions = ({ data }: ActionsRendererProps) => {
    const route = useRoute();
    const { auth } = usePage<PageProps>().props;
    const hasActions = canAccess(['editIntegrationOption', 'createIntegrationOption'], auth.can);

    return (
        <>
            {hasActions && (
                <DropdownMenu modal={false}>
                    <RowActionsDropdownTrigger />

                    <DropdownMenuContent align="end">
                        {canAccess(['editIntegrationOption'], auth.can) && data.uuid && (
                            <DropdownMenuLinkItem
                                data-testid="edit-integration-option"
                                key="edit-integration-option-action"
                                href={route('admin.integration-options.edit', data.uuid)}
                            >
                                <Pencil size={16} /> Edit Options
                            </DropdownMenuLinkItem>
                        )}

                        {canAccess(['createIntegrationOption'], auth.can) && !data.uuid && (
                            <DropdownMenuItem
                                data-testid="create-integration-option"
                                key="create-integration-option"
                                onClick={() =>
                                    router.get(
                                        route('admin.integration-options.create', {
                                            integration: data.integration_uuid,
                                        })
                                    )
                                }
                            >
                                <CirclePlus className="h-4 w-4!" /> Add Options
                            </DropdownMenuItem>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

export default IntegrationOptionRowActions;
