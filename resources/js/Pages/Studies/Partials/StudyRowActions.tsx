import { FileText, Pencil, Trash2 } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { usePage } from '@inertiajs/react';

import RowActionsDropdownTrigger from '@/Components/CommonButtons/RowActionsDropdownTrigger';
import ConfirmDialog from '@/Components/Modals/ConfirmDialog';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import { useModalContext } from '@/Contexts/ModalContext';
import { ActionsRendererProps } from '@/types/ag-grid';
import { PageProps } from '@/types/general';
import Modal from '@/UI-Kit/Modal';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLinkItem,
} from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess } from '@/utils/auth';

const StudyRowActions = ({ data }: ActionsRendererProps) => {
    const route = useRoute();
    const { auth } = usePage<PageProps>().props;
    const { setShowModal, setModalData, setModalComponent } = useModalContext();
    const hasActions = canAccess(['editStudy', 'viewStudy', 'deleteSite'], auth.can);

    return (
        <>
            {hasActions && (
                <DropdownMenu modal={false}>
                    <RowActionsDropdownTrigger />

                    <DropdownMenuContent align="end">
                        {canAccess(['editStudy'], auth.can) && (
                            <DropdownMenuLinkItem
                                key="edit-site"
                                data-testid="edit-study-button"
                                href={route('admin.studies.edit', data.uuid)}
                            >
                                <Pencil size={16} /> Edit Study
                            </DropdownMenuLinkItem>
                        )}

                        {canAccess(['viewStudy'], auth.can) && (
                            <DropdownMenuLinkItem
                                key="read-study"
                                data-testid="manage-study-button"
                                href={route('studies.show', data.uuid)}
                            >
                                <FileText className="h-4 w-4!" /> Manage Study
                            </DropdownMenuLinkItem>
                        )}

                        {canAccess(['deleteSite'], auth.can) && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="delete-study-button"
                                    key="delete-study"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    confirmData={data}
                                                    color="red"
                                                    content="Are you sure you want to delete this study?"
                                                    dialogType="delete"
                                                    endpoint={route('admin.studies.destroy', { study: data.uuid })}
                                                    iconProps={{ name: 'Trash2' }}
                                                    okText="Delete Study"
                                                    title="Delete Study"
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Trash2 className="h-4 w-4!" /> Delete Study
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

export default StudyRowActions;
