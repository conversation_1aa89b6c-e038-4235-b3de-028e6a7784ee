import { Ban, Pencil, Trash2 } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { usePage } from '@inertiajs/react';

import RowActionsDropdownTrigger from '@/Components/CommonButtons/RowActionsDropdownTrigger';
import ConfirmDialog from '@/Components/Modals/ConfirmDialog';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import { useModalContext } from '@/Contexts/ModalContext';
import { ActionsRendererProps } from '@/types/ag-grid';
import { PageProps } from '@/types/general';
import { UserInviteStatuses } from '@/types/user-invite-data';
import Modal from '@/UI-Kit/Modal';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLinkItem,
} from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess } from '@/utils/auth';

const UserInviteRowActions = ({ data }: ActionsRendererProps) => {
    const route = useRoute();
    const { auth, userInviteStatuses } = usePage<PageProps<{ userInviteStatuses: UserInviteStatuses }>>().props;
    const { setShowModal, setModalData, setModalComponent } = useModalContext();
    const hasActions =
        (canAccess(['editUserInvite'], auth.can) && data.status === userInviteStatuses.invited) ||
        (canAccess(['cancelUserInvite'], auth.can) &&
            (data.status === userInviteStatuses.invited || data.status === userInviteStatuses.expired)) ||
        (canAccess(['deleteUserInvite'], auth.can) &&
            (data.status === userInviteStatuses.invited ||
                data.status === userInviteStatuses.expired ||
                data.status === userInviteStatuses.cancelled));

    return (
        <>
            {hasActions && (
                <DropdownMenu modal={false}>
                    <RowActionsDropdownTrigger />

                    <DropdownMenuContent align="end">
                        {canAccess(['editUserInvite'], auth.can) && data.status === userInviteStatuses.invited && (
                            <DropdownMenuLinkItem
                                key="edit-user-invite"
                                data-testid="edit-user-invite-button"
                                href={route('admin.user-invites.edit', data.uuid)}
                            >
                                <Pencil size={16} /> Edit User Invite
                            </DropdownMenuLinkItem>
                        )}

                        {canAccess(['cancelUserInvite'], auth.can) &&
                            (data.status === userInviteStatuses.invited ||
                                data.status === userInviteStatuses.expired) && (
                                <Modal.Trigger key="cancel-user-invite">
                                    <DropdownMenuItem
                                        key="cancel-user-invite"
                                        data-testid="cancel-user-invite-button"
                                        onClick={() => {
                                            setModalData(data);
                                            setModalComponent(
                                                <ModalContentWrapper>
                                                    <ConfirmDialog
                                                        confirmData={data}
                                                        color="amber"
                                                        content="Are you sure you want to cancel this user invite?"
                                                        dialogType="confirm"
                                                        endpoint={route('admin.user-invites.cancel', {
                                                            userInvite: data.uuid,
                                                        })}
                                                        iconProps={{ name: 'Ban' }}
                                                        okText="Cancel User Invite"
                                                        title="Cancel User Invite"
                                                    />
                                                </ModalContentWrapper>
                                            );
                                            setShowModal(true);
                                        }}
                                    >
                                        <Ban className="h-4 w-4!" /> Cancel User Invite
                                    </DropdownMenuItem>
                                </Modal.Trigger>
                            )}

                        {canAccess(['deleteUserInvite'], auth.can) &&
                            (data.status === userInviteStatuses.invited ||
                                data.status === userInviteStatuses.expired ||
                                data.status === userInviteStatuses.cancelled) && (
                                <Modal.Trigger>
                                    <DropdownMenuItem
                                        data-testid="delete-user-invite-button"
                                        key="delete-user-invite"
                                        onClick={() => {
                                            setModalData(data);
                                            setModalComponent(
                                                <ModalContentWrapper>
                                                    <ConfirmDialog
                                                        confirmData={data}
                                                        color="red"
                                                        content="Are you sure you want to delete this user invite?"
                                                        dialogType="delete"
                                                        endpoint={route('admin.user-invites.destroy', {
                                                            userInvite: data.uuid,
                                                        })}
                                                        iconProps={{ name: 'Trash2' }}
                                                        okText="Delete User Invite"
                                                        title="Delete User Invite"
                                                    />
                                                </ModalContentWrapper>
                                            );
                                            setShowModal(true);
                                        }}
                                    >
                                        <Trash2 className="h-4 w-4!" /> Delete User Invite
                                    </DropdownMenuItem>
                                </Modal.Trigger>
                            )}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

export default UserInviteRowActions;
