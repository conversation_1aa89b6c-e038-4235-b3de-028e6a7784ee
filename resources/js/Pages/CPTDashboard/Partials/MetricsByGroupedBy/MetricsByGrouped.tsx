import { useState } from 'react';

import { usePage } from '@inertiajs/react';

import { useSelectedDatesContext } from '@/Contexts/SelectedDatesContext';
import useMetricsState from '@/hooks/useMetricState';
import MetricsByGroupedList from '@/Pages/CPTDashboard/Partials/MetricsByGroupedBy/MetricsByGroupedList';
import MetricsByGroupedTotals from '@/Pages/CPTDashboard/Partials/MetricsByGroupedBy/MetricsByGroupedTotals';
import MetricSelect from '@/Pages/CPTDashboard/Partials/MetricSelect';
import { PageProps } from '@/types/general';
import { IMetricStatData } from '@/types/metrics';
import { Card, CardAction, CardContent, CardHeader, CardTitle } from '@/UI-Kit/Shadcn/card';

const MetricsByGrouped = ({ className }: { className: string }) => {
    const { queryRequest } = usePage<PageProps>().props;
    const [moduleName, setModuleName] = useState('metricsByAdPlatform');

    const { selectedDate } = useSelectedDatesContext();
    const { metricsState, setMetricsState } = useMetricsState({
        metricsDataProp: moduleName,
        queryRequest,
        moduleName,
    });
    const { isLoading, selectedMetric } = metricsState;

    return (
        <Card className={className}>
            <CardHeader border="none">
                <CardTitle>
                    {!isLoading && (
                        <MetricSelect
                            metricsState={metricsState}
                            setMetricsState={setMetricsState}
                            moduleName={moduleName}
                            prependToName="Stats"
                        />
                    )}
                </CardTitle>
                <CardAction>
                    <div key="metric-by-source-date" className="stat-title text-xs">
                        {selectedDate}
                    </div>
                </CardAction>
            </CardHeader>
            <CardContent isLoading={isLoading} data-testid={!isLoading ? 'metrics-by-grouped-by' : undefined}>
                {!isLoading && <MetricsByGroupedTotals selectedMetric={selectedMetric as IMetricStatData} />}
                {!isLoading && (
                    <MetricsByGroupedList
                        moduleName={moduleName}
                        setModuleName={setModuleName}
                        selectedMetric={selectedMetric as IMetricStatData}
                    />
                )}
            </CardContent>
        </Card>
    );
};

export default MetricsByGrouped;
