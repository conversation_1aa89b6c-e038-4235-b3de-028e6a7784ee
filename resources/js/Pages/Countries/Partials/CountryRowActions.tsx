import { Pencil, Trash2 } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { router, usePage } from '@inertiajs/react';

import RowActionsDropdownTrigger from '@/Components/CommonButtons/RowActionsDropdownTrigger';
import ConfirmDialog from '@/Components/Modals/ConfirmDialog';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import { useModalContext } from '@/Contexts/ModalContext';
import { ActionsRendererProps } from '@/types/ag-grid';
import { PageProps } from '@/types/general';
import Modal from '@/UI-Kit/Modal';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem } from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess } from '@/utils/auth';

const CountryRowActions = ({ data }: ActionsRendererProps) => {
    const { auth } = usePage<PageProps>().props;
    const { setShowModal, setModalData, setModalComponent } = useModalContext();
    const hasActions = canAccess(['editCountry', 'deleteCountry'], auth.can);
    const route = useRoute();

    return (
        <>
            {hasActions && (
                <DropdownMenu modal={false}>
                    <RowActionsDropdownTrigger />

                    <DropdownMenuContent align="end">
                        {canAccess(['editCountry'], auth.can) && (
                            <DropdownMenuItem
                                data-testid="edit-country-button"
                                key="edit-country"
                                onClick={() => router.visit(route('admin.countries.edit', data.uuid))}
                            >
                                <Pencil size={16} /> Edit Country
                            </DropdownMenuItem>
                        )}

                        {canAccess(['deleteCountry'], auth.can) && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="delete-country-button"
                                    key="delete-country"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    confirmData={data}
                                                    color="red"
                                                    content="Are you sure you want to delete this country?"
                                                    dialogType="delete"
                                                    endpoint={route('admin.countries.destroy', { country: data.uuid })}
                                                    iconProps={{ name: 'Trash2' }}
                                                    okText="Delete Country"
                                                    title="Delete Country"
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Trash2 className="h-4 w-4!" /> Delete Country
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

export default CountryRowActions;
