import { Pencil, Trash2 } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { usePage } from '@inertiajs/react';

import RowActionsDropdownTrigger from '@/Components/CommonButtons/RowActionsDropdownTrigger';
import ConfirmDialog from '@/Components/Modals/ConfirmDialog';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import { useModalContext } from '@/Contexts/ModalContext';
import EditStudyStatusForm from '@/Pages/StudyStatuses/Partials/Form/EditStudyStatusForm';
import { ActionsRendererProps } from '@/types/ag-grid';
import { PageProps } from '@/types/general';
import Modal from '@/UI-Kit/Modal';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem } from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess } from '@/utils/auth';

const StudyStatusRowActions = ({ data }: ActionsRendererProps) => {
    const route = useRoute();
    const { auth } = usePage<PageProps>().props;
    const { setShowModal, setModalData, setModalComponent } = useModalContext();
    const canDelete =
        canAccess(['deleteStudyStatus'], auth.can) &&
        data.study_count === 0 &&
        !data.is_archive &&
        !data.is_system &&
        data.can_delete;

    const canEdit =
        canAccess(['editSystemStudyStatus'], auth.can) ||
        (!data.is_archive && !data.is_system && canAccess(['editStudyStatus'], auth.can));

    const hasActions = canEdit || canDelete;

    return (
        <>
            {hasActions && (
                <DropdownMenu modal={false}>
                    <RowActionsDropdownTrigger />

                    <DropdownMenuContent align="end">
                        {canEdit && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="edit-study-status"
                                    key="edit-study-status"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper title="Edit Study Status">
                                                <EditStudyStatusForm />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Pencil size={16} />
                                    Edit Study Status
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}

                        {canDelete && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="delete-study-status-button"
                                    key="delete-study-status"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    confirmData={data}
                                                    color="red"
                                                    content="Are you sure you want to delete this study status?"
                                                    dialogType="delete"
                                                    endpoint={route('admin.study-statuses.destroy', {
                                                        study_status: data.uuid,
                                                    })}
                                                    iconProps={{ name: 'Trash2' }}
                                                    okText="Delete Study Status"
                                                    title="Delete Study Status"
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Trash2 className="h-4 w-4!" /> Delete Study Status
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

export default StudyStatusRowActions;
