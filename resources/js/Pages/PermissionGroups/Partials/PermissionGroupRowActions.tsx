import { Pencil, Trash2 } from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { usePage } from '@inertiajs/react';

import RowActionsDropdownTrigger from '@/Components/CommonButtons/RowActionsDropdownTrigger';
import ConfirmDialog from '@/Components/Modals/ConfirmDialog';
import ModalContentWrapper from '@/Components/Modals/ModalContentWrapper';
import { useModalContext } from '@/Contexts/ModalContext';
import EditPermissionGroupForm from '@/Pages/PermissionGroups/Partials/Form/EditPermissionGroupForm';
import { ActionsRendererProps } from '@/types/ag-grid';
import { PageProps } from '@/types/general';
import Modal from '@/UI-Kit/Modal';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem } from '@/UI-Kit/Shadcn/dropdown-menu';
import { canAccess } from '@/utils/auth';

const PermissionGroupRowActions = ({ data }: ActionsRendererProps) => {
    const route = useRoute();
    const { auth } = usePage<PageProps>().props;
    const { setShowModal, setModalData, setModalComponent } = useModalContext();
    const hasActions = canAccess(['editPermissionGroup', 'deletePermissionGroup'], auth.can);

    return (
        <>
            {hasActions && (
                <DropdownMenu modal={false}>
                    <RowActionsDropdownTrigger />

                    <DropdownMenuContent align="end">
                        {canAccess(['editPermissionGroup'], auth.can) && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="edit-permission-group"
                                    key="edit-permission-group"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper title="Edit Permission Group">
                                                <EditPermissionGroupForm />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Pencil size={16} />
                                    Edit Permission Group
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}

                        {canAccess(['deletePermissionGroup'], auth.can) && (
                            <Modal.Trigger>
                                <DropdownMenuItem
                                    data-testid="delete-user-button"
                                    key="delete-permission-group"
                                    onClick={() => {
                                        setModalData(data);
                                        setModalComponent(
                                            <ModalContentWrapper>
                                                <ConfirmDialog
                                                    confirmData={data}
                                                    color="red"
                                                    content="Are you sure you want to delete this permission group?"
                                                    dialogType="delete"
                                                    endpoint={route('admin.permission-groups.destroy', {
                                                        permissionGroup: data.uuid,
                                                    })}
                                                    iconProps={{ name: 'Trash2' }}
                                                    okText="Delete Permission Group"
                                                    title="Delete Permission Group"
                                                />
                                            </ModalContentWrapper>
                                        );
                                        setShowModal(true);
                                    }}
                                >
                                    <Trash2 className="h-4 w-4!" /> Delete Permission Group
                                </DropdownMenuItem>
                            </Modal.Trigger>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </>
    );
};

export default PermissionGroupRowActions;
