import queryString from 'query-string';

import iframeResizer from '@iframe-resizer/parent';


(function embedCalendar(): void {
    type SearchParamsProps = Record<string, string>;
    const script = document.getElementById('1ndata-embed-calendar');
    const srcUrl = script?.getAttribute('src')?.replace('/embed-calendar.js', '');

    const windowURL = new URL(window.location.href);
    const searchParams = queryString.parse(windowURL.search) as SearchParamsProps;
    const questionUrl = srcUrl ?? import.meta.env.VITE_APP_URL;
    const iframeUrl = new URL(<string>questionUrl);
    const iframe = document.createElement('iframe');

    iframeUrl.pathname = `/embed-calendar/${searchParams?.referral}`;
    iframe.src = iframeUrl.href;
    iframe.style.border = '0';
    iframe.style.minWidth = '100%';
    iframe.style.overflow = 'hidden';
    iframe.style.width = '1px';
    iframe.style.height = '100%';
    iframeResizer({ log: false, license: 'GPLv3' }, iframe);

    if (script?.parentNode) {
        script.parentNode.replaceChild(iframe, script);
    }
})();
