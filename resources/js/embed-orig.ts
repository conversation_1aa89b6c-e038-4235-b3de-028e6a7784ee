import forEach from 'lodash/forEach';
import isEmpty from 'lodash/isEmpty';
import queryString from 'query-string';

import iframeResizer from '@iframe-resizer/parent';

const acceptedQueryParams = ['utm_campaign', 'utm_content', 'utm_medium', 'utm_source', 'utm_term', 'lang', 'test'];

(function embedQuestionnaireForm(): void {
    type SearchParamsProps = Record<string, string>;
    const script = document.getElementById('1ndata-embed');
    const formId = script?.getAttribute('data-form-id');
    const srcUrl = script?.getAttribute('src')?.replace('/embed.js', '');
    const windowURL = new URL(window.location.href);
    const searchParams = queryString.parse(windowURL.search) as SearchParamsProps;
    const questionUrl = srcUrl ?? import.meta.env.VITE_APP_URL;
    const iframeUrl = new URL(<string>questionUrl);

    if (formId) {
        iframeUrl.pathname = `/embed/${formId}`;
    }

    if (!isEmpty(searchParams)) {
        forEach(searchParams, (value, key) => {
            if (acceptedQueryParams.includes(key)) {
                iframeUrl.searchParams.append(key, value);
            }
        });
    }

    const iframe = document.createElement('iframe');
    iframe.src = iframeUrl.href;
    iframe.style.border = '0';
    iframe.style.minWidth = '100%';
    iframe.style.overflow = 'hidden';
    iframe.style.width = '1px';
    iframe.style.height = '100%';
    iframeResizer({ log: false, license: 'GPLv3' }, iframe);

    if (script?.parentNode) {
        script.parentNode.replaceChild(iframe, script);
    }
})();
