import { Dispatch, ReactNode, SetStateAction } from 'react';

import {
    Bug,
    CircleSlash,
    Feather,
    FileText,
    LifeBuoy,
    LogOut,
    Menu,
    PanelLeftClose,
    PanelLeftOpen,
    ScrollText,
    UserPen,
} from 'lucide-react';
import { useRoute } from 'ziggy-js';

import { router, usePage } from '@inertiajs/react';

import DropdownChevronIcon from '@/Components/CommonButtons/DropdownChevronIcon';
import { FULLSTORY_MASK_CLASS } from '@/constants/fullstory';
import useSaveSidebarState from '@/hooks/AgGrid/useSaveSidebarState';
import useStickyShadow from '@/hooks/useStickyShadow';
import { cn } from '@/lib/utils';
import { useStoreState } from '@/store/hooks';
import { PageProps, Tenant } from '@/types/general';
import { Button } from '@/UI-Kit/Shadcn/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuLinkItem,
    DropdownMenuPortal,
    DropdownMenuSeparator,
    DropdownMenuSub,
    DropdownMenuSubContent,
    DropdownMenuSubTrigger,
    DropdownMenuTrigger,
} from '@/UI-Kit/Shadcn/dropdown-menu';
import { Separator } from '@/UI-Kit/Shadcn/separator';

type Props = {
    breadcrumbs?: ReactNode;
    setMobileSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;
    topBarShadow?: boolean;
    mobileSidebarOpen: boolean;
};

const TopBar = ({ breadcrumbs, setMobileSidebarOpen, topBarShadow, mobileSidebarOpen }: Props) => {
    const { auth } = usePage<PageProps>().props;
    const { showShadow } = useStickyShadow();

    return (
        <div
            id="top-bar"
            className={cn(
                'sticky top-0 z-21 flex h-12 shrink-0 items-center gap-x-4 border-b border-gray-100 bg-white px-4',
                'sm:gap-x-6 sm:px-6 lg:gap-x-3 lg:pr-8 lg:pl-4',
                'transition-opacity ease-in-out',
                {
                    'shadow-xl shadow-black/10': topBarShadow && showShadow,
                    'pointer-events-none opacity-0 [transition-duration:100ms]': mobileSidebarOpen,
                    'pointer-events-auto opacity-100 [transition-duration:800ms]': !mobileSidebarOpen,
                }
            )}
        >
            <MobileMenuButton setMobileSidebarOpen={setMobileSidebarOpen} />
            <DesktopMenuButton />

            {/* Separator */}
            <div className="h-6 w-px bg-black/10" aria-hidden="true" />

            <div className="flex flex-1 gap-x-3 self-stretch lg:gap-x-4">
                <div className="relative flex flex-1 items-center">{breadcrumbs}</div>

                <div className="flex items-center">
                    <SwitchTenants />
                </div>

                {auth.isImpersonating && (
                    <>
                        <Separator orientation="vertical" className="my-auto h-2/3" />

                        <div className="text-red flex items-center bg-red-100 px-1 text-sm font-bold">
                            {`You are impersonating this user --->`}
                        </div>
                    </>
                )}

                <Separator orientation="vertical" className="my-auto h-2/3" />

                <div className="flex items-center">
                    <ProfileMenu />
                </div>
            </div>
        </div>
    );
};

export default TopBar;

const MobileMenuButton = ({ setMobileSidebarOpen }: { setMobileSidebarOpen: Dispatch<SetStateAction<boolean>> }) => (
    <button type="button" className="-m-2.5 p-2.5 text-gray-700 lg:hidden" onClick={() => setMobileSidebarOpen(true)}>
        <span className="sr-only">Open sidebar</span>

        <Menu className="h-6 w-6" aria-hidden="true" />
    </button>
);

const DesktopMenuButton = () => {
    const collapsedSidebar = useStoreState(state => state.appSettingsModel.collapsedSidebar);
    const { saveSidebarState } = useSaveSidebarState();

    return (
        <button type="button" className="-m-2 hidden p-2 text-gray-500 lg:block" onClick={() => saveSidebarState()}>
            <span className="sr-only">Open sidebar</span>

            {collapsedSidebar ? (
                <PanelLeftOpen className="h-5 w-5" aria-hidden="true" />
            ) : (
                <PanelLeftClose className="h-5 w-5" aria-hidden="true" />
            )}
        </button>
    );
};

const ProfileMenu = () => {
    const route = useRoute();
    const { auth } = usePage<PageProps>().props;

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button type="button" variant="transparent" data-ondata="profile-menu-trigger">
                    <span className="flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-green-300 text-[0.625rem] leading-4 font-semibold text-black group-hover:text-white">
                        {auth.user.initials}
                    </span>
                    <span className="hidden lg:flex lg:items-center">
                        <span
                            data-ondata="profile-menu-name"
                            className={cn('text-sm leading-6 font-medium text-black', FULLSTORY_MASK_CLASS)}
                            aria-hidden="true"
                        >
                            {auth.user.name}
                        </span>
                        <DropdownChevronIcon />
                    </span>
                </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>

                <DropdownMenuSeparator />

                {!auth.isImpersonating && (
                    <DropdownMenuLinkItem href={route('profile.edit')}>
                        <UserPen className="h-4 w-4!" /> My Profile
                    </DropdownMenuLinkItem>
                )}

                <Support />

                {auth.isImpersonating && (
                    <DropdownMenuLinkItem
                        href={route('admin.user-impersonation.destroy')}
                        inertiaLinkProps={{ method: 'delete' }}
                    >
                        <CircleSlash className="h-4 w-4!" /> Stop Impersonating
                    </DropdownMenuLinkItem>
                )}

                {!auth.isImpersonating && (
                    <>
                        <DropdownMenuSeparator />

                        <DropdownMenuLinkItem
                            href={route('logout')}
                            inertiaLinkProps={{ method: 'post', as: 'button', className: 'w-full' }}
                        >
                            <LogOut className="h-4 w-4!" /> Log Out
                        </DropdownMenuLinkItem>
                    </>
                )}
            </DropdownMenuContent>
        </DropdownMenu>
    );
};

const SwitchTenants = () => {
    const route = useRoute();
    const { userTenants, currentTenant } = usePage<PageProps>().props;
    const userTenantsExceptCurrent = userTenants.filter((tenant: Tenant) => tenant.uuid !== currentTenant.uuid);

    if (userTenantsExceptCurrent.length === 0) {
        return <span data-ondata="switch-tenant-button">{currentTenant.name ?? 'Central App'}</span>;
    }

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button type="button" variant="transparent" data-ondata="switch-tenant-button">
                    {currentTenant.name ?? 'Central App'} <DropdownChevronIcon />
                </Button>
            </DropdownMenuTrigger>

            <DropdownMenuContent className="w-44" align="end">
                <DropdownMenuLabel>
                    <p className="truncate" role="none">
                        <span className="block text-xs text-gray-500" role="none">
                            currently using
                        </span>

                        {currentTenant.name ?? 'Central App'}
                    </p>
                </DropdownMenuLabel>

                <DropdownMenuSeparator />

                {userTenantsExceptCurrent.map((tenant: Tenant) => {
                    const duskSelector = `switch-tenant-${tenant.uuid.replace(/-/g, '')}`;

                    return (
                        <DropdownMenuItem
                            key={tenant.name}
                            data-dusk={duskSelector}
                            onClick={() => router.post(route('switch-tenants.index', tenant.uuid), {})}
                        >
                            {tenant.name}
                        </DropdownMenuItem>
                    );
                })}
            </DropdownMenuContent>
        </DropdownMenu>
    );
};

const Support = () => {
    const { docPortalHome } = usePage<{ docPortalHome: string }>().props;
    const route = useRoute();

    return (
        <DropdownMenuSub>
            <DropdownMenuSubTrigger>
                <LifeBuoy className="h-4 w-4!" />
                Support
            </DropdownMenuSubTrigger>

            <DropdownMenuPortal>
                <DropdownMenuSubContent>
                    <DropdownMenuLinkItem href={route('support.fix-it')}>
                        <Bug className="h-4 w-4!" />
                        Report an Issue
                    </DropdownMenuLinkItem>

                    <DropdownMenuLinkItem href={route('support.feature-request')}>
                        <Feather className="h-4 w-4!" />
                        Request a Feature
                    </DropdownMenuLinkItem>

                    <DropdownMenuLinkItem href={route('support.release-notes')}>
                        <ScrollText className="h-4 w-4!" />
                        Release Notes
                    </DropdownMenuLinkItem>

                    <DropdownMenuLinkItem href={docPortalHome} isExternalLink anchorProps={{ target: '_blank' }}>
                        <FileText className="h-4 w-4!" />
                        Help Articles
                    </DropdownMenuLinkItem>
                </DropdownMenuSubContent>
            </DropdownMenuPortal>
        </DropdownMenuSub>
    );
};
