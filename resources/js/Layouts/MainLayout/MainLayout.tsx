import { PropsWithChildren, ReactNode, useEffect, useState } from 'react';
import { useIdleTimer } from 'react-idle-timer';

import Cookies from 'js-cookie';
import { useRoute } from 'ziggy-js';

import { Link, router, usePage } from '@inertiajs/react';

import ConditionalWrapper from '@/Components/ConditionalWrapper';
import { standardApiNotification } from '@/Components/Notifications';
import SpinnerWithDelay from '@/Components/SpinnerWithDelay';
import useUserReceivedNotification from '@/hooks/PusherListeners/useUserReceivedNotification';
import DesktopSidebar from '@/Layouts/MainLayout/Partials/DesktopSidebar';
import Header from '@/Layouts/MainLayout/Partials/Header';
import MainContent from '@/Layouts/MainLayout/Partials/MainContent';
import MobileSidebar from '@/Layouts/MainLayout/Partials/MobileSidebar';
import TopBar from '@/Layouts/MainLayout/Partials/TopBar';
import Footer from '@/Layouts/Partials/Footer';
import { cn } from '@/lib/utils';
import { useStoreState } from '@/store/hooks';
import { PageProps } from '@/types/general';
import AlertBanner from '@/UI-Kit/Alert/AlertBanner';

const MainLayout = ({
    breadcrumbs,
    children,
    header,
    headerActions,
    headerStats,
    stickyHeader = true,
    topBarShadow = false,
    useMainContentWrapper = true,
}: PropsWithChildren<{
    breadcrumbs?: ReactNode;
    header?: ReactNode;
    headerActions?: ReactNode;
    headerStats?: ReactNode;
    stickyHeader?: boolean;
    topBarShadow?: boolean;
    useMainContentWrapper?: boolean;
}>) => {
    const route = useRoute();
    const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);
    const collapsedSidebar = useStoreState(state => state.appSettingsModel.collapsedSidebar);
    const getMainContent = (contentChildren: ReactNode) => <MainContent>{contentChildren}</MainContent>;
    const { flash, auth, globalNotifications } = usePage<PageProps>().props;

    useUserReceivedNotification();

    useIdleTimer({
        onIdle: () => {
            const usingDocumentPortal = Cookies.get('usingDocumentPortal');

            if (!usingDocumentPortal) {
                router.post(route('logout'), {
                    inactivity: true,
                });
            }
        },
        timeout: 60 * 60 * 1000,
        crossTab: true,
        leaderElection: true,
        syncTimers: 200,
    });

    useEffect(() => {
        if (flash.message || (flash.message && 'original' in flash.message && (flash.message as any).original)) {
            const messageData = 'original' in flash.message ? flash.message.original : flash.message;

            standardApiNotification(
                messageData.success,
                messageData.message,
                messageData.description,
                messageData.duration
            );
        }
    }, [flash.message]);

    return (
        <>
            <SpinnerWithDelay />
            <div id="main-template" className="main-app relative">
                <MobileSidebar mobileSidebarOpen={mobileSidebarOpen} setMobileSidebarOpen={setMobileSidebarOpen} />
                <DesktopSidebar />
                <div
                    className={cn('min-h-screen flex-col', {
                        'lg:pl-64': !collapsedSidebar,
                        'lg:pl-20': collapsedSidebar,
                    })}
                >
                    {globalNotifications.map(notification => (
                        <AlertBanner
                            key={notification.uuid}
                            type={notification.type}
                            description={notification.message}
                            isDismissible={notification.is_dismissible}
                            globalNotification={notification}
                        />
                    ))}

                    {!auth.user.twoFactorEnabled && (
                        <AlertBanner
                            type="warning"
                            description={
                                <span className="text-md text-black">
                                    To improve security, 2FA is now required for all 1nData users.
                                    <Link
                                        href={`${route('profile.edit')}#twoFactorSetup`}
                                        className="text-primary ml-1 flex-1 rounded-md hover:text-emerald-600 hover:underline focus:outline-none"
                                    >
                                        Please enable 2FA here.
                                    </Link>
                                </span>
                            }
                        />
                    )}
                    <TopBar
                        setMobileSidebarOpen={setMobileSidebarOpen}
                        mobileSidebarOpen={mobileSidebarOpen}
                        breadcrumbs={breadcrumbs}
                        topBarShadow={topBarShadow}
                    />
                    <Header
                        header={header}
                        headerActions={headerActions}
                        headerStats={headerStats}
                        stickyHeader={stickyHeader}
                    />
                    <ConditionalWrapper
                        condition={useMainContentWrapper}
                        wrapper={(contentChildren: ReactNode) => getMainContent(contentChildren)}
                    >
                        {children}
                    </ConditionalWrapper>

                    <Footer />
                </div>
            </div>
        </>
    );
};

export default MainLayout;
