<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class='h-full'>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title inertia>{{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
            href="https://fonts.googleapis.com/css2?family=Manrope:wght@200;400;500;600;700;800&display=swap"
            rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        black: '#1B2222',
                        green: '#53CA97',
                    },
                    fontFamily: {
                        sans: ['Manrope'],
                        body: ['Manrope'],
                    },
                },
            },
        };
    </script>
</head>
<body class="font-sans antialiased h-full">
<div class="grid min-h-full grid-cols-1 grid-rows-[1fr_auto_1fr] bg-white lg:grid-cols-[max(50%,36rem)_1fr]">
    <div class="hidden lg:relative lg:col-start-1 lg:row-end-1 lg:row-start-4 lg:block bg-black">
        <div class='h-full flex items-center'>
            <img src="{{ asset('images/space_graphic.png') }}" alt="1nData Logo" class="w-1/2 m-auto xl:w-1/3">
        </div>
    </div>
    <header class="mx-auto w-full max-w-7xl px-6 pt-6 sm:pt-10 lg:col-span-2 lg:col-start-2 lg:row-start-1 lg:px-20">
        <a href="{{ route('login') }}">
            <span class="sr-only">1nData</span>
            <img class="h-8 w-auto sm:h-10" src="{{ asset('images/1ndata-logo-dark.png') }}"
                 alt="1nData Logo">
        </a>
    </header>
    <main class="mx-auto w-full max-w-7xl px-6 py-24 sm:py-32 lg:col-span-2 lg:col-start-2 lg:row-start-2 lg:px-20">
        <div class="max-w-lg">
            <p class="text-lg font-semibold leading-8 text-gray-400">503 | Maintenance</p>
            <h1 class="mt-4 text-3xl font-bold tracking-tight text-black sm:text-5xl">Be back <span
                        class='text-green'>soon</span></h1>
            <p class="mt-6 text-base leading-7 text-gray-600">The server is currently undergoing maintenance. We’re
                sorry for the inconvenience, we’ll be back as soon as possible.</p>
        </div>
    </main>

</div>
</body>
</html>
