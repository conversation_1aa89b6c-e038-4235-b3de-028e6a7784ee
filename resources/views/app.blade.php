<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    @env('staging')
        <meta name="robots" content="noindex, nofollow">
    @endenv

    <title inertia>{{ $page['props']['currentTenantName'] ?? config('app.name', 'Laravel') }}</title>
    <meta name="description" content="1nData delivers real-time metrics on site performance and patient activity.">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet"/>
    <link rel="preconnect" href="https://fonts.googleapis.com"/>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin/>
    {{--<link
        href="https://fonts.googleapis.com/css2?family=Manrope:wght@200;400;500;600;700;800&display=swap"
        rel="stylesheet"
    />--}}
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />


    <!-- Scripts -->
    @routes
    @viteReactRefresh
    @vite(['resources/js/app.tsx', "resources/js/Pages/{$page['component']}.tsx"])
    @inertiaHead

    <script type="text/javascript">
        window.AppcuesSettings = {
            enableURLDetection: true,
        };
    </script>
    <script src="//fast.appcues.com/211735.js"></script>

    @if (config('services.fullstory.enabled'))
        <script>
            window['_fs_host'] = 'fullstory.com';
            window['_fs_script'] = 'edge.fullstory.com/s/fs.js';
            window['_fs_org'] = 'o-2363B1-na1';
            window['_fs_namespace'] = 'FS';
            !function (m, n, e, t, l, o, g, y) {
                var s, f, a = function (h) {
                        return !(h in m) || (m.console && m.console.log && m.console.log('FullStory namespace conflict. Please set window["_fs_namespace"].'), !1)
                    }(e)
                ;

                function p(b) {
                    var h, d = [];

                    function j() {
                        h && (d.forEach((function (b) {
                            var d;
                            try {
                                d = b[h[0]] && b[h[0]](h[1])
                            } catch (h) {
                                return void (b[3] && b[3](h))
                            }
                            d && d.then ? d.then(b[2], b[3]) : b[2] && b[2](d)
                        })), d.length = 0)
                    }

                    function r(b) {
                        return function (d) {
                            h || (h = [b, d], j())
                        }
                    }

                    return b(r(0), r(1)), {
                        then: function (b, h) {
                            return p((function (r, i) {
                                d.push([b, h, r, i]), j()
                            }))
                        }
                    }
                }

                a && (g = m[e] = function () {
                    var b = function (b, d, j, r) {
                            function i(i, c) {
                                h(b, d, j, i, c, r)
                            }

                            r = r || 2;
                            var c, u = /Async$/;
                            return u.test(b) ? (b = b.replace(u, ""), "function" == typeof Promise ? new Promise(i) : p(i)) : h(b, d, j, c, c, r)
                        }
                    ;

                    function h(h, d, j, r, i, c) {
                        return b._api ? b._api(h, d, j, r, i, c) : (b.q && b.q.push([h, d, j, r, i, c]), null)
                    }

                    return b.q = [], b
                }(), y = function (b) {
                    function h(h) {
                        "function" == typeof h[4] && h[4](new Error(b))
                    }

                    var d = g.q;
                    if (d) {
                        for (var j = 0; j < d.length; j++) h(d[j]);
                        d.length = 0, d.push = h
                    }
                }, function () {
                    (o = n.createElement(t)).async = !0, o.crossOrigin = "anonymous", o.src = "https://" + l, o.onerror = function () {
                        y("Error loading " + l)
                    }
                    ;var b = n.getElementsByTagName(t)[0];
                    b && b.parentNode ? b.parentNode.insertBefore(o, b) : n.head.appendChild(o)
                }(), function () {
                    function b() {
                    }

                    function h(b, h, d) {
                        g(b, h, d, 1)
                    }

                    function d(b, d, j) {
                        h("setProperties", {type: b, properties: d}, j)
                    }

                    function j(b, h) {
                        d("user", b, h)
                    }

                    function r(b, h, d) {
                        j({
                            uid: b
                        }, d), h && j(h, d)
                    }

                    g.identify = r, g.setUserVars = j, g.identifyAccount = b, g.clearUserCookie = b, g.setVars = d, g.event = function (b, d, j) {
                        h("trackEvent", {
                            name: b, properties: d
                        }, j)
                    }, g.anonymize = function () {
                        r(!1)
                    }, g.shutdown = function () {
                        h("shutdown")
                    }, g.restart = function () {
                        h("restart")
                    },
                        g.log = function (b, d) {
                            h("log", {level: b, msg: d})
                        }, g.consent = function (b) {
                        h("setIdentity", {consent: !arguments.length || b})
                    }
                }(), s = "fetch",
                    f = "XMLHttpRequest", g._w = {}, g._w[f] = m[f], g._w[s] = m[s], m[s] && (m[s] = function () {
                    return g._w[s].apply(this, arguments)
                }), g._v = "2.0.0")
            }(window, document, window._fs_namespace, "script", window._fs_script);
        </script>
    @endif
</head>

<body class="font-sans antialiased">
@inertia
</body>

</html>
